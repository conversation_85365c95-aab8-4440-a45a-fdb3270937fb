import type { FC } from 'react';
import { memo, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiEditLine } from '@remixicon/react';
// RiThumbUpLine,,RiThumbDownLine
import type { ChatItem } from '../../types';
import { useChatContext } from '../context';
import copy from 'copy-to-clipboard';
import Toast from '@/app/components/base/toast';
import EditReplyModal from '@/app/components/app/annotation/edit-annotation-modal';
import Log from '@/app/components/base/chat/chat/log';
import ActionButton, { ActionButtonState } from '@/app/components/base/action-button';
import NewAudioButton from '@/app/components/base/new-audio-button';
import cn from '@/utils/classnames';
import { CopyOne, ReloadIcon } from '@/app/components/base/icons/src/public/apps';
import type { SVGProps } from 'react';

const RiThumbUpLineIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path
        d="M12.632 16.6363H3.18111C2.37747 16.6363 1.72656 15.9854 1.72656 15.1817V7.9781C1.72656 7.44355 2.01747 6.95628 2.48656 6.70173L4.90838 5.38173C5.57384 5.0181 6.11565 4.47991 6.48293 3.8181L7.4502 2.07991C7.69384 1.63991 8.15929 1.36719 8.66111 1.36719H8.74838C9.34111 1.36719 9.89747 1.59628 10.3157 2.01446C10.7411 2.43991 11.0066 3.00719 11.0538 3.60719L11.0866 4.00719C11.1557 4.82173 11.0975 5.63628 10.912 6.43628L10.8247 6.8181H14.5847C15.0429 6.8181 15.4684 7.02901 15.7447 7.39628C16.0211 7.76355 16.1084 8.22537 15.9847 8.66901L14.032 15.5781C13.8575 16.1999 13.2793 16.6363 12.632 16.6363ZM8.70111 2.8181L7.75565 4.52355C7.25384 5.42537 6.51202 6.16355 5.60293 6.6581L3.18111 7.9781V15.1817H12.632L14.5847 8.27264H10.4102C10.0647 8.27264 9.74474 8.11628 9.5302 7.84719C9.31565 7.5781 9.23565 7.22901 9.31565 6.89446L9.49747 6.10901C9.64656 5.4581 9.69384 4.79264 9.63929 4.13082L9.60656 3.73082C9.58474 3.47264 9.47202 3.22901 9.2902 3.04719C9.14838 2.90537 8.95202 2.82537 8.75202 2.82537H8.70474L8.70111 2.8181Z"
        fill="currentColor"
      />
    </svg>
  );
};

const RiThumbDownLineIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path
        d="M12.632 1.36763H3.18111C2.37747 1.36763 1.72656 2.01854 1.72656 2.82217V10.0258C1.72656 10.5604 2.01747 11.0476 2.48656 11.3022L4.90838 12.6222C5.57384 12.9858 6.11565 13.524 6.48293 14.1858L7.4502 15.924C7.69384 16.364 8.15929 16.6367 8.66111 16.6367H8.74838C9.34111 16.6367 9.89747 16.4076 10.3157 15.9894C10.7411 15.564 11.0066 14.9967 11.0538 14.3967L11.0866 13.9967C11.1557 13.1822 11.0975 12.3676 10.912 11.5676L10.8247 11.1858H14.5847C15.0429 11.1858 15.4684 10.9749 15.7447 10.6076C16.0211 10.2404 16.1084 9.77854 15.9847 9.3349L14.032 2.42581C13.8575 1.80399 13.2793 1.36763 12.632 1.36763ZM8.70111 15.1858L7.75565 13.4804C7.25384 12.5785 6.51202 11.8404 5.60293 11.3458L3.18111 10.0258V2.82217H12.632L14.5847 9.73126H10.4102C10.0647 9.73126 9.74474 9.88763 9.5302 10.1567C9.31565 10.4258 9.23565 10.7749 9.31565 11.1094L9.49747 11.8949C9.64656 12.5458 9.69384 13.2113 9.63929 13.8731L9.60656 14.2731C9.58474 14.5313 9.47202 14.7749 9.2902 14.9567C9.14838 15.0985 8.95202 15.1785 8.75202 15.1785H8.70474L8.70111 15.1858Z"
        fill="currentColor"
      />
    </svg>
  );
};

type OperationProps = {
  item: ChatItem;
  question: string;
  index: number;
  showPromptLog?: boolean;
  maxSize: number;
  contentWidth: number;
  hasWorkflowProcess: boolean;
  noChatInput?: boolean;
};
const Operation: FC<OperationProps> = ({
  item,
  question,
  index,
  showPromptLog,
  maxSize,
  contentWidth,
  hasWorkflowProcess,
  noChatInput,
}) => {
  const { t } = useTranslation();
  const {
    config,
    onAnnotationAdded,
    onAnnotationEdited,
    onAnnotationRemoved,
    onFeedback,
    onRegenerate,
  } = useChatContext();
  const [isShowReplyModal, setIsShowReplyModal] = useState(false);
  const {
    id,
    isOpeningStatement,
    content: messageContent,
    annotation,
    feedback,
    adminFeedback,
    agent_thoughts,
  } = item;
  const [localFeedback, setLocalFeedback] = useState(
    config?.supportAnnotation ? adminFeedback : feedback
  );

  const content = useMemo(() => {
    if (agent_thoughts?.length) return agent_thoughts.reduce((acc, cur) => acc + cur.thought, '');

    return messageContent;
  }, [agent_thoughts, messageContent]);

  const handleFeedback = async (rating: 'like' | 'dislike' | null) => {
    if (!config?.supportFeedback || !onFeedback) return;

    await onFeedback?.(id, { rating });
    setLocalFeedback({ rating });
  };

  const operationWidth = useMemo(() => {
    let width = 0;
    if (!isOpeningStatement) width += 26;
    if (!isOpeningStatement && showPromptLog) width += 28 + 8;
    if (!isOpeningStatement && config?.text_to_speech?.enabled) width += 26;
    if (!isOpeningStatement && config?.supportAnnotation && config?.annotation_reply?.enabled)
      width += 26;
    if (config?.supportFeedback && !localFeedback?.rating && onFeedback && !isOpeningStatement)
      width += 60 + 8;
    if (config?.supportFeedback && localFeedback?.rating && onFeedback && !isOpeningStatement)
      width += 28 + 8;
    return width;
  }, [
    isOpeningStatement,
    showPromptLog,
    config?.text_to_speech?.enabled,
    config?.supportAnnotation,
    config?.annotation_reply?.enabled,
    config?.supportFeedback,
    localFeedback?.rating,
    onFeedback,
  ]);

  const positionRight = useMemo(() => operationWidth < maxSize, [operationWidth, maxSize]);

  return (
    <>
      <div
        className={cn(
          'absolute -bottom-8 right-2 flex items-center justify-end gap-1'
          // hasWorkflowProcess && '',
          // !positionRight && '-bottom-7 right-2'
          // !hasWorkflowProcess && positionRight && '!top-[9px]'
        )}
        style={!hasWorkflowProcess && positionRight ? { left: contentWidth + 8 } : {}}
      >
        {showPromptLog && !isOpeningStatement && (
          // group-hover:block
          <div className="block ">
            <Log logItem={item} />
          </div>
        )}
        {!isOpeningStatement && (
          //  group-hover:flex
          <div className="flex items-center gap-0.5">
            {config?.text_to_speech?.enabled && (
              <NewAudioButton id={id} value={content} voice={config?.text_to_speech?.voice} />
            )}
            <ActionButton
              onClick={() => {
                copy(content);
                Toast.notify({ type: 'success', message: t('common.actionMsg.copySuccessfully') });
              }}
            >
              <CopyOne className="h-4 w-4" />
            </ActionButton>
            {!noChatInput && (
              <ActionButton onClick={() => onRegenerate?.(item)}>
                {/* <RiResetLeftLine className="h-4 w-4" /> */}
                <ReloadIcon className="h-4 w-4" />
              </ActionButton>
            )}
            {config?.supportAnnotation && config.annotation_reply?.enabled && (
              <ActionButton onClick={() => setIsShowReplyModal(true)}>
                <RiEditLine className="h-4 w-4" />
              </ActionButton>
            )}
          </div>
        )}
        {!isOpeningStatement && config?.supportFeedback && !localFeedback?.rating && onFeedback && (
          <div className="relative -top-[1px] text-xs text-components-toggle-bg-unchecked">|</div>
        )}
        {!isOpeningStatement && config?.supportFeedback && !localFeedback?.rating && onFeedback && (
          <div className="flex items-center gap-0.5 text-components-toggle-bg-unchecked">
            {!localFeedback?.rating && (
              <>
                <ActionButton onClick={() => handleFeedback('like')}>
                  <RiThumbUpLineIcon className="h-4 w-4" />
                </ActionButton>
                <ActionButton onClick={() => handleFeedback('dislike')}>
                  <RiThumbDownLineIcon className="h-4 w-4" />
                </ActionButton>
              </>
            )}
          </div>
        )}
        {!isOpeningStatement && config?.supportFeedback && localFeedback?.rating && onFeedback && (
          <div className="flex items-center gap-0.5">
            {localFeedback?.rating === 'like' && (
              <ActionButton state={ActionButtonState.Active} onClick={() => handleFeedback(null)}>
                <RiThumbUpLineIcon className="h-4 w-4" />
              </ActionButton>
            )}
            {localFeedback?.rating === 'dislike' && (
              <ActionButton
                state={ActionButtonState.Destructive}
                onClick={() => handleFeedback(null)}
              >
                <RiThumbDownLineIcon className="h-4 w-4" />
              </ActionButton>
            )}
          </div>
        )}
      </div>
      <EditReplyModal
        isShow={isShowReplyModal}
        onHide={() => setIsShowReplyModal(false)}
        query={question}
        answer={content}
        onEdited={(editedQuery, editedAnswer) =>
          onAnnotationEdited?.(editedQuery, editedAnswer, index)
        }
        onAdded={(annotationId, authorName, editedQuery, editedAnswer) =>
          onAnnotationAdded?.(annotationId, authorName, editedQuery, editedAnswer, index)
        }
        appId={config?.appId || ''}
        messageId={id}
        annotationId={annotation?.id || ''}
        createdAt={annotation?.created_at}
        onRemove={() => onAnnotationRemoved?.(index)}
      />
    </>
  );
};

export default memo(Operation);
