import{d as ee,c as u,o as r,n as N,b as e,t as z,w as O,A as D,a as b,u as pe,r as n,N as W,aC as I,z as me,R as X,aN as ve,aP as fe,P as _e,e as L,f as m,m as k,p as T,aa as M,a1 as he,J as R,K as V,aO as ge,_ as xe,k as Y,v as Z,E as d,x as be,y as ye}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as we}from"./DeleteIcon.vue_vue_type_script_setup_true_lang-Cz40G926.js";import{M as ke,d as Ce,B as Se,v as Ae,O as Fe,ap as Pe,aq as $e,ar as Ee,a as qe}from"./index-C4ad4gme.js";const ze={class:"flex items-start space-x-4"},Le={key:0,class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium bg-[#21272A]"},Me=["src"],De={key:1,class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:{background:`linear-gradient(
                        129deg,
                        #129bff 0%,
                        #3e60e9 100%
                    )`}},Te={class:"flex-1 min-w-0 !ml-[14px]"},Be={class:"text-[14px] mb-3 leading-[18px] font-semibold text-gray-900 truncate"},je={class:"mt-1 text-[12px] leading-[16px] text-[#A2A9B0]"},Ie={class:"mt-4"},Re={class:"text-xs leading-[16px] text-gray-600 line-clamp-5"},Ve=e("div",{class:"absolute bottom-6 left-6"},[e("div",{class:"flex items-center space-x-2"})],-1),Ne={class:"relative top-[2px] w-[24px] h-[24px] p-[3px] flex items-center justify-center rounded-[6px] hover:bg-[#F5F5F5] transition-colors duration-200"},Oe={name:"QueryappCard"},Ue=ee({...Oe,props:{queryapp:{},canPerformAction:{type:Boolean}},emits:["delete","cardClick"],setup(y){return(a,c)=>{var C,g;return r(),u("div",{class:N(["bg-white rounded-[20px] h-[224px] w-full min-w-[360px] p-[20px] relative group cursor-pointer transition-all duration-300 shadow-[0_2px_6px_0_rgba(0,0,0,0.04)]",{"hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.08)]":a.canPerformAction}]),onClick:c[2]||(c[2]=S=>a.canPerformAction?a.$emit("cardClick",a.queryapp):null)},[e("div",ze,[a.queryapp.icon_path?(r(),u("div",Le,[e("img",{src:a.queryapp.icon_path,alt:"应用图标",class:"w-[24px] h-[24px] object-contain"},null,8,Me)])):(r(),u("div",De,z((C=a.queryapp.title)==null?void 0:C.charAt(0)),1)),e("div",Te,[e("div",Be,z(a.queryapp.title),1),e("p",je," 创建者："+z(((g=a.queryapp.author)==null?void 0:g.name)||"-"),1)])]),e("div",Ie,[e("p",Re,z(a.queryapp.description||"这个应用还没有介绍～"),1)]),Ve,e("div",{class:"absolute bottom-6 right-6",onClick:c[1]||(c[1]=O(()=>{},["stop"]))},[e("div",Ne,[a.canPerformAction?(r(),D(we,{key:0,size:"18",color:"#A2A9B0",class:"relative top-[-1px]",onClick:c[0]||(c[0]=O(S=>a.$emit("delete",a.queryapp),["stop"]))})):b("",!0)])])],2)}}}),Qe=[{value:"bge-m3",label:"bge-m3"},{value:"text-embedding-v3",label:"text-embedding-v3"},{value:"text-embedding-3-large",label:"text-embedding-3-large"},{value:"custom-bce-embedding-base_v1",label:"CUSTOM-BCE-EMBEDDING"}],v=y=>(be("data-v-46137463"),y=y(),ye(),y),Ge={class:"h-full overflow-hidden"},He={class:"flex justify-between items-center mb-6"},Je=v(()=>e("h2",{class:"text-2xl font-bold text-gray-800"}," ",-1)),Ke={key:0,class:"col-span-full flex flex-col items-center justify-center py-20"},We={class:"text-gray-400 text-6xl mb-4"},Xe=v(()=>e("p",{class:"text-gray-500 text-lg mb-6"},"暂无应用数据",-1)),Ye={class:"col-span-full"},Ze={key:0,class:"flex justify-center items-center py-4 text-gray-500"},et=v(()=>e("span",{class:"ml-2 text-sm"},"加载中...",-1)),tt={key:1,class:"flex justify-center py-4 text-gray-500 text-sm"},st={class:"mt-6"},at={key:0,class:"new-modal-overlay"},ot={class:"new-modal-container"},lt=v(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"创建应用")],-1)),nt={class:"new-modal-center"},it={class:"new-modal-center-item"},rt=v(()=>e("label",{class:"font16 font-zhongcu"},[T("应用名称"),e("span",{class:"text-red-500"},"*")],-1)),ct={class:"new-modal-center-item"},dt=v(()=>e("label",{class:"font16 font-zhongcu"},[T("应用介绍"),e("span",{class:"text-red-500"},"*")],-1)),ut={class:"new-modal-center-item"},pt=v(()=>e("label",{class:"font16 font-zhongcu"},[T("数据模型"),e("span",{class:"text-red-500"},"*")],-1)),mt={class:"new-modal-center-item"},vt=v(()=>e("label",{class:"font16 font-zhongcu"},"应用图标 ",-1)),ft={class:"flex items-end mt-4"},_t={class:"mr-6"},ht={class:"flex w-14 h-14 p-3 border border-dashed border-[#E5E5E5] justify-center items-center flex-shrink-0 rounded-[6px] bg-[#F5F5F5]"},gt=["src"],xt={class:"flex items-center space-x-2"},bt=["onClick"],yt=["src"],wt={class:"new-modal-bottom pt-[20px]"},kt=v(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"}," 创建 ",-1)),Ct=ee({__name:"index",setup(y){const a=pe(),c=ke(),C=n([{path:"/admin/application",name:"应用管理"}]),g=n(!1),S=Ce(),U=()=>{var t;g.value=Fe(((t=S.userInfo)==null?void 0:t.permissions)||[],["create_aiserver"])};W(()=>S.userInfo,()=>{U()});const f=n([]),x=n(!1),_=n(1),A=n(20),Q=n(0),B=n(!1),i=n(null),F=n(!1),te=n(),o=n({title:"",description:"",embedding_model:"text-embedding-v3",icon_path:"/staticFiles/img/app_icons/y-icon0.png"}),se=n(["/staticFiles/img/app_icons/y-icon0.png","/staticFiles/img/app_icons/y-icon1.png","/staticFiles/img/app_icons/y-icon2.png","/staticFiles/img/app_icons/y-icon3.png","/staticFiles/img/app_icons/y-icon4.png","/staticFiles/img/app_icons/y-icon5.png","/staticFiles/img/app_icons/y-icon6.png","/staticFiles/img/app_icons/y-icon7.png"]),P=n(!1),ae=n("永久删除应用"),oe=n("该应用数据将被永久删除，不可恢复及撤销。确定要删除吗？"),w=n(null),$=async(t,s,h=!1)=>{var E,q;x.value=!0;try{const p=await Pe(t,s);p.data&&Array.isArray(p.data.data)?(h?f.value=[...f.value,...p.data.data]:f.value=p.data.data,Q.value=((E=p.data.total_num)==null?void 0:E[0])||0,B.value=f.value.length>=Q.value):d.error(((q=p.data)==null?void 0:q.message)||"获取应用列表失败")}catch(p){console.error("获取应用列表失败:",p),d.error("获取应用列表失败")}finally{x.value=!1}},j=I(()=>{if(i.value&&a.currentRoute.value.path==="/admin/application"){const{scrollTop:t}=i.value;c.setApplicationListScrollPosition(t)}},100),G=I(t=>{if(!i.value||a.currentRoute.value.path!=="/admin/application")return;const{scrollTop:s}=i.value;x.value||c.setApplicationListScrollPosition(s)},100),H=I(()=>{if(!i.value)return;const{scrollTop:t,scrollHeight:s,clientHeight:h}=i.value;t+h>=s-100&&!x.value&&!B.value&&(_.value++,$(_.value,A.value,!0))},200),J=()=>{i.value&&(i.value.addEventListener("scroll",H,{passive:!0}),i.value.addEventListener("scroll",G,{passive:!0}))},K=()=>{i.value&&(i.value.removeEventListener("scroll",H),i.value.removeEventListener("scroll",G))};W(()=>a.currentRoute.value.path,(t,s)=>{s==="/admin/application"&&j()},{immediate:!1});const le=()=>{o.value={title:"",description:"",embedding_model:"text-embedding-v3",icon_path:"/staticFiles/img/app_icons/y-icon0.png"},F.value=!0},ne=async()=>{if(!o.value.title){d.warning("请填写应用名称");return}if(!o.value.description){d.warning("请填写应用介绍");return}if(!o.value.embedding_model){d.warning("请选择数据模型");return}try{const t=await $e(o.value.title,o.value.description,o.value.embedding_model,o.value.icon_path);t.data.error==="0"?(d.success("应用创建成功"),F.value=!1,_.value=1,$(_.value,A.value)):t.data.error==="403"?d.error("暂无权限"):d.error(t.data.message||"创建应用失败")}catch(t){console.error("创建应用失败:",t),d.error("创建应用失败")}},ie=t=>{w.value=t.id,P.value=!0},re=()=>{P.value=!1,w.value=null},ce=async()=>{if(w.value)try{const t=await Ee(w.value);t.data.error==="0"?(d.success("删除成功"),_.value=1,$(_.value,A.value)):d.error(t.data.message||"删除失败")}catch(t){console.error("删除应用失败:",t),d.error("删除应用失败")}finally{P.value=!1,w.value=null}},de=t=>{a.push({name:"ApplicationDetail",params:{qid:t.id}})};return me(()=>{U(),$(_.value,A.value),X(()=>{J()})}),ve(()=>{a.currentRoute.value.path==="/admin/application"&&(J(),setTimeout(()=>{X(async()=>{if(i.value&&c.applicationListScrollPosition>0){await new Promise(s=>setTimeout(s,300));const t=(s=0)=>{s>=3||(i.value.scrollTop=c.applicationListScrollPosition,Math.abs(i.value.scrollTop-c.applicationListScrollPosition)>10&&setTimeout(()=>t(s+1),100))};t()}})},100))}),fe(()=>{K(),j()}),_e(()=>{K(),j()}),(t,s)=>{const h=L("el-icon"),E=L("el-button"),q=L("el-option"),p=L("el-select");return r(),u("div",Ge,[e("div",He,[Je,g.value?(r(),D(E,{key:0,type:"primary",onClick:le,class:"flex items-center !rounded-md"},{default:k(()=>[m(h,{class:"mr-1"},{default:k(()=>[m(M(he))]),_:1}),T(" 创建应用 ")]),_:1})):b("",!0)]),e("div",{ref_key:"containerRef",ref:i,class:N(["grid auto-rows-[224px] gap-6 overflow-y-auto h-[calc(100vh-272px)] custom-scrollbar p-2",f.value.length<=1?"grid-cols-[repeat(auto-fit,minmax(360px,0.5fr))]":"grid-cols-[repeat(auto-fit,minmax(360px,1fr))]"])},[(r(!0),u(R,null,V(f.value,l=>(r(),D(Ue,{key:l.id,queryapp:l,canPerformAction:g.value,onDelete:ie,onCardClick:de},null,8,["queryapp","canPerformAction"]))),128)),!x.value&&f.value.length===0?(r(),u("div",Ke,[e("div",We,[m(h,null,{default:k(()=>[m(M(ge))]),_:1})]),Xe])):b("",!0),e("div",Ye,[x.value?(r(),u("div",Ze,[m(h,{class:"is-loading"},{default:k(()=>[m(M(xe))]),_:1}),et])):b("",!0),B.value&&f.value.length>0?(r(),u("div",tt," 没有更多数据了 ")):b("",!0)])],2),e("div",st,[m(Se,{breadcrumbs:C.value},null,8,["breadcrumbs"])]),m(Ae,{show:P.value,title:ae.value,message:oe.value,onClose:re,onConfirm:ce},null,8,["show","title","message"]),F.value?(r(),u("div",at,[e("div",ot,[lt,e("form",{onSubmit:O(ne,["prevent"]),ref_key:"formRef",ref:te},[e("div",nt,[e("div",it,[rt,Y(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":s[0]||(s[0]=l=>o.value.title=l),placeholder:"请输入应用名称",required:"",maxlength:"50"},null,512),[[Z,o.value.title]])]),e("div",ct,[dt,Y(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu leading-normal align-text-top p-2 resize-none","onUpdate:modelValue":s[1]||(s[1]=l=>o.value.description=l),placeholder:"给应用介绍一下",required:"",maxlength:"100",style:{appearance:"none","-webkit-appearance":"none","line-height":"1.5","vertical-align":"top"}},null,512),[[Z,o.value.description]])]),e("div",ut,[pt,m(p,{modelValue:o.value.embedding_model,"onUpdate:modelValue":s[2]||(s[2]=l=>o.value.embedding_model=l),placeholder:"选择数据模型",size:"large",class:"font14 font-zhongcu field-select w-full focus:border-[rgba(18,155,254,1)] focus:outline-none"},{default:k(()=>[(r(!0),u(R,null,V(M(Qe),l=>(r(),D(q,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",mt,[vt,e("div",ft,[e("div",_t,[e("div",ht,[e("img",{src:o.value.icon_path,alt:"应用图标",class:"w-12 h-12 object-contain"},null,8,gt)])]),e("div",xt,[(r(!0),u(R,null,V(se.value,(l,ue)=>(r(),u("div",{key:ue,onClick:St=>o.value.icon_path=l,class:"cursor-pointer"},[e("div",{class:N(["w-10 h-10 flex justify-center items-center rounded-[6px] transition-colors duration-200",[o.value.icon_path===l?"bg-[#EBF7FF]":"hover:bg-[#F5F5F5]"]])},[e("img",{src:l,alt:"应用图标",class:"w-6 h-6 object-contain"},null,8,yt)],2)],8,bt))),128))])])])]),e("div",wt,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:s[3]||(s[3]=l=>F.value=!1)}," 取消 "),kt])],544)])])):b("",!0)])}}}),$t=qe(Ct,[["__scopeId","data-v-46137463"]]);export{$t as default};
