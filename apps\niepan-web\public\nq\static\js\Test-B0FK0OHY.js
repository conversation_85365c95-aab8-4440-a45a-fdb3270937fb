import{d as f,A as u,o as a,m as c,b as e,n as _,q as D,r as d,ae as I,E as C,Q as S,N as K,e as y,c as w,f as n,t as p,J as T,K as j,aa as g,aR as A,a as R,aS as q,x as P,y as J}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as x,ah as Q,B as U,a as G}from"./index-C4ad4gme.js";import{_ as O}from"./BackArrowIcon.vue_vue_type_script_setup_true_lang-D6zln9HB.js";const W=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.34508 7.4249L17.0126 1.75322V1.7549C17.3815 1.61217 17.7999 1.70031 18.0798 1.97974C18.3598 2.25917 18.4488 2.67737 18.3067 3.04656L12.6467 17.7257C12.5008 18.1051 12.1397 18.3584 11.7332 18.3665C11.3268 18.3745 10.956 18.1357 10.7951 17.7624L8.23508 11.8257L2.31008 9.27574C1.93718 9.11494 1.69856 8.74459 1.70623 8.33857C1.71391 7.93255 1.96637 7.57149 2.34508 7.4249ZM16.2091 3.85111L16.2093 3.85074H16.2101L16.2091 3.85111ZM16.2091 3.85111L4.46842 8.3899L8.89342 10.2949C9.23415 10.4414 9.51626 10.6975 9.69508 11.0224L9.76508 11.1657L11.6784 15.6016L16.2091 3.85111Z",fill:"white"})],-1),X={name:"SendMessageIcon"},Y=f({...X,props:{color:{default:"#52C668"},size:{default:20},className:{default:""}},setup(r){return(t,s)=>(a(),u(x,{color:t.color,size:t.size,class:_(t.className)},{default:c(()=>[W]),_:1},8,["color","size","class"]))}}),ee=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"},[e("path",{d:"M20.8334 91.6668H79.1667C81.468 91.6668 83.3334 89.8014 83.3334 87.5002V29.1668H62.5001V8.3335H20.8334C18.5322 8.3335 16.6667 10.199 16.6667 12.5002V87.5002C16.6667 89.8014 18.5322 91.6668 20.8334 91.6668Z",fill:"#E9EDF7"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12.5 12.4998C12.5 7.89746 16.231 4.1665 20.8333 4.1665H62.5C64.8012 4.1665 66.6667 6.03198 66.6667 8.33317V24.9998H83.3333C85.6345 24.9998 87.5 26.8653 87.5 29.1665V87.4998C87.5 92.1023 83.7691 95.8332 79.1667 95.8332H20.8333C16.231 95.8332 12.5 92.1023 12.5 87.4998V12.4998ZM58.3333 12.4998H20.8333V87.4998H79.1667V33.3332H62.5C60.1988 33.3332 58.3333 31.4677 58.3333 29.1665V12.4998Z",fill:"#E9EDF7"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M59.5536 5.38689C61.1808 3.75971 63.819 3.75971 65.4462 5.38689L86.2795 26.2202C87.9067 27.8474 87.9067 30.4856 86.2795 32.1128C84.6523 33.74 82.0142 33.74 80.387 32.1128L59.5536 11.2794C57.9265 9.65226 57.9265 7.01408 59.5536 5.38689Z",fill:"#E9EDF7"}),e("path",{d:"M45.8333 66.6665C52.7368 66.6665 58.3333 61.0701 58.3333 54.1665C58.3333 47.2629 52.7368 41.6665 45.8333 41.6665C38.9297 41.6665 33.3333 47.2629 33.3333 54.1665C33.3333 61.0701 38.9297 66.6665 45.8333 66.6665Z",fill:"white"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M45.8333 44.7915C40.6556 44.7915 36.4583 48.9888 36.4583 54.1665C36.4583 59.3442 40.6556 63.5415 45.8333 63.5415C51.0109 63.5415 55.2083 59.3442 55.2083 54.1665C55.2083 48.9888 51.0109 44.7915 45.8333 44.7915ZM30.2083 54.1665C30.2083 45.5371 37.2038 38.5415 45.8333 38.5415C54.4627 38.5415 61.4583 45.5371 61.4583 54.1665C61.4583 62.796 54.4627 69.7915 45.8333 69.7915C37.2038 69.7915 30.2083 62.796 30.2083 54.1665Z",fill:"white"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M53.8099 60.5479C54.8881 59.2002 56.8546 58.9817 58.2023 60.0599L68.619 68.3932C69.9666 69.4714 70.1852 71.4379 69.107 72.7856C68.0288 74.1333 66.0623 74.3518 64.7146 73.2737L54.2979 64.9403C52.9502 63.8622 52.7317 61.8956 53.8099 60.5479Z",fill:"white"})],-1),te={name:"KbaseTestDefaultIcon"},se=f({...te,props:{color:{default:"#52C668"},size:{default:100},className:{default:""}},setup(r){return(t,s)=>(a(),u(x,{color:t.color,size:t.size,class:_(t.className)},{default:c(()=>[ee]),_:1},8,["color","size","class"]))}}),oe=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 14"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.25 1.75016C2.25 1.10583 2.77233 0.583496 3.41667 0.583496H9.25C9.40471 0.583496 9.55308 0.644954 9.66248 0.75435L12.5791 3.67102C12.6885 3.78041 12.75 3.92879 12.75 4.0835V12.2502C12.75 12.8945 12.2277 13.4168 11.5833 13.4168H3.41667C2.77234 13.4168 2.25 12.8945 2.25 12.2502V1.75016ZM9.00838 1.75016H3.41667V12.2502H11.5833V4.32512L9.00838 1.75016ZM4.58333 5.83333C4.58333 5.51117 4.8445 5.25 5.16667 5.25H9.83333C10.1555 5.25 10.4167 5.51117 10.4167 5.83333C10.4167 6.1555 10.1555 6.41667 9.83333 6.41667H5.16667C4.8445 6.41667 4.58333 6.1555 4.58333 5.83333ZM5.16667 7.58333C4.8445 7.58333 4.58333 7.8445 4.58333 8.16667C4.58333 8.48883 4.8445 8.75 5.16667 8.75H9.83333C10.1555 8.75 10.4167 8.48883 10.4167 8.16667C10.4167 7.8445 10.1555 7.58333 9.83333 7.58333H5.16667Z",fill:"#333333"})],-1),le={name:"KbaseTestDefaultFileIcon"},ae=f({...le,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(r){return(t,s)=>(a(),u(x,{color:t.color,size:t.size,class:_(t.className)},{default:c(()=>[oe]),_:1},8,["color","size","class"]))}}),ne=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},[e("path",{d:"M7.99984 6.66667C9.2885 6.66667 10.3332 5.622 10.3332 4.33333C10.3332 3.04467 9.2885 2 7.99984 2C6.71117 2 5.6665 3.04467 5.6665 4.33333C5.6665 5.622 6.71117 6.66667 7.99984 6.66667Z",fill:"white",stroke:"white","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"}),e("path",{d:"M2 13.6002V14.0002H14V13.6002C14 12.1067 14 11.36 13.7094 10.7895C13.4537 10.2878 13.0457 9.8798 12.544 9.62413C11.9735 9.3335 11.2268 9.3335 9.73333 9.3335H6.26667C4.7732 9.3335 4.02647 9.3335 3.45603 9.62413C2.95426 9.8798 2.54631 10.2878 2.29065 10.7895C2 11.36 2 12.1067 2 13.6002Z",fill:"white",stroke:"white","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"})],-1),re={name:"AddIcon"},ce=f({...re,props:{color:{default:"white"},size:{default:16},className:{default:""}},setup(r){return(t,s)=>(a(),u(x,{color:t.color,size:t.size,class:_(t.className)},{default:c(()=>[ne]),_:1},8,["color","size","class"]))}}),H=r=>(P("data-v-39c03c93"),r=r(),J(),r),de={class:"h-full overflow-hidden"},ie={class:"overflow-hidden h-[calc(100vh-234px)] overflow-y-auto px-2"},ue={class:"flex items-center mb-6 pb-[12px] border-0 border-solid border-b border-[#E5E5E5]"},_e=H(()=>e("span",{class:"ml-2 text-[18px] font-semibold text-[#121619] leading-[26px] font-[PingFang SC]"},"测试",-1)),pe={class:"h-[calc(100vh-300px)] relative",style:{"padding-bottom":"102px"}},fe={key:0,class:"h-full bg-white rounded-[12px] p-5 overflow-y relative"},me={class:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center"},he=H(()=>e("p",{class:"mt-3 text-[#697077] font-['PingFang_SC'] text-[14px] font-normal leading-[22px]"}," 输入知识库相关问题，测试文本块召回情况 ",-1)),ve={key:1,class:"h-full bg-white rounded-[12px] p-4 overflow-y"},Ce={class:"flex items-center"},we={class:"w-8 h-8 rounded-full text-center leading-8 bg-[#129BFE] mr-3 flex-shrink-0"},ge={class:"text-base font-semibold w-full truncate"},xe={class:"result-item p-5 rounded-[12px] bg-[#F7F9FC] h-full"},be={class:"result-item-top font12 text-[#697077]"},ke={class:"result-item-content font14 multi-line-ellipsis my-[15px] text-[#697077] leading-5"},ye={class:"result-item-bottom font12 flex items-center"},Ve={class:"file-name truncate"},He=["disabled"],Me={class:"mt-6"},ze=f({__name:"Test",setup(r){const t=D(),s=d(""),V=d(""),m=d(!1),b=d(!1),h=d(null),v=d([]),M=()=>{b.value=!0},z=()=>{b.value=!1},L=o=>{if(o.key==="Enter")if(s.value.trim()===""){o.preventDefault();return}else k()},E=(o,i)=>o+"."+i,k=I.debounce(async()=>{if(s.value==""||!s.value){C.error("请输入要测试的文本");return}h.value=S.service({lock:!0,text:"搜索中",background:"rgba(0, 0, 0, 0.5)"}),v.value=[];try{const o=await Q(t.params.id,s.value);V.value=s.value,o.data.message?C.error(o.data.message):(v.value=o.data,o.data.length<=0&&(h.value.close(),C.error("暂无搜索结果")))}catch{h.value.close(),C.error("搜索失败")}finally{h.value.close(),s.value=""}});K(s,o=>{o!==""?m.value=!0:m.value=!1});const Z=d([{name:"知识库",path:"/admin/knowledge"},{name:"召回测试"}]);return(o,i)=>{const F=y("router-link"),B=y("el-tooltip"),$=y("el-input");return a(),w("div",de,[e("div",ie,[e("div",ue,[n(F,{to:{name:"Knowledge"},class:"flex items-center cursor-pointer"},{default:c(()=>[n(O,{size:24,color:"#121619"})]),_:1}),_e]),e("div",pe,[v.value.length<=0?(a(),w("div",fe,[e("div",me,[n(se,{class:"mb-3"}),he])])):(a(),w("div",ve,[e("div",Ce,[e("div",we,[n(ce)]),e("div",ge,p(V.value),1)]),n(g(q),{gutter:16},{default:c(()=>[(a(!0),w(T,null,j(v.value,(l,N)=>(a(),u(g(A),{span:6,key:N,class:"min-h-[210px] mt-[16px]"},{default:c(()=>[e("div",xe,[e("div",be," 集合ID "+p(l.vectorid),1),e("div",ke,p(l.docfragment),1),l.document_title?(a(),u(B,{key:0,effect:"light",content:E(l.document_title,l.document_type),placement:"bottom"},{default:c(()=>[e("div",ye,[n(ae,{class:"mr-1"}),e("span",Ve,p(l.document_title)+"."+p(l.document_type),1)])]),_:2},1032,["content"])):R("",!0)])]),_:2},1024))),128))]),_:1})])),e("div",{class:_(["absolute bottom-[10px] left-0 right-0 send-box flex items-center justify-between px-5",{focused:b.value}])},[n($,{modelValue:s.value,"onUpdate:modelValue":i[0]||(i[0]=l=>s.value=l),placeholder:"输入问题",resize:"none",onKeydown:L,onFocus:M,onBlur:z},null,8,["modelValue"]),e("button",{class:_(["send-btn w-10 h-10 rounded-[10px] flex items-center justify-center",m.value?"":"send-btn-no"]),disabled:!m.value,onClick:i[1]||(i[1]=(...l)=>g(k)&&g(k)(...l))},[n(Y,{class:"w-5 h-5"})],10,He)],2)])]),e("div",Me,[n(U,{breadcrumbs:Z.value},null,8,["breadcrumbs"])])])}}}),Fe=G(ze,[["__scopeId","data-v-39c03c93"]]);export{Fe as default};
