import{_}from"./quesheng-C5oWzANf.js";import{f as v}from"./utils-ahBajhV-.js";import{u as b}from"./useLinkHandler-E0UXn71M.js";import{d as g,s as k,c as n,o as l,b as s,t as a,n as m,aa as i}from"./pnpm-pnpm-B4aX-tnA.js";import"./index-C4ad4gme.js";const x=["href","target"],C={class:"flex justify-between items-center"},w={class:"text-3xl font-zhongcu"},j={class:"font-zhongcu ml-1"},y={class:"text-[#3C3C43]"},L={class:"w-[42px] h-[42px] overflow-hidden rounded-full text-center leading-[42px]"},z=["src"],B={key:1,src:_,alt:"",class:"w-full h-full object-contain object-center"},F={class:"mt-[10px]"},N={class:"text-[rgba(60,60,67,0.6)]"},U={class:"text-theme mt-[7px]"},V=g({__name:"Card5Component",props:{data:{}},setup(p){const o=p,{linkUrl:f,handleClick:r}=b(o),d=k(()=>{if(o.data.totalNum==0)return{value:0,unit:""};const{value:t,unit:e}=v(o.data.totalNum,!1);return{value:t,unit:e}});return(t,e)=>{var c,u;return l(),n("a",{href:i(f),onClick:e[0]||(e[0]=(...h)=>i(r)&&i(r)(...h)),target:t.data.isLink?"_blank":"",class:m(t.data.isLink?"":"cursor-default")},[s("div",{class:m(["bg-white p-[15px] pt-[7px] pb-[13px] border border-solid border-[#F1F1F1] rounded-[20px] h-full text-xs",{"transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5":t.data.isLink}])},[s("div",C,[s("div",null,[s("div",null,[s("span",w,a(d.value.value),1),s("span",j,a(d.value.unit),1)]),s("div",y,a(t.data.title),1)]),s("div",L,[(c=t.data)!=null&&c.imgUrl?(l(),n("img",{key:0,src:(u=t.data)==null?void 0:u.imgUrl,alt:"",class:"w-full h-full object-contain object-center"},null,8,z)):(l(),n("img",B))])]),s("div",F,a(t.data.desc1),1),s("div",N,a(t.data.desc2),1),s("div",U,a(t.data.desc3),1)],2)],10,x)}}});export{V as default};
