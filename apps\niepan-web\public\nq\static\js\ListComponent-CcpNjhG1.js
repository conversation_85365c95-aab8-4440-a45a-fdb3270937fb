import{d as p,s as u,c as o,o as a,b as e,t as s,a as m,J as _,K as f}from"./pnpm-pnpm-B4aX-tnA.js";const b={class:"w-[360px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs"},h={class:"flex justify-between items-center"},x={class:"text-3xl font-zhongcu"},k={class:"font-zhongcu ml-1"},v={class:"mt-[10px]"},w={class:"overflow-one w-[80%]"},g={class:"px-[5px] overflow-one"},y=["href"],B=p({__name:"ListComponent",props:{data:{}},setup(l){const n=l,i=u(()=>n.data.link?`${n.data.link}`:"javascript:;");return(t,F)=>{var r;return a(),o("div",b,[e("div",h,[e("div",null,[e("span",x,s(t.data.totalNum),1),e("span",k,s(t.data.title),1)])]),e("div",v,[(a(!0),o(_,null,f((r=t.data)==null?void 0:r.items,(d,c)=>(a(),o("div",{key:c,class:"flex justify-between item-center py-[8px] border-0 border-b border-[#ECECF2] border-solid text-xs"},[e("span",w,s(d.title),1),e("span",g,s(d.status),1)]))),128)),t.data.isMore?(a(),o("a",{key:0,href:i.value,target:"_blank",class:"text-[#129BFE] mt-[5px] block"},s(t.data.moreTitle),9,y)):m("",!0)])])}}});export{B as default};
