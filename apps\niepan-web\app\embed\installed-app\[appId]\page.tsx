'use client';

import React, { useEffect, useState } from 'react';
import InstalledApp from '@/app/components/explore/installed-app';
import ExploreContext from '@/context/explore-context';
import { fetchInstalledAppList } from '@/service/explore';
import type { InstalledApp as InstalledAppType } from '@/models/explore';
import '@/app/styles/globals.css';

export default function EmbedDataSourcesPage({ params }: { params: Promise<{ appId: string }> }) {
  const resolvedParams = React.use(params);
  const [isLoading, setIsLoading] = useState(true);
  const [installedApps, setInstalledApps] = useState<InstalledAppType[]>([]);
  const [controlUpdateInstalledApps, setControlUpdateInstalledApps] = useState(0);
  const [hasEditPermission, setHasEditPermission] = useState(false);

  useEffect(() => {
    console.log('Component mounted');
    console.log('Full params:', resolvedParams);
    console.log('appId:', resolvedParams.appId);
    console.log('Current URL:', window.location.href);

    const fetchApps = async () => {
      try {
        const response = await fetchInstalledAppList();
        const { installed_apps } = response as { installed_apps: InstalledAppType[] };
        setInstalledApps(installed_apps || []);
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to fetch installed apps:', error);
        setIsLoading(false);
      }
    };
    fetchApps();
  }, [controlUpdateInstalledApps, resolvedParams.appId]);

  if (!resolvedParams.appId) {
    console.error('No appId provided in params');
    return <div>Error: No appId provided</div>;
  }

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="h-full w-full">
      <ExploreContext.Provider
        value={{
          controlUpdateInstalledApps,
          setControlUpdateInstalledApps,
          hasEditPermission,
          installedApps,
          setInstalledApps,
        }}
      >
        <InstalledApp id={resolvedParams.appId} />
      </ExploreContext.Provider>
    </div>
  );
}
