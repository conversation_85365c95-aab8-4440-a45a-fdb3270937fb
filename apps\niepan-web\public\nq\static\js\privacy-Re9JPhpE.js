import{V as s,e as t,c as a,o as c,b as d,A as l,a as i}from"./pnpm-pnpm-B4aX-tnA.js";import{a as m}from"./index-C4ad4gme.js";const _={name:"PrivacyView",components:{VueOfficeDocx:s},data(){return{docxSrc:"/staticFiles/yszc/yszc20250311.docx"}},methods:{renderedHandler(){console.log("文档渲染完成")},errorHandler(e){console.error("文档渲染失败:",e)}}},f={class:"privacy-container max-w-4xl mx-auto"},x={class:"bg-white rounded-lg"};function p(e,u,h,v,r,o){const n=t("vue-office-docx");return c(),a("div",f,[d("div",x,[r.docxSrc?(c(),l(n,{key:0,src:r.docxSrc,style:{width:"100%",height:"100%"},onRendered:o.rendered<PERSON>and<PERSON>,onError:o.errorHandler,class:"w-full min-h-[600px]"},null,8,["src","onRendered","onError"])):i("",!0)])])}const V=m(_,[["render",p]]);export{V as default};
