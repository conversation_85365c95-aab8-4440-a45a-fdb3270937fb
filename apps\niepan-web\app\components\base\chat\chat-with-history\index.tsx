import type { FC } from 'react';
import { useEffect, useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import { useThemeContext } from '../embedded-chatbot/theme/theme-context';
import { ChatWithHistoryContext, useChatWithHistoryContext } from './context';
import { useChatWithHistory } from './hooks';
import Sidebar from './sidebar';
import Header from './header';
import HeaderInMobile from './header-in-mobile';
import ChatWrapper from './chat-wrapper';
import Workbench from './workbench';
import type { InstalledApp } from '@/models/explore';
import Loading from '@/app/components/base/loading';
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints';
import { checkOrSetAccessToken } from '@/app/components/share/utils';
import AppUnavailable from '@/app/components/base/app-unavailable';
import cn from '@/utils/classnames';
import { RiLayoutRight2Line } from '@remixicon/react';
import ActionButton from '@/app/components/base/action-button';

type ChatWithHistoryProps = {
  className?: string;
};
const ChatWithHistory: FC<ChatWithHistoryProps> = ({ className }) => {
  const {
    appInfoError,
    appData,
    appInfoLoading,
    appChatListDataLoading,
    chatShouldReloadKey,
    isMobile,
    themeBuilder,
    sidebarCollapseState,
    workbenchCollapseState,
    handleWorkbenchCollapse,
    isWorkbenchEnabled,
  } = useChatWithHistoryContext();
  const isSidebarCollapsed = sidebarCollapseState;
  const isWorkbenchCollapsed = workbenchCollapseState;
  const customConfig = appData?.custom_config;
  const site = appData?.site;

  const [showSidePanel, setShowSidePanel] = useState(false);
  const [showWorkbenchPanel, setShowWorkbenchPanel] = useState(false);

  useEffect(() => {
    themeBuilder?.buildTheme(site?.chat_color_theme, site?.chat_color_theme_inverted);
    if (site) {
      if (customConfig) document.title = `${site.title}`;
      else document.title = `${site.title} - Powered by INTELLIDO`;
    }
  }, [site, customConfig, themeBuilder]);

  if (appInfoLoading) return <Loading type="app" />;

  if (appInfoError) return <AppUnavailable />;

  return (
    <div className={cn('flex h-full bg-background-body', isMobile && 'flex-col', className)}>
      {!isMobile && (
        <div
          className={cn(
            'flex w-[240px] flex-col bg-components-panel-bg px-4 py-5 transition-all duration-200 ease-in-out',
            isSidebarCollapsed && 'w-0 overflow-hidden !p-0'
          )}
        >
          <Sidebar />
        </div>
      )}
      {isMobile && <HeaderInMobile />}
      <div className={cn('relative grow p-2', isMobile && 'h-[calc(100%_-_56px)] p-0')}>
        {isSidebarCollapsed && (
          <div
            className={cn(
              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',
              showSidePanel ? 'left-0' : 'left-[-248px]'
            )}
            onMouseEnter={() => setShowSidePanel(true)}
            onMouseLeave={() => setShowSidePanel(false)}
          >
            <Sidebar isPanel />
          </div>
        )}
        <div
          className={cn(
            'flex h-full flex-col overflow-hidden',
            isMobile ? 'rounded-t-2xl' : 'rounded-2xl'
          )}
        >
          {!isMobile && <Header />}
          {appChatListDataLoading && <Loading type="app" />}
          {!appChatListDataLoading && <ChatWrapper key={chatShouldReloadKey} />}
        </div>
      </div>
      {isWorkbenchEnabled && !isMobile && (
        <div
          className={cn(
            'flex w-[240px] flex-col bg-components-panel-bg px-4 py-5 transition-all duration-200 ease-in-out',
            isWorkbenchCollapsed && 'w-0 overflow-hidden !p-0'
          )}
        >
          <div className="mb-4 flex items-center justify-between">
            <div className="body-lg-semibold text-text-primary">工作台</div>
            <ActionButton size="l" onClick={() => handleWorkbenchCollapse(true)}>
              <RiLayoutRight2Line className="h-[18px] w-[18px]" />
            </ActionButton>
          </div>
          <Workbench />
        </div>
      )}
      {isWorkbenchEnabled && !isMobile && isWorkbenchCollapsed && (
        <div
          className={cn(
            'absolute right-0 top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',
            showWorkbenchPanel ? 'right-0' : 'right-[-248px]'
          )}
          // onMouseEnter={() => setShowWorkbenchPanel(true)}
          // onMouseLeave={() => setShowWorkbenchPanel(false)}
        >
          <Workbench isPanel />
        </div>
      )}
    </div>
  );
};

export type ChatWithHistoryWrapProps = {
  installedAppInfo?: InstalledApp;
  className?: string;
};
const ChatWithHistoryWrap: FC<ChatWithHistoryWrapProps> = ({ installedAppInfo, className }) => {
  const media = useBreakpoints();
  const isMobile = media === MediaType.mobile;
  const themeBuilder = useThemeContext();

  const {
    appInfoError,
    appInfoLoading,
    appData,
    appParams,
    appMeta,
    appChatListDataLoading,
    currentConversationId,
    currentConversationItem,
    appPrevChatTree,
    pinnedConversationList,
    conversationList,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    inputsForms,
    handleNewConversation,
    handleStartChat,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    handleDeleteConversation,
    conversationRenaming,
    handleRenameConversation,
    handleNewConversationCompleted,
    chatShouldReloadKey,
    isInstalledApp,
    appId,
    handleFeedback,
    currentChatInstanceRef,
    sidebarCollapseState,
    handleSidebarCollapse,
    workbenchCollapseState,
    handleWorkbenchCollapse,
    clearChatList,
    setClearChatList,
    isResponding,
    setIsResponding,
    isWorkbenchEnabled,
  } = useChatWithHistory(installedAppInfo);

  return (
    <ChatWithHistoryContext.Provider
      value={{
        appInfoError,
        appInfoLoading,
        appData,
        appParams,
        appMeta,
        appChatListDataLoading,
        currentConversationId,
        currentConversationItem,
        appPrevChatTree,
        pinnedConversationList,
        conversationList,
        newConversationInputs,
        newConversationInputsRef,
        handleNewConversationInputsChange,
        inputsForms,
        handleNewConversation,
        handleStartChat,
        handleChangeConversation,
        handlePinConversation,
        handleUnpinConversation,
        handleDeleteConversation,
        conversationRenaming,
        handleRenameConversation,
        handleNewConversationCompleted,
        chatShouldReloadKey,
        isMobile,
        isInstalledApp,
        appId,
        handleFeedback,
        currentChatInstanceRef,
        themeBuilder,
        sidebarCollapseState,
        handleSidebarCollapse,
        workbenchCollapseState,
        handleWorkbenchCollapse,
        clearChatList,
        setClearChatList,
        isResponding,
        setIsResponding,
        isWorkbenchEnabled,
      }}
    >
      <ChatWithHistory className={className} />
    </ChatWithHistoryContext.Provider>
  );
};

const ChatWithHistoryWrapWithCheckToken: FC<ChatWithHistoryWrapProps> = ({
  installedAppInfo,
  className,
}) => {
  const [initialized, setInitialized] = useState(false);
  const [appUnavailable, setAppUnavailable] = useState<boolean>(false);
  const [isUnknownReason, setIsUnknownReason] = useState<boolean>(false);

  useAsyncEffect(async () => {
    if (!initialized) {
      if (!installedAppInfo) {
        try {
          await checkOrSetAccessToken();
        } catch (e: any) {
          if (e.status === 404) {
            setAppUnavailable(true);
          } else {
            setIsUnknownReason(true);
            setAppUnavailable(true);
          }
        }
      }
      setInitialized(true);
    }
  }, []);

  if (!initialized) return null;

  if (appUnavailable) return <AppUnavailable isUnknownReason={isUnknownReason} />;

  return <ChatWithHistoryWrap installedAppInfo={installedAppInfo} className={className} />;
};

export default ChatWithHistoryWrapWithCheckToken;
