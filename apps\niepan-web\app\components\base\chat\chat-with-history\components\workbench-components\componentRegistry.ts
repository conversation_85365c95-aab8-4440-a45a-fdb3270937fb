import { lazy, ComponentType } from 'react';

// 定义组件数据接口
export interface WorkbenchComponentData {
  uid: string;
  area: string;
  data: Record<string, any>;
}

// 定义组件注册接口
export interface ComponentRegistration {
  component: ComponentType<any>;
  loader?: () => Promise<{ default: ComponentType<any> }>;
}

// 组件注册表
const componentRegistry = new Map<string, ComponentRegistration>();

// 基础加载组件
const LoadingComponent: ComponentType = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-gray-500">加载中...</div>
  </div>
);

// 基础错误组件
const ErrorComponent: ComponentType<{ error?: string }> = ({ error }) => (
  <div className="flex items-center justify-center h-full">
    <div className="text-red-500">{error || '加载失败'}</div>
  </div>
);

// 未找到组件
const NotFoundComponent: ComponentType<{ uid: string }> = ({ uid }) => (
  <div className="flex items-center justify-center h-full">
    <div className="text-gray-500">组件 "{uid}" 未找到</div>
  </div>
);

// 注册组件（同步）
export function registerComponent(uid: string, component: ComponentType<any>) {
  componentRegistry.set(uid, { component });
}

// 注册组件（异步）
export function registerAsyncComponent(
  uid: string,
  loader: () => Promise<{ default: ComponentType<any> }>
) {
  const LazyComponent = lazy(loader);
  componentRegistry.set(uid, {
    component: LazyComponent,
    loader,
  });
}

// 获取组件
export function getComponent(uid: string): ComponentType<any> {
  const registration = componentRegistry.get(uid);
  
  if (!registration) {
    return (props: any) => <NotFoundComponent uid={uid} {...props} />;
  }
  
  return registration.component;
}

// 检查组件是否存在
export function hasComponent(uid: string): boolean {
  return componentRegistry.has(uid);
}

// 获取所有注册的组件ID
export function getRegisteredComponentIds(): string[] {
  return Array.from(componentRegistry.keys());
}

// 清空注册表
export function clearRegistry() {
  componentRegistry.clear();
}

// 导出基础组件
export { LoadingComponent, ErrorComponent, NotFoundComponent };

// 预注册一些基础组件
registerComponent('loading', LoadingComponent);
registerComponent('error', ErrorComponent);
registerComponent('not-found', NotFoundComponent);
