import { API_PREFIX } from '@/config';

const shouldPrependApiPrefix = (path: string) => {
  return path.startsWith('/console/api');
};

const removeApiPrefix = (path: string) => {
  if (path.startsWith('/console/api')) return path.substring('/console/api'.length);

  return path;
};

export const getIconPrefix = (path: string) => {
  if (shouldPrependApiPrefix(path)) return `${API_PREFIX}${removeApiPrefix(path)}`;
  return path;
};
