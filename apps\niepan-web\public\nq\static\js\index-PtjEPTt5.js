const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/Card1Component-BpJiucAK.js","static/js/quesheng-C5oWzANf.js","static/js/utils-ahBajhV-.js","static/js/useLinkHandler-E0UXn71M.js","static/js/index-C4ad4gme.js","static/js/pnpm-pnpm-B4aX-tnA.js","static/css/pnpm-pnpm-BdGb95pV.css","static/css/index-CKcViCi5.css","static/js/Card2Component-DMwG9Qz5.js","static/js/Card3Component-UYvuc4Zk.js","static/js/Card4Component-BinFKABo.js","static/js/Card5Component-ql_JMPrX.js","static/js/Card6Component-blEbV0BP.js","static/js/HotQuestionsComponent-DOJ9Luby.js","static/js/HighFrequencyKnowledgeVomponent-0IhHjlda.js","static/js/StatisticalDataComponent-Ca3pJLSo.js","static/js/LastQuestionComponent-Cpcqk2Qu.js","static/js/NotFund-nd1RMTR3.js"])))=>i.map(i=>d[i]);
import{aF as ne,aH as Q,r,R as be,ap as L,u as we,s as O,z as Ee,P as ke,Q as J,c,o as i,b as l,M as T,a as x,J as N,K as H,t as S,aa as t,w as Be,n as oe,k as Ue,v as De,A as I,ar as K}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as Qe}from"./quesheng-C5oWzANf.js";import{c as m,G as Le,H as Ie,I as Ke,J as Me,K as Re,L as Fe}from"./index-C4ad4gme.js";const qe="data:image/png;base64,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",Pe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAYAAADFeBvrAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEVSURBVHgB7dnLTcNAFIXhcw2sSQmmA+ggJZANj41DB0AHlEAHhiAUJSzGVICoJNABEsvIubl57J2JYvsmOt/GD40t/fLI8sgAERER7YcQQuftI3TRoAQ1GQ5D+j+Vic7k63UUntCQ2oKmR+hC0VnsC6SPhtQW1BYGeccg7xjkHYO8Y5B3BxckVQPex+G8hNwjkt04VbUP1JU/O1Egmn5nV72XmCsqgwajYmKbFG1Rfcxues+bDq+cciqrJUBr1J5uhOOqAbNSLxLBgyRyigi7mHJa6m//dsdTbluDcbizx5uvD3+y68szNICvbe8Y5B2DvGOQdwzyjkHe1RZUnqCwpcfy019UP3EIcvtHlNuKF0RERLSf5s8+RC3a1FpEAAAAAElFTkSuQmCC",je="/nq/static/png/INTELLIDO-CpEP0vEe.png",le=!1,Ve=[{id:1,title:"商机总金额",description:"商机总金额",icon_file:"http://araitest.aialign.com.cn/media/intellido.models/card/********/527d4f276b2cb209f0ab8ec5979aeaade2d26d76.png",ability:"business-amount-component",url:"http://crm.aialign.com.cn/opportunity-list"},{id:2,title:"汽轮机回款情况",description:"汽轮机回款情况",icon_file:"http://araitest.aialign.com.cn/media/intellido.models/card/********/73602211595819b2563dad64e8173cbfa465a845.png",ability:"projectfund-amount-component",url:"http://crm.aialign.com.cn/project-fund/list"},{id:3,title:"所有商机",description:"所有商机",icon_file:"",ability:"opportunity-list-component",url:"http://crm.aialign.com.cn/opportunity-list"},{id:4,title:"我的邮件",description:"新邮件：远景项目一期开发启动会",icon_file:"http://araitest.aialign.com.cn/media/intellido.models/card/********/e8c836d03ade0303264c769112b55d648eb84b2d.png",ability:"email-amount-component",url:"https://www.baidu.com"}],Ge=[{id:1,data:{uid:"hot-questions-component",data:{title:"热门问题",questions:[{question:"如何创建物料分类库并维护其结构？",type:"app",targetId:"4"},{question:"如何激活工作区？",type:"app",targetId:"4"},{question:"在风场GoldenBOM管理中，维护风场Golden的TBB小类对象和TBB实例结构的步骤分别是什么？",type:"app",targetId:"4"},{question:"PLM系统中，当用户休假或不方便登录系统时，如何将任务转派给他人？",type:"app",targetId:"4"},{question:"物料分类结构树中叶子节点和非叶子节点的作用分别是什么？",type:"worker",targetId:"4"}]},area:"card-1"}},{id:2,data:{uid:"high-frequency-knowledge-component",data:{title:"高频知识",documents:[{title:"MaxKB后端文档.docx",fileType:"docx",fileUrl:"http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx"},{title:"PLM21x_风场GoldenBOM管理用户操作手册.pptx",fileType:"pptx",fileUrl:"http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_风场GoldenBOM管理用户操作手册.pptx"},{title:"MaxKB后端文档.docx",fileType:"docx",fileUrl:"http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx"},{title:"PLM21x_风场GoldenBOM管理用户操作手册.pptx",fileType:"pptx",fileUrl:"http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_风场GoldenBOM管理用户操作手册.pptx"},{title:"MaxKB后端文档.docx",fileType:"docx",fileUrl:"http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx"}]},area:"card-2"}},{id:3,data:{uid:"statistical-data-component",data:{title:"统计数据",qaCount:16e5,kbaseCount:32,siteuserCount:4,documentCount:470},area:"card-3"}},{id:4,data:{uid:"last-question-component",data:{title:"最新问答",question:"怎样激活刚创建的工作区，以便其他成员可以访问？",answer:"激活工作区的具体步骤​：1.进入工作区特性页面​。找到需要激活的工作区，进入其特性页面。​2.执行激活操作​。在页面操作栏中点击【激活】，将工作区状态作区状",type:"app",targetId:"4"},area:"card-4"}},{id:5,data:{uid:"feishu-message-component",data:{title:"未读的",desc1:"项目评审会",desc2:"数据经纬技术架构和功能",desc3:"+15 更多",totalNum:3,linkUrl:"https://www.baidu.com",isLink:!0,imgUrl:"http://araitest.aialign.com.cn/media/intellido.models/card/********/f4ca93f4536c1e980afbbfa0d8dbae4865cb8a58.png"},area:"card-5"}},{id:6,data:{uid:"project-list-component",data:{title:"汽车行业",totalNum:2,linkUrl:"http://crm.aialign.com.cn/project/list",isLink:!0,imgUrl:"http://araitest.aialign.com.cn/media/intellido.models/card/********/3c315c60489a948d9f50b55c35f66c18a1c73549.png",projects:[{id:21,code:"1",name:"汽车行业哦",estimated_signing_date:"2024-12-13",estimated_delivery_date:null,account:{id:44,name:"1454",industry:"汽车"},opportunity:{id:26,name:"1212122"},creator:{id:1,name:"刘三岁"},type:null,business_type:"service",sales_side:null,estimated_contract_amount:null,execution_progress:"software_delivery",status:20,product_value_points:"W",product_technical_points:"EQ",existing_problems:"WQ",solutions:"QW",create_time:"2024-12-21 09:57",update_time:"2024-12-21 09:57",execution_progress_str:"软件交付"},{id:23,code:"1",name:"汽车行业哦1",estimated_signing_date:"2024-12-13",estimated_delivery_date:null,account:{id:44,name:"1454",industry:"汽车"},opportunity:{id:26,name:"1212122"},creator:{id:1,name:"刘三岁"},type:null,business_type:"service",sales_side:null,estimated_contract_amount:null,execution_progress:"software_delivery",status:20,product_value_points:"W",product_technical_points:"EQ",existing_problems:"WQ",solutions:"QW",create_time:"2024-12-21 09:57",update_time:"2024-12-21 09:57",execution_progress_str:"软件交付"}]},area:"card-6"}}],Je=new Map(Ge.map(a=>[a.id,a.data])),re={getAllCards:()=>Promise.resolve({data:Ve}),getCardData:a=>Promise.resolve({data:Je.get(a)})},Se="/nq/static/png/********-153443-BKxvaETg.png?a=1",Ye="/nq/static/png/********-180326-Cgnaqr7v.png?a=1",Oe={"business-amount-component":()=>m(()=>import("./Card1Component-BpJiucAK.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])).then(a=>a.default),"projectfund-amount-component":()=>m(()=>import("./Card2Component-DMwG9Qz5.js"),__vite__mapDeps([8,2,3,4,5,6,7])).then(a=>a.default),"opportunity-list-component":()=>m(()=>import("./Card3Component-UYvuc4Zk.js"),__vite__mapDeps([9,2,3,4,5,6,7])).then(a=>a.default),"email-amount-component":()=>m(()=>import("./Card4Component-BinFKABo.js"),__vite__mapDeps([10,3,4,5,6,7])).then(a=>a.default),"feishu-message-component":()=>m(()=>import("./Card5Component-ql_JMPrX.js"),__vite__mapDeps([11,1,2,3,4,5,6,7])).then(a=>a.default),"project-list-component":()=>m(()=>import("./Card6Component-blEbV0BP.js"),__vite__mapDeps([12,1,2,3,4,5,6,7])).then(a=>a.default),"hot-questions-component":()=>m(()=>import("./HotQuestionsComponent-DOJ9Luby.js"),__vite__mapDeps([13,5,6])).then(a=>a.default),"high-frequency-knowledge-component":()=>m(()=>import("./HighFrequencyKnowledgeVomponent-0IhHjlda.js"),__vite__mapDeps([14,5,6,4,7])).then(a=>a.default),"statistical-data-component":()=>m(()=>import("./StatisticalDataComponent-Ca3pJLSo.js"),__vite__mapDeps([15,2,5,6])).then(a=>a.default),"last-question-component":()=>m(()=>import("./LastQuestionComponent-Cpcqk2Qu.js"),__vite__mapDeps([16,5,6,4,7])).then(a=>a.default)},ie={template:`
      <div class="flex items-center justify-center h-full">
        <div class="text-gray-500">加载中...</div>
      </div>
    `},se={template:`
      <div class="flex items-center justify-center h-full">
        <div class="text-red-500">加载失败</div>
      </div>
    `},Te=a=>{const v=Oe[a];return v?ne({loader:v,loadingComponent:ie,errorComponent:se,delay:100,timeout:1e4,suspensible:!1,onError(u,h,s,f){f<=3?h():(console.error("Component load failed:",u),s())}}):ne({loader:()=>m(()=>import("./NotFund-nd1RMTR3.js"),__vite__mapDeps([17,4,5,6,7])),loadingComponent:ie,errorComponent:se})};function Ne(){const a=Q(null),v=r(null),u=r(""),h=e=>{v.value=e},s=Q(null),f=r(null),_=r(""),y=e=>{f.value=e},M=Q(null),b=r(null),R=r(""),w=e=>{b.value=e},F=Q(null),A=r(null),q=r(""),E=e=>{A.value=e},p=Q(null),k=r(null),B=r(""),P=e=>{k.value=e},j=Q(null),U=r(null),V=r(""),G=e=>{U.value=e},Y=e=>`${e}-${Date.now()}`;return{card1Component:a,card1ComponentData:v,card1ComponentKey:u,handleCard1DataUpdate:h,card2Component:s,card2ComponentData:f,card2ComponentKey:_,handleCard2DataUpdate:y,card3Component:M,card3ComponentData:b,card3ComponentKey:R,handleCard3DataUpdate:w,card4Component:F,card4ComponentData:A,card4ComponentKey:q,handleCard4DataUpdate:E,card5Component:p,card5ComponentData:k,card5ComponentKey:B,handleCard5DataUpdate:P,card6Component:j,card6ComponentData:U,card6ComponentKey:V,handleCard6DataUpdate:G,updatePreviewComponents:async e=>{if(!(e!=null&&e.uid))return;const C=Y(e.uid),g=Te(e.uid);await be(()=>{e.area=="card-1"?(v.value=e.data,u.value=C,a.value=L(g)):e.area=="card-2"?(f.value=e.data,_.value=C,s.value=L(g)):e.area=="card-3"?(b.value=e.data,R.value=C,M.value=L(g)):e.area=="card-4"?(A.value=e.data,q.value=C,F.value=L(g)):e.area=="card-5"?(k.value=e.data,B.value=C,p.value=L(g)):e.area=="card-6"&&(U.value=e.data,V.value=C,j.value=L(g))})},clearComponents:()=>{v.value=null,u.value="",a.value=null,f.value=null,_.value="",s.value=null,b.value=null,R.value="",M.value=null,A.value=null,q.value="",F.value=null,k.value=null,B.value="",p.value=null,U.value=null,V.value="",j.value=null}}}const He={class:"flex p-[15px] relative"},We=["href"],ze=l("div",{class:"w-[50px] h-[50px] rounded-full bg-white border border-solid border-[#E0E0E0] text-base text-[#C8C8C8] flex items-center justify-center z-[1]"},[l("img",{src:Pe,alt:"",class:"w-[26px] h-[26px] object-contain object-center"})],-1),Ze={key:1,class:"z-0 absolute top-0 right-0 w-[304px] h-20 rounded-[20px] bg-[#F5F5F5]"},Xe={key:2,class:"absolute top-[90px] right-0 bg-[#fff] rounded-[20px] border border-solid border-[#E0E0E0] w-[304px]"},$e={class:"max-h-[66vh] overflow-y-auto scrollbar-thin"},et=["href"],tt={class:"overflow-one text-sm"},at=l("div",{class:"px-[15px] pt-[7px] pb-[14px]"},[l("div",{class:"text-sm bg-[#F5F5F5] w-full h-[50px] rounded-[25px] block text-center leading-[50px]"}," 添加智能体 ")],-1),nt={class:"flex justify-center items-center flex-col max-w-[1100px] mx-auto"},ot=l("img",{src:je,alt:"",class:"w-[367px] h-[51px] mb-[45px]"},null,-1),lt=["src"],rt=l("rect",{opacity:"0.01",y:"0.5",width:"30",height:"30",fill:"black"},null,-1),it=["fill"],st={class:"flex justify-center items-center mt-10 mb-12"},dt=["title","href"],ct={class:"w-[70px] h-[70px] rounded-full flex justify-center items-center overflow-hidden"},ut=["src"],pt={key:1,src:Qe,alt:"",class:"w-full h-full object-contain object-center"},mt={class:"text-[#404040] text-base mt-[10px] max-w-[130px] truncate"},vt={class:"flex gap-4 w-full"},W=5,Ct={__name:"index",setup(a){const v=we(),u=r(!1),h=r([]),s=r(null),f=r(window.innerHeight),_=()=>{f.value=window.innerHeight},y=O(()=>h.value.slice(0,W)),M=O(()=>h.value.length>W),b=O(()=>h.value.length-W),R=()=>{u.value=!u.value},w=r(["#48AEF9","#627BF5","#ECBC49","#3579ED","#36C7F7"]),F=()=>w.value[Math.floor(Math.random()*w.value.length)],A=n=>v.resolve({name:"IndexWithQid",query:{qid:n.id}}).href;Ee(()=>{q(),ge(),_e(),window.addEventListener("resize",_)}),ke(()=>{window.removeEventListener("resize",_)});const q=async()=>{try{s.value=J.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const n=await Le();h.value=n.data,s.value.close()}finally{s.value.close()}},E=r(!1),p=r(""),k=n=>{if(n.key==="Enter"){if(n.preventDefault(),p.value.trim()==="")return;n.shiftKey?p.value+=`
`:B()}},B=()=>{p.value.trim()!=""&&v.push({name:"Chat",query:{question:encodeURIComponent(p.value)}})},{card1Component:P,card1ComponentData:j,card1ComponentKey:U,handleCard1DataUpdate:V,card2Component:G,card2ComponentData:Y,card2ComponentKey:z,handleCard2DataUpdate:Z,card3Component:e,card3ComponentData:C,card3ComponentKey:g,handleCard3DataUpdate:de,card4Component:X,card4ComponentData:ce,card4ComponentKey:ue,handleCard4DataUpdate:pe,card5Component:$,card5ComponentData:me,card5ComponentKey:ve,handleCard5DataUpdate:he,card6Component:ee,card6ComponentData:fe,card6ComponentKey:xe,handleCard6DataUpdate:Ce,updatePreviewComponents:te}=Ne(),ge=async()=>{try{s.value=J.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const d=le?await re.getAllCards():await Ie();for(var n=0;n<d.data.length;n++)Ae(d.data[n].id)}finally{s.value.close()}},Ae=async n=>{try{s.value=J.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const d=le?await re.getCardData(n):await Ke(n);te(d.data),te(P)}finally{s.value.close()}},ae=r([]),_e=async()=>{try{s.value=J.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const n=await Me();ae.value=n.data,s.value.close()}finally{s.value.close()}},ye=n=>{const d=`${n.name}-应用-INTELLIDO`,o=Fe(d),D=n.url.includes("?");return`${n.url}${D?"&":"?"}_t=${o}`};return(n,d)=>(i(),c("div",{onClick:d[3]||(d[3]=o=>u.value=!1)},[l("div",{class:oe(["w-full pr-[85px] text-right flex justify-end items-center",f.value<1400?"pt-[28px] pb-[100px]":"pt-[38px] pb-[112px]"])},[l("img",{src:qe,alt:"",class:"w-[50px] h-[50px] object-contain object-center relative z-70",style:T([{right:`-${12*y.value.length}px`}])},null,4),l("div",He,[(i(!0),c(N,null,H(y.value,(o,D)=>(i(),c("a",{class:"w-[50px] h-[50px] rounded-full border border-solid border-white text-base text-white flex items-center justify-center relative",target:"_blank",href:A(o),key:o.id,style:T([{"background-color":w.value[D%w.value.length]},D===0?{"z-index":60}:{},{right:`-${12+12*(y.value.length-D)}px`},{"z-index":`${10+10*(y.value.length-D)}`}])},S(t(Re)(o.title)),13,We))),128)),M.value?(i(),c("div",{key:0,class:"w-[50px] h-[50px] rounded-full bg-[#F3F3F3] border border-solid border-[#E0E0E0] text-base text-[#C8C8C8] flex items-center justify-center relative right-[-12px] z-10 cursor-pointer",onClick:Be(R,["stop"])}," + "+S(b.value),1)):x("",!0),ze,u.value?(i(),c("div",Ze)):x("",!0),u.value?(i(),c("div",Xe,[l("div",$e,[(i(!0),c(N,null,H(h.value,o=>(i(),c("a",{target:"_blank",class:"flex items-center py-[7px] px-[15px] hover:bg-[rgba(216,216,216,0.15)]",key:o.id,href:A(o)},[l("div",{class:"w-9 h-9 rounded-full text-white text-xs text-center leading-9 mr-[10px] shrink-0",style:T({backgroundColor:F()})}," 商机 ",4),l("div",tt,S(o.title),1)],8,et))),128)),at])])):x("",!0)])],2),l("div",nt,[ot,l("div",{class:oe(["w-full h-[80px] rounded-[25px] bg-white shadow-custom-shadow px-[25px] py-[15px] flex justify-between items-center border border-solid",E.value?"border-[rgba(18,155,254,1)]":"border-[rgba(229,229,229,1)]"])},[l("img",{src:E.value?t(Ye):t(Se),alt:"",class:"w-[50px] h-[50px] object-contain object-center relative t-[-2px]"},null,8,lt),Ue(l("textarea",{class:"h-full w-[100%] leading-[50px] border-none mx-[10px] outline-none resize-none overflow-auto scrollbar-hide",placeholder:"小A超级入口","onUpdate:modelValue":d[0]||(d[0]=o=>p.value=o),onFocus:d[1]||(d[1]=o=>E.value=!0),onBlur:d[2]||(d[2]=o=>E.value=!1),onKeydown:k,rows:"1"},null,544),[[De,p.value]]),(i(),c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"30",height:"31",viewBox:"0 0 30 31",fill:"none",class:"cursor-pointer",onClick:B},[rt,l("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M19.04 17.9258L26.6484 25.5342C26.7422 25.6279 26.7422 25.7803 26.6484 25.874L25.3711 27.1484C25.2773 27.2422 25.125 27.2422 25.0312 27.1484L17.4229 19.543C15.8965 20.7207 14.0332 21.3594 12.0703 21.3594C9.7207 21.3594 7.51465 20.4453 5.85645 18.7842C4.19824 17.126 3.28125 14.917 3.28125 12.5703C3.28125 10.2207 4.19531 8.01465 5.85645 6.35645C7.51465 4.69824 9.72363 3.78125 12.0703 3.78125C14.417 3.78125 16.626 4.69531 18.2842 6.35645C19.9424 8.01758 20.8594 10.2207 20.8594 12.5703C20.8594 14.5332 20.2207 16.3994 19.04 17.9258ZM12.0703 19.1328C13.8223 19.1328 15.4688 18.4502 16.7109 17.2109C17.9502 15.9717 18.6328 14.3223 18.6328 12.5703C18.6328 10.8184 17.9502 9.17188 16.7109 7.92969C15.4717 6.6875 13.8223 6.00781 12.0703 6.00781C10.3184 6.00781 8.67188 6.69043 7.42969 7.92969C6.19043 9.16895 5.50781 10.8184 5.50781 12.5703C5.50781 14.3223 6.19043 15.9688 7.42969 17.2109C8.67188 18.4502 10.3184 19.1328 12.0703 19.1328Z",fill:p.value?"rgba(52,58,63,1)":"rgba(162,169,176,1)"},null,8,it)]))],2),l("div",st,[(i(!0),c(N,null,H(ae.value,o=>(i(),c("a",{key:o.id,title:o.name,href:ye(o),target:"_blank",class:"text-center mr-16 flex flex-col items-center justify-center idapp-item"},[l("div",ct,[o.icon_file?(i(),c("img",{key:0,src:o.icon_file,alt:"",class:"w-full h-full object-contain object-center"},null,8,ut)):(i(),c("img",pt))]),l("div",mt,S(o.name),1)],8,dt))),128))]),l("div",vt,[t(P)?(i(),I(K(t(P)),{key:`card-1-${t(U)}`,data:t(j),"onUpdate:data":t(V),class:"flex-1 min-h-[180px]"},null,40,["data","onUpdate:data"])):x("",!0),t(G)?(i(),I(K(t(G)),{key:`card-2-${t(z)}`,data:t(Y),"onUpdate:data":t(Z),class:"flex-1 min-h-[180px]"},null,40,["data","onUpdate:data"])):x("",!0),t(e)?(i(),I(K(t(e)),{key:`card-3-${t(g)}`,data:t(C),"onUpdate:data":t(de),class:"flex-1 min-h-[180px]"},null,40,["data","onUpdate:data"])):x("",!0),t(X)?(i(),I(K(t(X)),{key:`card-4-${t(ue)}`,data:t(ce),"onUpdate:data":t(pe),class:"flex-1 min-h-[180px]"},null,40,["data","onUpdate:data"])):x("",!0),t($)?(i(),I(K(t($)),{key:`card-5-${t(ve)}`,data:t(me),"onUpdate:data":t(he),class:"flex-1 min-h-[180px]"},null,40,["data","onUpdate:data"])):x("",!0),t(ee)?(i(),I(K(t(ee)),{key:`card-6-${t(xe)}`,data:t(fe),"onUpdate:data":t(Ce),class:"flex-1 min-h-[180px]"},null,40,["data","onUpdate:data"])):x("",!0)])])]))}};export{Ct as default};
