import { memo } from 'react';
import { RiMicLine } from '@remixicon/react';
import type { EnableType } from '../../types';
import type { Theme } from '../../embedded-chatbot/theme/theme-context';
import Button from '@/app/components/base/button';
import ActionButton from '@/app/components/base/action-button';
import { FileUploaderInChatInput } from '@/app/components/base/file-uploader';
import type { FileUpload } from '@/app/components/base/features/types';
import cn from '@/utils/classnames';
import { Send } from '@/app/components/base/icons/src/public/apps';
import { StopCircleIcon } from '@/app/components/base/icons/src/public/common';
import { t } from 'i18next';
import Tooltip from '@/app/components/base/tooltip';

type OperationProps = {
  fileConfig?: FileUpload;
  speechToTextConfig?: EnableType;
  onShowVoiceInput?: () => void;
  onSend: () => void;
  theme?: Theme | null;
  noStopResponding?: boolean;
  isResponding?: boolean;
  onStopResponding?: () => void;
};
const Operation = ({
  ref,
  fileConfig,
  speechToTextConfig,
  onShowVoiceInput,
  onSend,
  theme,
  noStopResponding, // 新增 noStopResponding 属性
  isResponding, // 新增 isResponding 属性
  onStopResponding, // 新增 onStopResponding 属性
}: OperationProps & {
  ref: React.RefObject<HTMLDivElement>;
}) => {
  return (
    <div className={cn('flex shrink-0 items-center justify-end')}>
      <div className="flex items-center pl-1" ref={ref}>
        <div className="flex items-center space-x-1">
          {fileConfig?.enabled && <FileUploaderInChatInput fileConfig={fileConfig} />}
          {speechToTextConfig?.enabled && (
            <ActionButton size="l" onClick={onShowVoiceInput}>
              <RiMicLine className="h-5 w-5" />
            </ActionButton>
          )}
        </div>
        {!noStopResponding && isResponding && (
          <Tooltip
            popupContent={t('appDebug.operation.stopResponding')}
            popupClassName="bg-background-gradient-mask-transparent-dark"
          >
            <Button
              onClick={onStopResponding}
              className="ml-3 h-10 w-10 border-0 bg-components-input-bg-normal px-0"
            >
              <StopCircleIcon className="h-6 w-6" />
            </Button>
          </Tooltip>
        )}
        {!isResponding && !noStopResponding && (
          <Button
            className="ml-3 h-10 w-10 px-0"
            variant="primary"
            onClick={onSend}
            style={
              theme
                ? {
                    backgroundColor: theme.primaryColor,
                  }
                : {}
            }
          >
            <Send className="h-6 w-6" />
          </Button>
        )}
      </div>
    </div>
  );
};
Operation.displayName = 'Operation';

export default memo(Operation);
