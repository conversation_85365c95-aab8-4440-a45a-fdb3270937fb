import { memo, useCallback, useEffect, useState } from 'react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import {
  RiArrowDownSLine,
  RiPlayCircleLine,
  // RiPlayList2Line,
  // RiTerminalBoxLine,
} from '@remixicon/react';
import { useKeyPress } from 'ahooks';
import Toast from '../../base/toast';
import type { ModelAndParameter } from '../configuration/debug/types';
import { getKeyboardKeyCodeBySystem } from '../../workflow/utils';
import SuggestedAction from './suggested-action';
import PublishWithMultipleModel from './publish-with-multiple-model';
import Button from '@/app/components/base/button';
import {
  PortalToFollowElem,
  PortalToFollowElemContent,
  PortalToFollowElemTrigger,
} from '@/app/components/base/portal-to-follow-elem';
import { fetchInstalledAppList } from '@/service/explore';
import EmbeddedModal from '@/app/components/app/overview/embedded';
import { useStore as useAppStore } from '@/app/components/app/store';
import { useGetLanguage } from '@/context/i18n';
// import { CodeBrowser } from '@/app/components/base/icons/src/vender/line/development'
import WorkflowToolConfigureButton from '@/app/components/tools/workflow-tool/configure-button';
import type { InputVar } from '@/app/components/workflow/types';
import { appDefaultIconBackground } from '@/config';
import type { PublishWorkflowParams } from '@/types/workflow';
import { Application } from '@/app/components/base/icons/src/public/datasets';
// 选择权限
import { DatasetPermission } from '@/models/datasets';
import cn from 'classnames';
import { fetchAllDepartments, fetchDepartmentsByIds, fetchMembersByIds } from '@/service/common';
import { useAppContext } from '@/context/app-context';
import { AddOne } from '@/app/components/base/icons/src/public/datasets';
import MemberSelectModal from '@/app/components/datasets/settings/permission-selector/member-select-modal';
import { ChartGraph } from '@/app/components/base/icons/src/public/common';
import { RiCloseLine } from '@remixicon/react';
import { updateAppPermissionConfig } from '@/service/apps';

export type AppPublisherProps = {
  disabled?: boolean;
  publishDisabled?: boolean;
  publishedAt?: number;
  /** only needed in workflow / chatflow mode */
  draftUpdatedAt?: number;
  debugWithMultipleModel?: boolean;
  multipleModelConfigs?: ModelAndParameter[];
  /** modelAndParameter is passed when debugWithMultipleModel is true */
  onPublish?: (params?: any) => Promise<any> | any;
  onRestore?: () => Promise<any> | any;
  onToggle?: (state: boolean) => void;
  crossAxisOffset?: number;
  toolPublished?: boolean;
  inputs?: InputVar[];
  onRefreshData?: () => void;
  isPublished?: boolean;
  onSetPublishApp?: (params?: any) => Promise<any> | any;

  // 选择权限
  permission?: DatasetPermission;
  installedAppPermission?: DatasetPermission;
  // 保存为草稿
  onSaveDraft?: (params?: any) => Promise<any> | any;
};

const PUBLISH_SHORTCUT = ['⌘', '⇧', 'P'];
const SAVE_SHORTCUT = ['⌘', 'S'];

const AppPublisher = ({
  disabled = false,
  publishDisabled = false,
  publishedAt,
  draftUpdatedAt,
  debugWithMultipleModel = false,
  multipleModelConfigs = [],
  onPublish,
  // onRestore,
  onToggle,
  crossAxisOffset = 0,
  toolPublished,
  inputs,
  onRefreshData,
  isPublished,
  onSetPublishApp,
  // 选择权限
  permission,
  installedAppPermission,
  // 保存为草稿
  onSaveDraft,
}: AppPublisherProps) => {
  const { t } = useTranslation();
  const [published, setPublished] = useState(false);
  const { userProfile } = useAppContext();

  const [open, setOpen] = useState(false);
  const appDetail = useAppStore(state => state.appDetail);
  const setAppDetail = useAppStore(state => state.setAppDetail);
  const { app_base_url: appBaseURL = '', access_token: accessToken = '' } = appDetail?.site ?? {};
  const appMode =
    appDetail?.mode !== 'completion' && appDetail?.mode !== 'workflow' ? 'chat' : appDetail.mode;
  const appURL = `${appBaseURL}/${appMode}/${accessToken}`;
  // const isChatApp = ['chat', 'agent-chat', 'completion'].includes(appDetail?.mode || '');

  const [selectedMemberIDs, setSelectedMemberIDs] = useState<any[]>(
    appDetail?.partial_member_list || []
  );
  const [selectedDeptIDs, setSelectedDeptIDs] = useState<any[]>(
    appDetail?.partial_department_list || []
  );

  // 发布范围
  const [selectedInstalledAppMemberIDs, setSelectedInstalledAppMemberIDs] = useState<any[]>(
    appDetail?.installed_app_partial_member_list || []
  );
  const [selectedInstalledAppDeptIDs, setSelectedInstalledAppDeptIDs] = useState<any[]>(
    appDetail?.installed_app_partial_department_list || []
  );

  // 选择权限
  const [activeTab, setActiveTab] = useState<'edit' | 'scope'>('edit');
  const [newPermission, setNewPermission] = useState<DatasetPermission | undefined>(permission);
  const [newInstalledAppPermission, setNewInstalledAppPermission] = useState<
    DatasetPermission | undefined
  >(installedAppPermission);
  const isOnlyMe = newPermission === DatasetPermission.onlyMe;
  const isAllTeamMembers = newPermission === DatasetPermission.allTeamMembers;
  const isPartialMembers = newPermission === DatasetPermission.partialMembers;
  const [showMemberSelectModal, setShowMemberSelectModal] = useState(false);
  const isOnlyMeInstalledApp = newInstalledAppPermission === DatasetPermission.onlyMe;
  const isAllTeamMembersInstalledApp =
    newInstalledAppPermission === DatasetPermission.allTeamMembers;
  const isPartialMembersInstalledApp =
    newInstalledAppPermission === DatasetPermission.partialMembers;

  const language = useGetLanguage();
  const formatTimeFromNow = useCallback(
    (time: number) => {
      return dayjs(time)
        .locale(language === 'zh_Hans' ? 'zh-cn' : language.replace('_', '-'))
        .fromNow();
    },
    [language]
  );

  const handlePublish = useCallback(
    async (params?: ModelAndParameter | PublishWorkflowParams) => {
      try {
        await onPublish?.(params);
        setPublished(true);
      } catch {
        setPublished(false);
      } finally {
        setOpen(false);
        setActiveTab('edit');
      }
    },
    [onPublish]
  );

  // 保存为草稿
  const handleSaveDraft = useCallback(
    async (params?: ModelAndParameter | PublishWorkflowParams) => {
      try {
        await onSaveDraft?.(params);
        // setPublished(true);
      } catch {
        // setPublished(false);
      } finally {
        setOpen(false);
        setActiveTab('edit');
      }
    },
    [onSaveDraft]
  );

  // 上下架应用
  const handleSetPublishApp = useCallback(
    async (params: boolean) => {
      try {
        await onSetPublishApp?.(params);
      } catch (error) {
        console.error('Failed to set publish app:------', error);
      } finally {
        setOpen(false);
        setActiveTab('edit');
      }
    },
    [onSetPublishApp]
  );

  // const handleRestore = useCallback(async () => {
  //   try {
  //     await onRestore?.();
  //     setOpen(false);
  //   } catch {}
  // }, [onRestore]);

  const handleTrigger = useCallback(() => {
    const state = !open;

    if (disabled) {
      setOpen(false);
      return;
    }

    onToggle?.(state);
    setOpen(state);

    if (state) setPublished(false);
    else setActiveTab('edit');
  }, [disabled, onToggle, open]);

  const handleOpenInExplore = useCallback(async () => {
    try {
      const { installed_apps }: any = (await fetchInstalledAppList(appDetail?.id)) || {};
      if (installed_apps?.length > 0) window.open(`/nq/app-chat/${installed_apps[0].id}`, '_blank');
      // window.open(`/explore/installed/${installed_apps[0].id}`, '_blank');
      else throw new Error(t('workflow.common.errorPublish'));
      // else throw new Error('No app found in Explore');
    } catch (e: any) {
      Toast.notify({ type: 'error', message: `${e.message || e}` });
    }
  }, [appDetail?.id]);

  const [embeddingModalOpen, setEmbeddingModalOpen] = useState(false);

  useKeyPress(
    `${getKeyboardKeyCodeBySystem('ctrl')}.shift.p`,
    e => {
      e.preventDefault();
      if (publishDisabled || published) return;
      handlePublish();
    },
    { exactMatch: true, useCapture: true }
  );

  // 切换权限
  const handlePermissionChange = (newPermission: DatasetPermission) => {
    setNewPermission(newPermission);
  };

  // 发布范围权限
  const handleInstalledAppPermissionChange = (newPermission: DatasetPermission) => {
    setNewInstalledAppPermission(newPermission);
  };

  //   全部部门和部门员工
  const [departments, setDepartments] = useState<any[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [keywords, setKeywords] = useState('');

  const handleKeywordsChange = (value: string) => {
    setKeywords(value);
    // handleSearch(value);
  };

  // 已选择成员
  const [selectedMembers, setSelectedMembers] = useState<
    Array<{ id: string; name: string; role: string; uuid: string }>
  >([]);

  //   已选部门
  const [selectedDepts, setSelectedDepts] = useState<
    Array<{ id: string; name: string; uuid: string }>
  >([]);

  // 发布范围
  // 已选择成员
  const [selectedInstalledAppMembers, setSelectedInstalledAppMembers] = useState<
    Array<{ id: string; name: string; role: string; uuid: string }>
  >([]);

  //   已选部门
  const [selectedInstalledAppDepts, setSelectedInstalledAppDepts] = useState<
    Array<{ id: string; name: string; uuid: string }>
  >([]);

  //   获取部门和部门员工
  useEffect(() => {
    const loadDepartments = async () => {
      try {
        // const data = await fetchDepartments(searchKeyword);
        const data = await fetchAllDepartments({
          tenant_id: Number(userProfile.tenant?.id),
          no_position_members: 1,
        });

        // debugger;
        // 递归处理部门下的用户数据 把
        const preProcessData = (departments: any) => {
          if (departments && departments.length && departments.length > 0) {
            for (let di = 0; di < departments.length; di++) {
              const tmpMembers = departments[di].members as any;
              for (let mi = 0; mi < tmpMembers.length; mi++) {
                const tmpMember = tmpMembers[mi];
                tmpMember.uuid = tmpMember.staff_uuid;
              }
              if (departments[di].children && departments[di].children.length > 0)
                preProcessData(departments[di].children);
            }
          }
        };

        preProcessData(data);
        // debugger;
        setDepartments(data);
      } catch (error) {
        console.error('Failed to fetch departments:', error);
      }
    };
    loadDepartments();
  }, []);

  // 加载已选择的部门和用户详细信息
  useEffect(() => {
    let isCurrentEffect = true;
    const loadSelectedDetails = async () => {
      // 如果应用详情不存在，或者两个权限都不是部分成员权限，则返回
      if (
        !appDetail ||
        (permission !== DatasetPermission.partialMembers &&
          installedAppPermission !== DatasetPermission.partialMembers)
      )
        return;

      try {
        // 加载部门详细信息
        if (selectedDeptIDs.length > 0) {
          const deptData = await fetchDepartmentsByIds({
            tenant_id: Number(userProfile.tenant?.id),
            department_ids: selectedDeptIDs,
          });

          // 检查当前effect是否仍然是最新的
          if (!isCurrentEffect) return;

          if (deptData && deptData.length > 0) {
            const formattedDepts = deptData.map(dept => ({
              id: dept.id,
              name: dept.name,
              uuid: dept.uuid,
            }));
            setSelectedDepts(formattedDepts);
          }
        }
        // debugger;
        // 加载用户详细信息
        if (selectedMemberIDs.length > 0) {
          // debugger;
          const members = await fetchMembersByIds({
            tenant_id: Number(userProfile.tenant?.id),
            user_uuids: selectedMemberIDs,
          });

          // 检查当前effect是否仍然是最新的
          if (!isCurrentEffect) return;

          if (members && members.length > 0) {
            const formattedMembers = members.map(member => ({
              id: member.id,
              name: member.name,
              role: member.role,
              uuid: (member as any).user?.uuid as string,
            }));
            setSelectedMembers(formattedMembers);
          }
        }
        // 发布范围
        // 加载部门详细信息
        if (selectedInstalledAppDeptIDs.length > 0) {
          const installedAppDeptData = await fetchDepartmentsByIds({
            tenant_id: Number(userProfile.tenant?.id),
            department_ids: selectedInstalledAppDeptIDs,
          });

          // 检查当前effect是否仍然是最新的
          if (!isCurrentEffect) return;

          if (installedAppDeptData && installedAppDeptData.length > 0) {
            const formattedInstalledAppDepts = installedAppDeptData.map(dept => ({
              id: dept.id,
              name: dept.name,
              uuid: dept.uuid,
            }));
            setSelectedInstalledAppDepts(formattedInstalledAppDepts);
          }
        }
        // debugger;
        // 加载用户详细信息
        if (selectedInstalledAppMemberIDs.length > 0) {
          // debugger;
          const installedAppMembers = await fetchMembersByIds({
            tenant_id: Number(userProfile.tenant?.id),
            user_uuids: selectedInstalledAppMemberIDs,
          });

          // 检查当前effect是否仍然是最新的
          if (!isCurrentEffect) return;

          if (installedAppMembers && installedAppMembers.length > 0) {
            const formattedInstalledAppMembers = installedAppMembers.map(member => ({
              id: member.id,
              name: member.name,
              role: member.role,
              uuid: (member as any).user?.uuid as string,
            }));
            setSelectedInstalledAppMembers(formattedInstalledAppMembers);
          }
        }
      } catch (error) {
        console.error('Failed to load selected details:', error);
      }
    };
    loadSelectedDetails();
    return () => {
      isCurrentEffect = false;
    };
  }, [appDetail, userProfile.tenant?.id]);

  //   更新已选员工
  const resetSelectedMembers = (selectedMemberIds: string[] = []) => {
    const findMembers = (depts: any[]): any[] => {
      let members: any[] = [];
      for (const dept of depts) {
        if (dept.members) members = members.concat(dept.members);

        if (dept.children) members = members.concat(findMembers(dept.children));
      }
      return members;
    };

    const allMembers = findMembers(departments);

    if (activeTab === 'edit') {
      // 使用Set进行去重，避免重复添加相同uuid的用户
      const uniqueMembers = new Map();
      // 首先尝试通过uuid匹配
      selectedMemberIds.forEach(memberId => {
        const memberByUuid = allMembers.find(m => (m.uuid || m.staff_uuid) === memberId);
        if (memberByUuid && !uniqueMembers.has(memberByUuid.uuid || memberByUuid.staff_uuid))
          uniqueMembers.set(memberByUuid.uuid || memberByUuid.staff_uuid, memberByUuid);
      });

      // 如果通过uuid没找到，再尝试通过id匹配（兼容旧数据）
      selectedMemberIds.forEach(memberId => {
        if (uniqueMembers.size < selectedMemberIds.length) {
          const memberById = allMembers.find(m => m.id === memberId);
          if (
            memberById &&
            !uniqueMembers.has(memberById.uuid || memberById.staff_uuid || memberById.id)
          ) {
            uniqueMembers.set(
              memberById.uuid || memberById.staff_uuid || memberById.id,
              memberById
            );
          }
        }
      });

      setSelectedMembers(Array.from(uniqueMembers.values()));
    } else if (activeTab === 'scope') {
      // 使用Set进行去重，避免重复添加相同uuid的用户
      const uniqueMembers = new Map();
      // 首先尝试通过uuid匹配
      selectedMemberIds.forEach(memberId => {
        const memberByUuid = allMembers.find(m => (m.uuid || m.staff_uuid) === memberId);
        if (memberByUuid && !uniqueMembers.has(memberByUuid.uuid || memberByUuid.staff_uuid))
          uniqueMembers.set(memberByUuid.uuid || memberByUuid.staff_uuid, memberByUuid);
      });

      // 如果通过uuid没找到，再尝试通过id匹配（兼容旧数据）
      selectedMemberIds.forEach(memberId => {
        if (uniqueMembers.size < selectedMemberIds.length) {
          const memberById = allMembers.find(m => m.id === memberId);
          if (
            memberById &&
            !uniqueMembers.has(memberById.uuid || memberById.staff_uuid || memberById.id)
          ) {
            uniqueMembers.set(
              memberById.uuid || memberById.staff_uuid || memberById.id,
              memberById
            );
          }
        }
      });

      setSelectedInstalledAppMembers(Array.from(uniqueMembers.values()));
    }
  };

  // 在组件顶层声明ref
  const prevMemberIdsRef = React.useRef('');
  const prevDeptIdsRef = React.useRef('');

  useEffect(() => {
    // 使用JSON.stringify比较，避免不必要的回调
    const memberIds =
      selectedMembers && selectedMembers.length > 0 ? selectedMembers.map(m => m.uuid || m.id) : [];

    const currentMemberIds = JSON.stringify(memberIds);

    if (prevMemberIdsRef.current !== currentMemberIds) {
      prevMemberIdsRef.current = currentMemberIds;
      setSelectedMemberIDs(memberIds);
    }

    // 同样处理部门
    const deptIds =
      selectedDepts && selectedDepts.length > 0 ? selectedDepts.map(d => d.uuid || d.id) : [];

    const currentDeptIds = JSON.stringify(deptIds);

    if (prevDeptIdsRef.current !== currentDeptIds) {
      prevDeptIdsRef.current = currentDeptIds;
      setSelectedDeptIDs(deptIds);
    }

    // --------------------------发布范围-----------------------------------
    const installedAppMemberIds =
      selectedInstalledAppMembers && selectedInstalledAppMembers.length > 0
        ? selectedInstalledAppMembers.map(m => m.uuid || m.id)
        : [];

    const currentInstalledAppMemberIds = JSON.stringify(memberIds);

    if (prevMemberIdsRef.current !== currentInstalledAppMemberIds) {
      prevMemberIdsRef.current = currentInstalledAppMemberIds;
      setSelectedInstalledAppMemberIDs(installedAppMemberIds);
    }

    // 同样处理部门
    const installedAppDeptIds =
      selectedInstalledAppDepts && selectedInstalledAppDepts.length > 0
        ? selectedInstalledAppDepts.map(d => d.uuid || d.id)
        : [];

    const currentInstalledAppDeptIds = JSON.stringify(deptIds);

    if (prevDeptIdsRef.current !== currentInstalledAppDeptIds) {
      prevDeptIdsRef.current = currentInstalledAppDeptIds;
      setSelectedInstalledAppDeptIDs(installedAppDeptIds);
    }
  }, [
    selectedMembers,
    selectedDepts,
    setSelectedDeptIDs,
    setSelectedMemberIDs,
    selectedInstalledAppMembers,
    selectedInstalledAppDepts,
    setSelectedInstalledAppDeptIDs,
    setSelectedInstalledAppMemberIDs,
  ]);

  //   更新已选部门
  const resetSelectedDepts = (selectedDeptIds: string[] = []) => {
    if (activeTab === 'edit') {
      // 使用Map进行去重，避免重复添加相同uuid的部门
      const uniqueDepts = new Map();

      selectedDeptIds.forEach(deptId => {
        if (!uniqueDepts.has(deptId)) {
          const findDept = (depts: any[]): any => {
            for (const dept of depts) {
              // 优先使用uuid匹配
              if ((dept.uuid || dept.id) === deptId) return dept;
              // 兼容旧数据，使用id匹配
              if (dept.id === deptId && !dept.uuid) return dept;

              if (dept.children) {
                const found = findDept(dept.children);
                if (found) return found;
              }
            }
            return null;
          };

          const dept = findDept(departments);
          if (dept) uniqueDepts.set(deptId, dept);
        }
      });
      setSelectedDepts(Array.from(uniqueDepts.values()));
    } else if (activeTab === 'scope') {
      // 使用Map进行去重，避免重复添加相同uuid的部门
      const uniqueInstalledAppDepts = new Map();

      selectedDeptIds.forEach(deptId => {
        if (!uniqueInstalledAppDepts.has(deptId)) {
          const findInstalledAppDept = (depts: any[]): any => {
            for (const dept of depts) {
              // 优先使用uuid匹配
              if ((dept.uuid || dept.id) === deptId) return dept;
              // 兼容旧数据，使用id匹配
              if (dept.id === deptId && !dept.uuid) return dept;

              if (dept.children) {
                const found = findInstalledAppDept(dept.children);
                if (found) return found;
              }
            }
            return null;
          };

          const dept = findInstalledAppDept(departments);
          if (dept) uniqueInstalledAppDepts.set(deptId, dept);
        }
      });
      setSelectedInstalledAppDepts(Array.from(uniqueInstalledAppDepts.values()));
    }
  };

  // 保存权限
  const handleSavePermissions = async () => {
    try {
      const requestParams = {
        appId: appDetail?.id,
        body: {
          permission: newPermission,
          installed_app_permission: newInstalledAppPermission,
        },
      } as any;

      // debugger;
      if (newPermission === DatasetPermission.partialMembers) {
        // 使用 selectedMemberIDs 作为 ID 列表
        requestParams.body.partial_member_list = selectedMembers.map(({ uuid }) => {
          return {
            user_id: uuid,
            role: '',
          };
        });
        // 使用 selectedDeptIDs 作为部门 ID 列表
        requestParams.body.partial_department_list = selectedDepts.map(dep => {
          return {
            department_id: dep.uuid,
          };
        });
      }
      if (newInstalledAppPermission === DatasetPermission.partialMembers) {
        // 使用 selectedMemberIDs 作为 ID 列表
        requestParams.body.installed_app_partial_member_list = selectedInstalledAppMembers.map(
          ({ uuid }) => {
            return {
              user_id: uuid,
              role: '',
            };
          }
        );
        // 使用 selectedDeptIDs 作为部门 ID 列表
        requestParams.body.installed_app_partial_department_list = selectedInstalledAppDepts.map(
          dep => {
            return {
              department_id: dep.uuid,
            };
          }
        );
      }
      await updateAppPermissionConfig(requestParams);

      Toast.notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') });
      // Fetch latest app details after successful publish
      // const res = await fetchAppDetail({ url: '/apps', id: String(appDetail?.id) });
      // setAppDetail(res);
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Failed to update app publish status:', error);
      Toast.notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') });
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } finally {
      setOpen(false);
      setActiveTab('edit');
    }
  };

  return (
    <>
      <PortalToFollowElem
        open={open}
        // onOpenChange={setOpen}
        placement="bottom-end"
        offset={{
          mainAxis: 4,
          crossAxis: crossAxisOffset,
        }}
      >
        <PortalToFollowElemTrigger onClick={handleTrigger}>
          <Button variant="primary" className="p-2" disabled={disabled}>
            {t('workflow.common.publish')}
            <RiArrowDownSLine className="h-4 w-4 text-components-button-primary-text" />
          </Button>
        </PortalToFollowElemTrigger>
        <PortalToFollowElemContent className="!left-auto !right-4 !top-[112px] z-[11] h-[90vh] !transform-none">
          <div className="flex h-full  w-[400px] flex-col justify-between rounded-[20px] border-[0.5px] border-components-panel-border bg-components-panel-bg p-6 shadow-xl shadow-shadow-shadow-5">
            <div className="flex grow flex-col overflow-y-auto">
              <div className="flex-none border-b-[0.5px] border-t-divider-regular pb-[10px]">
                {debugWithMultipleModel ? (
                  <PublishWithMultipleModel
                    multipleModelConfigs={multipleModelConfigs}
                    onSelect={item => handlePublish(item)}
                    // textGenerationModelList={textGenerationModelList}
                  />
                ) : (
                  <Button
                    variant="primary"
                    className="mb-[10px] w-full rounded-md !shadow-none"
                    onClick={() => handlePublish()}
                    disabled={publishDisabled || published}
                  >
                    {published ? (
                      t('workflow.common.published')
                    ) : (
                      <div className="flex gap-1">
                        {isPublished ? (
                          <span>{t('workflow.common.publishUpdate1')}</span>
                        ) : (
                          <span>{t('workflow.common.publishUpdate')}</span>
                        )}

                        <div className="flex gap-0.5">
                          {PUBLISH_SHORTCUT.map(key => (
                            <span
                              key={key}
                              className="system-kbd h-4 w-4 rounded-[4px] bg-components-kbd-bg-white text-text-primary-on-surface"
                            >
                              {key}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </Button>
                )}
                {/* 保存草稿， */}
                {appDetail?.mode !== 'workflow' && appDetail?.mode !== 'advanced-chat' && (
                  <Button
                    variant="primary"
                    className="mb-[10px] w-full rounded-md bg-state-accent-active text-text-accent !shadow-none hover:bg-state-accent-active hover:text-text-accent"
                    onClick={() => handleSaveDraft()}
                    disabled={publishDisabled || published}
                  >
                    <div className="flex gap-1">
                      <span>{t('workflow.common.saveDraft')}</span>
                      <div className="flex gap-0.5">
                        {SAVE_SHORTCUT.map(key => (
                          <span
                            key={key}
                            className="system-kbd h-4 w-4 rounded-[4px] bg-[#129bfe1f] text-text-accent"
                          >
                            {key}
                          </span>
                        ))}
                      </div>
                    </div>
                  </Button>
                )}
                <div className="flex items-center justify-between">
                  {/* 运行webapp */}
                  <SuggestedAction
                    disabled={!publishedAt}
                    link={appURL}
                    icon={<RiPlayCircleLine className="h-4 w-4" />}
                    className="flex-1"
                  >
                    {t('workflow.common.runWebApp')}
                  </SuggestedAction>
                  {/* 在广场中打开 */}
                  <SuggestedAction
                    onClick={() => {
                      publishedAt && handleOpenInExplore();
                    }}
                    disabled={!publishedAt}
                    icon={<Application className="h-4 w-4" />}
                    className="ml-[10px] flex-1"
                  >
                    {t('workflow.common.openInExplore')}
                  </SuggestedAction>
                </div>
                {/* 已发布的应用可以点击下架应用 */}
                {isPublished ? (
                  <Button
                    variant="primary"
                    className="mb-[10px] w-full rounded-md bg-components-button-tertiary-bg text-text-quaternary !shadow-none hover:bg-components-button-tertiary-bg hover:text-text-quaternary"
                    onClick={() => handleSetPublishApp(false)}
                    // disabled={publishDisabled || published}
                  >
                    <div className="flex gap-1">
                      <img src="/datasets/remove-app.png" alt="" />
                      <span>{t('workflow.common.removeApp')}</span>
                    </div>
                  </Button>
                ) : null}
                {publishedAt ? (
                  <div className="mb-[6px] mt-[6px] flex items-center justify-between">
                    <div className="system-sm-medium flex w-full items-center justify-between text-text-secondary">
                      {/* {t('workflow.common.publishedAt')} */}
                      <span className="text-text-tertiary">
                        {t('workflow.common.latestPublished')}
                      </span>
                      <span className="ml-2 text-text-primary">
                        {formatTimeFromNow(publishedAt)}
                      </span>
                    </div>
                    {/* {isChatApp && (
                      <Button
                        variant="secondary-accent"
                        size="small"
                        onClick={handleRestore}
                        disabled={published}
                      >
                        {t('workflow.common.restore')}
                      </Button>
                    )} */}
                  </div>
                ) : (
                  <div className="system-sm-medium mb-[6px] mt-[6px] flex items-center justify-between text-text-secondary">
                    {t('workflow.common.autoSaved')} ·{' '}
                    {Boolean(draftUpdatedAt) && formatTimeFromNow(draftUpdatedAt!)}
                  </div>
                )}
              </div>
              <div className="flex grow flex-col py-4 pb-0">
                <div className="flex-none text-sm text-text-primary">
                  {t('workflow.common.permissionTitle')}
                </div>
                <div className="flex grow flex-col">
                  <div className="flex flex-none gap-4 border-b border-t-divider-regular py-[10px]">
                    <div
                      className={`relative cursor-pointer px-3 text-sm ${
                        activeTab === 'edit' ? 'text-text-accent' : 'text-text-primary'
                      }`}
                      onClick={() => setActiveTab('edit')}
                    >
                      {t('workflow.common.backendeditable')}
                      <div
                        className={`absolute bottom-[-10px] left-0 right-0 mx-auto h-[2px] w-6 rounded-sm ${
                          activeTab === 'edit' ? 'bg-components-button-primary-bg' : ''
                        }`}
                      ></div>
                    </div>
                    <div
                      className={`relative cursor-pointer px-3 text-sm ${
                        activeTab === 'scope' ? 'text-text-accent' : 'text-text-primary'
                      }`}
                      onClick={() => setActiveTab('scope')}
                    >
                      {t('workflow.common.releasescope')}
                      <div
                        className={`absolute bottom-[-10px] left-0 right-0 mx-auto h-[2px] w-6 rounded-sm ${
                          activeTab === 'scope' ? 'bg-components-button-primary-bg' : ''
                        }`}
                      ></div>
                    </div>
                  </div>
                  <div className="mt-4 grow">
                    <div className="flex h-full flex-col justify-between rounded-xl bg-[#F7F9FC] p-4">
                      {activeTab === 'edit' && (
                        <div className="max-h-[90%] overflow-y-auto">
                          <div className="text-sm text-text-tertiary">
                            {t('workflow.common.editDescription')}
                          </div>
                          <div className="my-3 mt-2 flex flex-col">
                            <div
                              className="flex cursor-pointer items-center"
                              onClick={() => {
                                handlePermissionChange(DatasetPermission.onlyMe);
                              }}
                            >
                              <div className="flex h-[42px] items-center gap-2">
                                <input
                                  type="radio"
                                  name="editPermission"
                                  className="h-[14px] w-[14px] cursor-pointer border-components-panel-border text-components-button-primary-bg focus:ring-components-button-primary-bg"
                                  checked={isOnlyMe}
                                  onChange={() => {
                                    handlePermissionChange(DatasetPermission.onlyMe);
                                  }}
                                />
                                <div
                                  className={cn(
                                    'mr-2 grow text-sm leading-5',
                                    isOnlyMe ? 'text-text-accent' : 'text-text-secondary'
                                  )}
                                >
                                  {t('datasetSettings.form.permissionsOnlyMe')}
                                </div>
                              </div>
                            </div>
                            <div
                              className="flex cursor-pointer items-center"
                              onClick={() => {
                                handlePermissionChange(DatasetPermission.allTeamMembers);
                                // setOpen(false);
                              }}
                            >
                              <div className="flex h-[42px] items-center gap-2">
                                <input
                                  type="radio"
                                  name="editPermission"
                                  className="h-[14px] w-[14px] cursor-pointer border-components-panel-border text-components-button-primary-bg focus:ring-components-button-primary-bg"
                                  checked={isAllTeamMembers}
                                  onChange={() => {
                                    handlePermissionChange(DatasetPermission.allTeamMembers);
                                  }}
                                />
                                <div
                                  className={cn(
                                    'mr-2 grow text-sm leading-5',
                                    isAllTeamMembers ? 'text-text-accent' : 'text-text-secondary'
                                  )}
                                >
                                  {t('datasetSettings.form.permissionsAllMember')}
                                </div>
                              </div>
                            </div>
                            <div
                              className="flex cursor-pointer items-center justify-between"
                              onClick={() => {
                                handlePermissionChange(DatasetPermission.partialMembers);
                                // setShowMemberSelectModal(true);
                              }}
                            >
                              <div className="flex h-[42px] items-center gap-2">
                                <input
                                  type="radio"
                                  name="editPermission"
                                  className="h-[14px] w-[14px] cursor-pointer border-components-panel-border text-components-button-primary-bg focus:ring-components-button-primary-bg"
                                  checked={isPartialMembers}
                                  onChange={() => {
                                    handlePermissionChange(DatasetPermission.partialMembers);
                                  }}
                                />
                                <div
                                  className={cn(
                                    'group mr-2 flex grow items-center text-sm leading-5',
                                    isPartialMembers ? 'text-text-accent' : 'text-text-secondary'
                                  )}
                                >
                                  {t('datasetSettings.form.permissionsInvitedMembers')}
                                </div>
                              </div>
                              <Button
                                type="button"
                                size="small"
                                className={`ml-2 border-none bg-transparent !p-0 text-xs text-text-accent shadow-none ${
                                  isPartialMembers ? 'flex' : 'hidden'
                                }`}
                                onClick={() => {
                                  // 打开选择弹窗，保留当前已选择的部门和人员
                                  setShowMemberSelectModal(true);
                                }}
                              >
                                <AddOne className="mr-0.5 h-4 w-4" />{' '}
                                {t('common.members.addMembers')}
                              </Button>
                            </div>
                            {isPartialMembers && (
                              <>
                                <div className="mr-2 flex grow flex-wrap gap-1 truncate text-sm leading-5 text-components-input-text-filled">
                                  {selectedDepts.map(dept => {
                                    return (
                                      <div
                                        key={dept.id}
                                        className="flex items-center gap-1 rounded-[42px] bg-background-default-dodge py-[2px] pl-[2px] pr-1.5"
                                      >
                                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[linear-gradient(129deg,_#129BFF_9.52%,_#3E60E9_92.09%)]">
                                          <ChartGraph className="h-3 w-3" />
                                        </div>
                                        <span className="text-xs text-text-primary">
                                          {dept.name}
                                        </span>

                                        <Button
                                          type="button"
                                          size="small"
                                          className="h-4 w-4 rounded-full border-transparent bg-transparent p-0 font-semibold text-text-tertiary shadow-none hover:border-transparent hover:bg-transparent"
                                          onClick={e => {
                                            e.stopPropagation();
                                            // 使用uuid来唯一标识部门
                                            const deptUuid = dept.uuid || dept.id;
                                            const newDepts = selectedDepts.filter(
                                              d => (d.uuid || d.id) !== deptUuid
                                            );
                                            setSelectedDepts([...newDepts]);
                                            if (setSelectedDeptIDs)
                                              setSelectedDeptIDs(newDepts.map(d => d.uuid || d.id));
                                          }}
                                        >
                                          <RiCloseLine className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    );
                                  })}
                                  {selectedMembers.map((member, index) => (
                                    <div
                                      key={index}
                                      className="flex items-center gap-1 rounded-[42px] bg-background-default-dodge py-[2px] pl-[2px] pr-1.5"
                                    >
                                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[linear-gradient(157deg,_#5F6C90_12.46%,_#393D49_85.12%)]">
                                        <span className="text-xs font-medium text-white">
                                          {member.name.trim().charAt(0).toUpperCase()}
                                        </span>
                                      </div>
                                      <span className="text-xs text-text-primary">
                                        {member.name}
                                      </span>
                                      <Button
                                        type="button"
                                        size="small"
                                        onClick={e => {
                                          e.stopPropagation();
                                          // 使用uuid来唯一标识用户
                                          const memberUuid = member.uuid || member.id;
                                          const newMembers = selectedMembers.filter(
                                            m => (m.uuid || m.id) !== memberUuid
                                          );
                                          setSelectedMembers([...newMembers]);
                                          setSelectedMemberIDs(newMembers.map(m => m.uuid || m.id));
                                        }}
                                        className="h-4 w-4 rounded-full border-transparent bg-transparent p-0 font-semibold text-text-tertiary shadow-none hover:border-transparent hover:bg-transparent"
                                      >
                                        <RiCloseLine className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      )}
                      {/* 发布范围权限 */}
                      {activeTab === 'scope' && (
                        <div className="max-h-[90%] overflow-y-auto">
                          <div className="text-sm text-text-tertiary">
                            {t('workflow.common.scopeDescription')}
                          </div>
                          <div className="my-3 mt-2 flex flex-col">
                            <div
                              className="flex cursor-pointer items-center"
                              onClick={() => {
                                handleInstalledAppPermissionChange(DatasetPermission.onlyMe);
                              }}
                            >
                              <div className="flex h-[42px] items-center gap-2">
                                <input
                                  type="radio"
                                  name="editPermission"
                                  className="h-[14px] w-[14px] cursor-pointer border-components-panel-border text-components-button-primary-bg focus:ring-components-button-primary-bg"
                                  checked={isOnlyMeInstalledApp}
                                  onChange={() => {
                                    handleInstalledAppPermissionChange(DatasetPermission.onlyMe);
                                  }}
                                />
                                <div
                                  className={cn(
                                    'mr-2 grow text-sm leading-5',
                                    isOnlyMeInstalledApp
                                      ? 'text-text-accent'
                                      : 'text-text-secondary'
                                  )}
                                >
                                  {t('datasetSettings.form.permissionsOnlyMe')}
                                </div>
                              </div>
                            </div>
                            <div
                              className="flex cursor-pointer items-center"
                              onClick={() => {
                                handleInstalledAppPermissionChange(
                                  DatasetPermission.allTeamMembers
                                );
                                // setOpen(false);
                              }}
                            >
                              <div className="flex h-[42px] items-center gap-2">
                                <input
                                  type="radio"
                                  name="editPermission"
                                  className="h-[14px] w-[14px] cursor-pointer border-components-panel-border text-components-button-primary-bg focus:ring-components-button-primary-bg"
                                  checked={isAllTeamMembersInstalledApp}
                                  onChange={() => {
                                    handleInstalledAppPermissionChange(
                                      DatasetPermission.allTeamMembers
                                    );
                                  }}
                                />
                                <div
                                  className={cn(
                                    'mr-2 grow text-sm leading-5',
                                    isAllTeamMembersInstalledApp
                                      ? 'text-text-accent'
                                      : 'text-text-secondary'
                                  )}
                                >
                                  {t('datasetSettings.form.permissionsAllMember')}
                                </div>
                              </div>
                            </div>
                            <div
                              className="flex cursor-pointer items-center justify-between"
                              onClick={() => {
                                handleInstalledAppPermissionChange(
                                  DatasetPermission.partialMembers
                                );
                                // setShowMemberSelectModal(true);
                              }}
                            >
                              <div className="flex h-[42px] items-center gap-2">
                                <input
                                  type="radio"
                                  name="editPermission"
                                  className="h-[14px] w-[14px] cursor-pointer border-components-panel-border text-components-button-primary-bg focus:ring-components-button-primary-bg"
                                  checked={isPartialMembersInstalledApp}
                                  onChange={() => {
                                    handleInstalledAppPermissionChange(
                                      DatasetPermission.partialMembers
                                    );
                                  }}
                                />
                                <div
                                  className={cn(
                                    'group mr-2 flex grow items-center text-sm leading-5',
                                    isPartialMembersInstalledApp
                                      ? 'text-text-accent'
                                      : 'text-text-secondary'
                                  )}
                                >
                                  {t('datasetSettings.form.permissionsInvitedMembers')}
                                </div>
                              </div>
                              <Button
                                type="button"
                                size="small"
                                className={`ml-2 border-none bg-transparent !p-0 text-xs text-text-accent shadow-none ${
                                  isPartialMembersInstalledApp ? 'flex' : 'hidden'
                                }`}
                                onClick={() => {
                                  // 打开选择弹窗，保留当前已选择的部门和人员
                                  setShowMemberSelectModal(true);
                                }}
                              >
                                <AddOne className="mr-0.5 h-4 w-4" />{' '}
                                {t('common.members.addMembers')}
                              </Button>
                            </div>
                            {isPartialMembersInstalledApp && (
                              <>
                                <div className="mr-2 flex grow flex-wrap gap-1 truncate text-sm leading-5 text-components-input-text-filled">
                                  {selectedInstalledAppDepts.map(dept => {
                                    return (
                                      <div
                                        key={dept.id}
                                        className="flex items-center gap-1 rounded-[42px] bg-background-default-dodge py-[2px] pl-[2px] pr-1.5"
                                      >
                                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[linear-gradient(129deg,_#129BFF_9.52%,_#3E60E9_92.09%)]">
                                          <ChartGraph className="h-3 w-3" />
                                        </div>
                                        <span className="text-xs text-text-primary">
                                          {dept.name}
                                        </span>

                                        <Button
                                          type="button"
                                          size="small"
                                          className="h-4 w-4 rounded-full border-transparent bg-transparent p-0 font-semibold text-text-tertiary shadow-none hover:border-transparent hover:bg-transparent"
                                          onClick={e => {
                                            e.stopPropagation();
                                            // 使用uuid来唯一标识部门
                                            const deptUuid = dept.uuid || dept.id;
                                            const newDepts = selectedInstalledAppDepts.filter(
                                              d => (d.uuid || d.id) !== deptUuid
                                            );
                                            setSelectedInstalledAppDepts([...newDepts]);
                                            if (setSelectedInstalledAppDeptIDs) {
                                              setSelectedInstalledAppDeptIDs(
                                                newDepts.map(d => d.uuid || d.id)
                                              );
                                            }
                                          }}
                                        >
                                          <RiCloseLine className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    );
                                  })}
                                  {selectedInstalledAppMembers.map((member, index) => (
                                    <div
                                      key={index}
                                      className="flex items-center gap-1 rounded-[42px] bg-background-default-dodge py-[2px] pl-[2px] pr-1.5"
                                    >
                                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[linear-gradient(157deg,_#5F6C90_12.46%,_#393D49_85.12%)]">
                                        <span className="text-xs font-medium text-white">
                                          {member.name.trim().charAt(0).toUpperCase()}
                                        </span>
                                      </div>
                                      <span className="text-xs text-text-primary">
                                        {member.name}
                                      </span>
                                      <Button
                                        type="button"
                                        size="small"
                                        onClick={e => {
                                          e.stopPropagation();
                                          // 使用uuid来唯一标识用户
                                          const memberUuid = member.uuid || member.id;
                                          const newMembers = selectedInstalledAppMembers.filter(
                                            m => (m.uuid || m.id) !== memberUuid
                                          );
                                          setSelectedInstalledAppMembers([...newMembers]);
                                          setSelectedInstalledAppMemberIDs(
                                            newMembers.map(m => m.uuid || m.id)
                                          );
                                        }}
                                        className="h-4 w-4 rounded-full border-transparent bg-transparent p-0 font-semibold text-text-tertiary shadow-none hover:border-transparent hover:bg-transparent"
                                      >
                                        <RiCloseLine className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      )}
                      <div className="text-right">
                        <Button
                          type="button"
                          size="small"
                          className={`ml-2 h-8 min-w-20 border-util-colors-indigo-indigo-600 bg-transparent px-3 text-xs text-text-accent hover:border-util-colors-indigo-indigo-600`}
                          onClick={() => {
                            handleSavePermissions();
                          }}
                        >
                          {t('workflow.common.savePermissions')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* 发布为工具 */}
            {appDetail?.mode === 'workflow' && (
              <div className="flex-none">
                <WorkflowToolConfigureButton
                  disabled={!publishedAt}
                  published={!!toolPublished}
                  detailNeedUpdate={!!toolPublished && published}
                  workflowAppId={appDetail?.id}
                  icon={{
                    content: (appDetail.icon_type === 'image' ? '🤖' : appDetail?.icon) || '🤖',
                    background:
                      (appDetail.icon_type === 'image'
                        ? appDefaultIconBackground
                        : appDetail?.icon_background) || appDefaultIconBackground,
                  }}
                  name={appDetail?.name}
                  description={appDetail?.description}
                  inputs={inputs}
                  handlePublish={handlePublish}
                  onRefreshData={onRefreshData}
                />
              </div>
            )}
          </div>
        </PortalToFollowElemContent>
        <EmbeddedModal
          siteInfo={appDetail?.site}
          isShow={embeddingModalOpen}
          onClose={() => setEmbeddingModalOpen(false)}
          appBaseUrl={appBaseURL}
          accessToken={accessToken}
        />
      </PortalToFollowElem>
      <MemberSelectModal
        isShow={showMemberSelectModal}
        onClose={() => setShowMemberSelectModal(false)}
        onConfirm={(selectedMemberIDs, selectedDeptIDs) => {
          resetSelectedMembers(selectedMemberIDs);
          resetSelectedDepts(selectedDeptIDs);
        }}
        departments={departments} // 组织架构，包括部门和员工
        selectedMembers={
          activeTab === 'edit'
            ? selectedMembers && selectedMembers.length > 0
              ? selectedMembers.map(m => m.uuid || m.id)
              : []
            : selectedInstalledAppMembers && selectedInstalledAppMembers.length > 0
            ? selectedInstalledAppMembers.map(m => m.uuid || m.id)
            : []
        } // 已选成员id，优先使用uuid
        selectedDepts={
          activeTab === 'edit'
            ? selectedDepts && selectedDepts.length > 0
              ? selectedDepts.map(d => d.uuid || d.id)
              : []
            : selectedInstalledAppDepts && selectedInstalledAppDepts.length > 0
            ? selectedInstalledAppDepts.map(d => d.uuid || d.id)
            : []
        } // 已选部门，优先使用uuid
        keywords={keywords} // 搜索关键词
        handleKeywordsChange={handleKeywordsChange} // 搜索关键词变化
      />
    </>
  );
};

export default memo(AppPublisher);
