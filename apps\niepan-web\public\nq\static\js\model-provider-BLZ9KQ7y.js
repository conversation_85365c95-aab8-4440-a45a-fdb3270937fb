import{w as s,x as t}from"./index-C4ad4gme.js";import{d as l,r as a,z as c,c as n,o as d,b as i}from"./pnpm-pnpm-B4aX-tnA.js";const p={class:"border-0 rounded-[20px] overflow-hidden h-[calc(100vh-120px)] relative z-50"},u=["src"],h=l({__name:"model-provider",setup(m){const r=s(),{globalConfig:e}=t(),o=a("");return c(()=>{r.setActiveMenu("model_provider"),e.workspaceSettings&&e.workspaceSettings.modelProviders?o.value=e.workspaceSettings.modelProviders:(console.warn("模型供应商嵌入URL未配置，请检查config.js文件"),o.value="http://localhost:3000/embed/model-providers")}),(f,v)=>(d(),n("div",p,[i("iframe",{src:o.value,class:"w-full h-full border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""},null,8,u)]))}});export{h as default};
