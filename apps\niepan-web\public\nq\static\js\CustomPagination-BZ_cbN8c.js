import{d as B,r as g,s as E,N as L,c as p,o as v,b as n,n as m,k as N,v as M,J as I,t as x,K as H,R as j,x as D,y as K}from"./pnpm-pnpm-B4aX-tnA.js";import{a as R}from"./index-C4ad4gme.js";const f=d=>(D("data-v-7ee24f41"),d=d(),K(),d),V={class:"custom-pagination flex items-center justify-between w-full py-3 shrink-0 px-0 pb-0"},T={class:"flex items-center gap-0.5 rounded-[10px] bg-[#E9EDF7] p-0.5 h-8"},Z=["tabindex"],J=["disabled"],U=f(()=>n("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"currentColor",class:"h-4 w-4"},[n("path",{d:"M7.82843 10.9999H20V12.9999H7.82843L13.1924 18.3638L11.7782 19.778L4 11.9999L11.7782 4.22168L13.1924 5.63589L7.82843 10.9999Z"})],-1)),$=[U],q={class:"text-xs font-medium text-text-primary"},A=f(()=>n("div",{class:"text-xs font-medium text-text-tertiary"},"/",-1)),G={class:"text-xs font-medium text-text-primary"},O=["tabindex"],Q=["disabled"],W=f(()=>n("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"currentColor",class:"h-4 w-4"},[n("path",{d:"M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"})],-1)),X=[W],Y={class:"flex grow list-none items-center justify-center gap-1"},ee={tabindex:"0",class:"text-sm font-medium flex min-w-8 cursor-pointer items-center justify-center rounded-md px-3 py-1.5 text-text-primary hover:bg-components-button-ghost-bg-hover bg-components-segmented-control-bg-normal"},te={class:"flex shrink-0 items-center gap-2"},ae=f(()=>n("div",{class:"text-2xs uppercase w-[51px] shrink-0 text-end text-text-tertiary"},null,-1)),ne={class:"flex items-center gap-[1px] rounded-[10px] bg-components-segmented-control-bg-normal p-0.5 h-8"},se=["onClick"],oe=B({__name:"CustomPagination",props:{currentPage:{},pageSize:{},total:{},pageSizes:{default:()=>[10,25,50]}},emits:["update:currentPage","update:pageSize","change"],setup(d,{expose:z,emit:F}){const i=d,o=F,t=g(i.currentPage),l=g(i.pageSize),a=g(String(i.currentPage)),r=g(!1),h=g(null),s=E(()=>Math.ceil(i.total/l.value)||1);L(()=>i.currentPage,e=>{t.value=e,a.value=String(e)}),L(()=>i.pageSize,e=>{l.value=e});const _=()=>{r.value=!0,a.value=String(t.value),j(()=>{h.value&&(h.value.focus(),h.value.select())})},w=()=>{a.value===""?a.value=String(t.value):b(),r.value=!1},y=e=>{e.key==="Enter"?(a.value===""?a.value=String(t.value):b(),r.value=!1):e.key==="Escape"&&(r.value=!1,a.value=String(t.value))},P=()=>{a.value=a.value.replace(/[^0-9]/g,"")},b=()=>{if(a.value===""){a.value=String(t.value);return}let e=parseInt(a.value);isNaN(e)||e<1?e=1:e>s.value&&(e=s.value),a.value=String(e),e!==t.value&&(t.value=e,o("update:currentPage",e),o("change",{page:e,pageSize:l.value}))},S=()=>{t.value>1&&(t.value--,a.value=String(t.value),o("update:currentPage",t.value),o("change",{page:t.value,pageSize:l.value}))},k=()=>{t.value<s.value&&(t.value++,a.value=String(t.value),o("update:currentPage",t.value),o("change",{page:t.value,pageSize:l.value}))},C=e=>{l.value=e;const u=Math.ceil(i.total/e)||1;t.value>u&&(t.value=u,a.value=String(u)),o("update:pageSize",e),o("change",{page:t.value,pageSize:e})};return z({localCurrentPage:t,localPageSize:l,totalPages:s,isEditing:r,inputPage:a,handlePageInputChange:b,handlePrevPage:S,handleNextPage:k,handleSizeChange:C,enableEditing:_,handlePageInputBlur:w,handlePageInputKeydown:y,filterNonNumeric:P}),(e,u)=>(v(),p("div",V,[n("div",T,[n("div",{class:m(["bg-[#FFF] rounded-md",{"opacity-50":e.currentPage<=1}]),tabindex:e.currentPage<=1?-1:void 0},[n("button",{type:"button",disabled:e.currentPage<=1,onClick:S,class:m(["btn btn-secondary w-[22px] h-7 px-1.5 !rounded-md !min-w-[28px]",{disabled:e.currentPage<=1}])},$,10,J)],10,Z),n("div",{class:"flex items-center gap-0.5 rounded-lg px-2 py-0.5 hover:cursor-text hover:bg-state-base-hover-alt",onClick:_},[r.value?N((v(),p("input",{key:0,ref_key:"pageInputRef",ref:h,"onUpdate:modelValue":u[0]||(u[0]=c=>a.value=c),class:"w-6 text-center text-xs font-medium text-text-primary bg-white outline-none border-b border-blue-500",onBlur:w,onKeydown:y,onInput:P,type:"text"},null,544)),[[M,a.value]]):(v(),p(I,{key:1},[n("div",q,x(t.value),1),A,n("div",G,x(s.value),1)],64))]),n("div",{class:m(["bg-[#FFF] rounded-md",{"opacity-50":e.currentPage>=s.value}]),tabindex:e.currentPage>=s.value?-1:void 0},[n("button",{type:"button",disabled:e.currentPage>=s.value,onClick:k,class:m(["btn btn-secondary w-[22px] h-7 px-1.5 !rounded-md !min-w-[28px]",{disabled:e.currentPage>=s.value}])},X,10,Q)],10,O)]),n("div",Y,[n("li",null,[n("a",ee,x(e.currentPage),1)])]),n("div",te,[ae,n("div",ne,[(v(!0),p(I,null,H(e.pageSizes,c=>(v(),p("div",{key:c,onClick:le=>C(c),class:m(["text-sm font-medium h-7 w-8 cursor-pointer rounded-md text-center !leading-[28px] text-text-primary hover:bg-state-base-hover",{"bg-components-segmented-control-item-active-bg shadow-xs hover:bg-components-segmented-control-item-active-bg":l.value===c}])},x(c),11,se))),128))])])]))}}),ue=R(oe,[["__scopeId","data-v-7ee24f41"]]);export{ue as default};
