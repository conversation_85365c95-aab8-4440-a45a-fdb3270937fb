'use client';
import type { FC } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { RiEditLine, RiMoreFill } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { useBoolean } from 'ahooks';
import {
  PortalToFollowElem,
  PortalToFollowElemContent,
  PortalToFollowElemTrigger,
} from '@/app/components/base/portal-to-follow-elem';
import ActionButton, { ActionButtonState } from '@/app/components/base/action-button';
import cn from '@/utils/classnames';
import type { SVGProps } from 'react';
import { Zhiding } from '@/app/components/base/icons/src/public/common';
import { NoZhiding } from '@/app/components/base/icons/src/public/common';
// import {  Zhiding } from '@/app/components/base/icons/src/public/apps';

type Props = {
  isActive?: boolean;
  isItemHovering?: boolean;
  isPinned: boolean;
  isShowRenameConversation?: boolean;
  onRenameConversation?: () => void;
  isShowDelete: boolean;
  togglePin: () => void;
  onDelete: () => void;
};

const RiDeleteBinLineIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      className={className ?? ''}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.72508 0.875C4.44216 0.875 4.19716 1.07917 4.1505 1.35917L3.88217 2.91667H1.75008C1.42925 2.91667 1.16675 3.17917 1.16675 3.5C1.16675 3.82083 1.42925 4.08333 1.75008 4.08333H2.66008L3.20841 12.5796C3.22883 12.8858 3.48258 13.125 3.79175 13.125H10.2084C10.5147 13.125 10.7713 12.8858 10.7917 12.5796L11.3401 4.08333H12.2501C12.5709 4.08333 12.8334 3.82083 12.8334 3.5C12.8334 3.17917 12.5709 2.91667 12.2501 2.91667H10.118L9.84967 1.35917C9.803 1.07917 9.558 0.875 9.27508 0.875L4.72508 0.875ZM8.93383 2.91667H5.06633L5.21508 2.04167H8.78217L8.93092 2.91667H8.93383ZM4.34008 11.9583L3.83258 4.08333H10.1705L9.663 11.9583H4.34008ZM5.54175 9.625C5.22091 9.625 4.95841 9.8875 4.95841 10.2083C4.95841 10.5292 5.22091 10.7917 5.54175 10.7917H8.45842C8.77925 10.7917 9.04175 10.5292 9.04175 10.2083C9.04175 9.8875 8.77925 9.625 8.45842 9.625H5.54175Z"
        fill="currentColor" // 使用 currentColor 让样式由 CSS 控制
      />
    </svg>
  );
};
const Operation: FC<Props> = ({
  isActive,
  isItemHovering,
  isPinned,
  togglePin,
  isShowRenameConversation,
  onRenameConversation,
  isShowDelete,
  onDelete,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const ref = useRef(null);
  const [isHovering, { setTrue: setIsHovering, setFalse: setNotHovering }] = useBoolean(false);
  useEffect(() => {
    if (!isItemHovering && !isHovering) setOpen(false);
  }, [isItemHovering, isHovering]);
  return (
    <PortalToFollowElem open={open} onOpenChange={setOpen} placement="bottom-end" offset={4}>
      <PortalToFollowElemTrigger onClick={() => setOpen(v => !v)}>
        <ActionButton
          className={cn(isItemHovering || open ? 'opacity-100' : 'opacity-0')}
          state={
            isActive
              ? ActionButtonState.Active
              : open
              ? ActionButtonState.Hover
              : ActionButtonState.Default
          }
        >
          <RiMoreFill className="h-4 w-4" />
        </ActionButton>
      </PortalToFollowElemTrigger>
      <PortalToFollowElemContent className="z-50">
        <div
          ref={ref}
          className={
            'min-w-[136px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-[10px] shadow-lg backdrop-blur-sm'
          }
          onMouseEnter={setIsHovering}
          onMouseLeave={setNotHovering}
          onClick={e => {
            e.stopPropagation();
          }}
        >
          <div
            className={cn(
              'system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-primary hover:bg-state-base-hover'
            )}
            onClick={togglePin}
          >
            {isPinned && <NoZhiding className="h-4 w-4 shrink-0 text-text-tertiary" />}
            {!isPinned && <Zhiding className="h-4 w-4 shrink-0 text-text-tertiary" />}
            <span className="grow">
              {isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}
            </span>
          </div>
          {isShowRenameConversation && (
            <div
              className={cn(
                'system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-primary hover:bg-state-base-hover'
              )}
              onClick={onRenameConversation}
            >
              <RiEditLine className="h-4 w-4 shrink-0 text-text-tertiary" />
              <span className="grow">{t('explore.sidebar.action.rename')}</span>
            </div>
          )}
          {isShowDelete && (
            <div
              className={cn(
                'system-md-regular group flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-primary hover:bg-state-destructive-hover hover:text-text-destructive'
              )}
              onClick={onDelete}
            >
              <RiDeleteBinLineIcon
                className={cn(
                  'h-4 w-4 shrink-0 text-text-tertiary group-hover:text-text-destructive'
                )}
              />
              <span className="grow">{t('explore.sidebar.action.delete')}</span>
            </div>
          )}
        </div>
      </PortalToFollowElemContent>
    </PortalToFollowElem>
  );
};
export default React.memo(Operation);
