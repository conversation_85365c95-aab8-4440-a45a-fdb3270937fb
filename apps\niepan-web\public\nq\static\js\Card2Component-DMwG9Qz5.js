import{f as u}from"./utils-ahBajhV-.js";import{u as f}from"./useLinkHandler-E0UXn71M.js";import{d as h,s as m,c as _,o as v,b as t,t as o,n as i,aa as n}from"./pnpm-pnpm-B4aX-tnA.js";import"./index-C4ad4gme.js";const g=["href","target"],x={class:"flex justify-between items-center"},b={class:"font-zhongcu overflow-one"},k={class:"mb-[15px]"},w={class:"text-3xl font-zhongcu"},C={class:"font-zhongcu ml-1"},y={class:"flex"},z=t("div",{class:"bg-theme w-[3px] h-[54px] rounded-[2px]"},null,-1),L={class:"ml-[12px]"},B={class:"leading-5 overflow-one"},F={class:"leading-5 overflow-one"},E=h({__name:"Card2Component",props:{data:{}},setup(r){const a=r,{linkUrl:c,handleClick:l}=f(a),d=m(()=>{if(a.data.totalMoney==0)return{value:0,unit:""};const{value:s,unit:e}=u(a.data.totalMoney);return{value:s,unit:e}});return(s,e)=>(v(),_("a",{href:n(c),onClick:e[0]||(e[0]=(...p)=>n(l)&&n(l)(...p)),target:s.data.isLink?"_blank":"",class:i(s.data.isLink?"":"cursor-default")},[t("div",{class:i(["bg-white p-[15px] pt-[7px] pb-[13px] border border-solid border-[#F1F1F1] rounded-[20px] h-full text-xs",{"transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5":s.data.isLink}])},[t("div",x,[t("span",b,o(s.data.title),1)]),t("div",k,[t("span",w,o(d.value.value),1),t("span",C,o(d.value.unit),1)]),t("div",y,[z,t("div",L,[t("div",B,o(s.data.desc1),1),t("div",F,o(s.data.desc2),1)])])],2)],10,g))}});export{E as default};
