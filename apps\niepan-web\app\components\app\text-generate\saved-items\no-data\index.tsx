'use client';
import type { FC } from 'react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { RiAddLine } from '@remixicon/react';
import Button from '@/app/components/base/button';
import type { SVGProps } from 'react';
export type INoDataProps = {
  onStartCreateContent: () => void;
};

const CollectIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.99914 1C8.25298 0.99935 8.48517 1.14291 8.59802 1.37029L10.507 5.21658L14.7642 5.83734C15.0149 5.87391 15.2233 6.04949 15.3018 6.29044C15.3804 6.53138 15.3155 6.79602 15.1344 6.97333L12.0379 10.0059L12.7732 14.2187C12.817 14.4697 12.7139 14.7237 12.5076 14.8732C12.3012 15.0227 12.0277 15.0415 11.8029 14.9217L8.00089 12.8952L4.19974 14.9216C3.97476 15.0416 3.70107 15.0227 3.49469 14.873C3.2883 14.7233 3.18537 14.469 3.22953 14.2179L3.97013 10.0059L0.868609 6.97374C0.687257 6.79644 0.622213 6.5316 0.7008 6.29046C0.779386 6.04932 0.98799 5.87366 1.23898 5.83727L5.51931 5.21659L7.40217 1.37336C7.51385 1.14541 7.7453 1.00065 7.99914 1ZM8.00473 3.17407L6.56187 6.11917C6.465 6.31689 6.27676 6.45404 6.05886 6.48563L2.76517 6.96324L5.15367 9.29833C5.31102 9.45217 5.38233 9.67375 5.34422 9.89048L4.77961 13.1015L7.68723 11.5514C7.88324 11.447 8.11841 11.4469 8.31443 11.5514L11.2249 13.1027L10.6641 9.88966C10.6264 9.67337 10.6975 9.45236 10.8544 9.29874L13.2399 6.96251L9.9689 6.48556C9.75241 6.45399 9.56518 6.31821 9.46792 6.12224L8.00473 3.17407Z"
        fill="currentColor"
      />
    </svg>
  );
};

const NoData: FC<INoDataProps> = ({ onStartCreateContent }) => {
  const { t } = useTranslation();

  return (
    <div className="rounded-xl bg-background-section-burn p-6 ">
      <div className="flex h-10 w-10 items-center justify-center rounded-[10px] border-[0.5px] border-components-card-border bg-components-card-bg-alt text-text-accent shadow-lg backdrop-blur-sm">
        <CollectIcon className="h-4 w-4 text-text-accent" />
      </div>
      <div className="mt-3">
        <span className="system-xl-semibold text-text-secondary">
          {t('share.generation.savedNoData.title')}
        </span>
      </div>
      <div className="system-sm-regular mt-1 text-text-tertiary">
        {t('share.generation.savedNoData.description')}
      </div>
      <Button variant="primary" className="mt-3" onClick={onStartCreateContent}>
        <RiAddLine className="mr-1 h-4 w-4" />
        <span>{t('share.generation.savedNoData.startCreateContent')}</span>
      </Button>
    </div>
  );
};

export default React.memo(NoData);
