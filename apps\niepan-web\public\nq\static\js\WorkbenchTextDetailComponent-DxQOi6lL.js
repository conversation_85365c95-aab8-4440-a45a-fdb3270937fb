import{d as r,c as e,o as t,J as i,K as c,b as d,a as p,t as n}from"./pnpm-pnpm-B4aX-tnA.js";const l={class:"flex justify-between flex-wrap text-sm"},m={class:"w-[8em] shrink-0 truncate mr-[8px] text-[#A2A9B0]"},u={key:0},h=r({__name:"WorkbenchTextDetailComponent",props:{data:{}},setup(_){return(a,x)=>(t(),e("div",l,[(t(!0),e(i,null,c(a.data.items,(s,o)=>(t(),e("div",{key:o,class:"w-[49%] flex bg-[#F7F9FC] rounded-md py-[9px] px-[16px] mb-[12px]"},[d("div",m,n(s.title),1),["singleLine","multiLine"].includes(s.itemType)?(t(),e("div",u,n(s.content),1)):p("",!0)]))),128))]))}});export{h as default};
