<template>
  <div class="fixed inset-0 min-h-screen w-full bg-white/50 p-6 backdrop-blur-[20px] z-[100]">
    <iframe
      :src="embedUrl"
      class="w-full h-full border-0"
      frameborder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen
      ref="iframeRef"
    ></iframe>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
// import { useMenuV3Store } from "@/store/modules/menuV3";
import { useConfig } from '@/hooks/useConfig';

// const menuStore = useMenuV3Store();
const { globalConfig } = useConfig();
const embedUrl = ref('');
const iframeRef = ref<HTMLIFrameElement | null>(null);

const props = defineProps({
  onCancel: {
    type: Function,
    required: true,
  },
});

// 监听来自 iframe 的消息
const handleMessage = (event: MessageEvent) => {
  // 安全检查 - 在生产环境中应该检查 event.origin
  // if (event.origin !== 'http://localhost:3000') return;

  const message = event.data;
  if (message && message.from === 'niepan-embed') {
    if (message && message.type === 'close-modal') {
      props.onCancel();
    }
  }
};

onMounted(() => {
  // 添加消息监听器
  window.addEventListener('message', handleMessage);

  // 从全局配置中获取嵌入URL
  if (globalConfig.workspaceSettings && globalConfig.workspaceSettings.modalAbout) {
    embedUrl.value = globalConfig.workspaceSettings.modalAbout;
  } else {
    console.warn('API扩展嵌入URL未配置，请检查config.js文件');
    embedUrl.value = 'http://localhost:3000/embed/modal-about'; // 默认URL
  }
});

onBeforeUnmount(() => {
  // 移除消息监听器
  window.removeEventListener('message', handleMessage);
});
</script>
