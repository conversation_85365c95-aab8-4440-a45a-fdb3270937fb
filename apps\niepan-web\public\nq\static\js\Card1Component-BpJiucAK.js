import{_ as g}from"./quesheng-C5oWzANf.js";import{f as k}from"./utils-ahBajhV-.js";import{u as b}from"./useLinkHandler-E0UXn71M.js";import{d as v,s as x,c as o,o as s,b as a,a as y,t as l,n as m,aa as r}from"./pnpm-pnpm-B4aX-tnA.js";import"./index-C4ad4gme.js";const w=["href","target"],C={key:0,class:"w-[42px] h-[42px] overflow-hidden rounded-full text-center leading-[42px]"},j=["src"],U={key:1,src:g,alt:"",class:"w-full h-full object-contain object-center"},z={class:"mt-[20px] mb-[8px]"},L={class:"text-34 leading-34 font-zhongcu"},B={class:"font-zhongcu ml-1"},F={class:"flex justify-between items-center"},D={class:"font-zhongcu"},H=v({__name:"Card1Component",props:{data:{}},setup(f){const n=f,{linkUrl:h,handleClick:i}=b(n),d=x(()=>{if(n.data.totalMoney==0)return{value:0,unit:""};const{value:t,unit:e}=k(n.data.totalMoney);return{value:t,unit:e}});return(t,e)=>{var c,u,p;return s(),o("a",{href:r(h),onClick:e[0]||(e[0]=(..._)=>r(i)&&r(i)(..._)),target:t.data.isLink&&t.data.linkUrl?"_blank":"",class:m(t.data.isLink?"":"cursor-default")},[a("div",{class:m(["bg-white p-[15px] pt-[7px] pb-[13px] border border-solid border-[#F1F1F1] rounded-[20px] h-full text-xs",{"transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5":t.data.isLink}])},[(c=t.data)!=null&&c.imgUrl?(s(),o("div",C,[(u=t.data)!=null&&u.imgUrl?(s(),o("img",{key:0,src:(p=t.data)==null?void 0:p.imgUrl,alt:"",class:"w-full h-full object-contain object-center"},null,8,j)):(s(),o("img",U))])):y("",!0),a("div",z,[a("span",L,l(d.value.value),1),a("span",B,l(d.value.unit),1)]),a("div",F,[a("span",D,l(t.data.title),1)])],2)],10,w)}}});export{H as default};
