import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiExpandRightLine, RiLayoutLeft2Line } from '@remixicon/react';
import { useChatWithHistoryContext } from '../context';
import AppIcon from '@/app/components/base/app-icon';
import ActionButton from '@/app/components/base/action-button';
import Button from '@/app/components/base/button';
import List from '@/app/components/base/chat/chat-with-history/sidebar/list';
import Confirm from '@/app/components/base/confirm';
import RenameModal from '@/app/components/base/chat/chat-with-history/sidebar/rename-modal';
import type { ConversationItem } from '@/models/share';
import cn from '@/utils/classnames';
import { AppTypeIcon } from '@/app/components/app/type-selector';

type Props = {
  isPanel?: boolean;
};

const Sidebar = ({ isPanel }: Props) => {
  const { t } = useTranslation();
  const {
    appData,
    handleNewConversation,
    pinnedConversationList,
    conversationList,
    currentConversationId,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    conversationRenaming,
    handleRenameConversation,
    handleDeleteConversation,
    sidebarCollapseState,
    handleSidebarCollapse,
    isMobile,
    isResponding,
  } = useChatWithHistoryContext();
  const isSidebarCollapsed = sidebarCollapseState;

  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null);
  const [showRename, setShowRename] = useState<ConversationItem | null>(null);

  const handleOperate = useCallback(
    (type: string, item: ConversationItem) => {
      if (type === 'pin') handlePinConversation(item.id);

      if (type === 'unpin') handleUnpinConversation(item.id);

      if (type === 'delete') setShowConfirm(item);

      if (type === 'rename') setShowRename(item);
    },
    [handlePinConversation, handleUnpinConversation]
  );
  const handleCancelConfirm = useCallback(() => {
    setShowConfirm(null);
  }, []);
  const handleDelete = useCallback(() => {
    if (showConfirm) {
      handleDeleteConversation(showConfirm.id, {
        onSuccess: handleCancelConfirm,
      });
    }
  }, [showConfirm, handleDeleteConversation, handleCancelConfirm]);
  const handleCancelRename = useCallback(() => {
    setShowRename(null);
  }, []);
  const handleRename = useCallback(
    (newName: string) => {
      if (showRename) {
        handleRenameConversation(showRename.id, newName, {
          onSuccess: handleCancelRename,
        });
      }
    },
    [showRename, handleRenameConversation, handleCancelRename]
  );

  return (
    <div
      className={cn(
        'flex w-full grow flex-col',
        isPanel &&
          'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg'
      )}
    >
      <div className={cn('flex shrink-0 items-center gap-3 p-1')}>
        <div className="relative shrink-0">
          <AppIcon
            size="large"
            iconType={appData?.site.icon_type}
            icon={appData?.site.icon}
            background={appData?.site.icon_background}
            imageUrl={appData?.site.icon_url}
            className="rounded-full  !text-xl"
          />
          <AppTypeIcon
            type={appData?.site.mode}
            wrapperClassName="absolute -bottom-0.5 -right-0.5 w-4 h-4 shadow-sm"
            className="h-3 w-3"
          />
        </div>
        <div className={cn('system-md-semibold grow truncate text-text-secondary')}>
          {appData?.site.title}
        </div>
        {!isMobile && isSidebarCollapsed && (
          <ActionButton size="l" onClick={() => handleSidebarCollapse(false)}>
            <RiExpandRightLine className="h-[18px] w-[18px]" />
          </ActionButton>
        )}
        {!isMobile && !isSidebarCollapsed && (
          <ActionButton size="l" onClick={() => handleSidebarCollapse(true)}>
            <RiLayoutLeft2Line className="h-[18px] w-[18px] text-[#A2A9B0]" />
          </ActionButton>
        )}
      </div>
      <div className="mt-3 flex items-center text-xs font-medium text-text-tertiary">
        {appData?.site.mode === 'advanced-chat' && (
          <div className="truncate">{t('app.types.advanced').toUpperCase()}</div>
        )}
        {appData?.site.mode === 'chat' && (
          <div className="truncate">{t('app.types.chatbot').toUpperCase()}</div>
        )}
        {appData?.site.mode === 'agent-chat' && (
          <div className="truncate">{t('app.types.agent').toUpperCase()}</div>
        )}
        {appData?.site.mode === 'workflow' && (
          <div className="truncate">{t('app.types.workflow').toUpperCase()}</div>
        )}
        {appData?.site.mode === 'completion' && (
          <div className="truncate">{t('app.types.completion').toUpperCase()}</div>
        )}
      </div>
      <div className="shrink-0 py-4">
        <Button
          variant="secondary-accent"
          disabled={isResponding}
          className="w-full justify-center border-util-colors-indigo-indigo-600"
          onClick={handleNewConversation}
        >
          <span className="text-sm">+</span>
          {t('share.chat.newChat')}
        </Button>
      </div>
      <div className="h-0 grow space-y-2 overflow-y-auto pt-4">
        {/* pinned list */}
        {!!pinnedConversationList.length && (
          <div className="mb-4">
            <List
              isPin
              title={t('share.chat.pinnedTitle') || ''}
              list={pinnedConversationList}
              onChangeConversation={handleChangeConversation}
              onOperate={handleOperate}
              currentConversationId={currentConversationId}
            />
          </div>
        )}
        {!!conversationList.length && (
          <List
            title={(pinnedConversationList.length && t('share.chat.unpinnedTitle')) || ''}
            list={conversationList}
            onChangeConversation={handleChangeConversation}
            onOperate={handleOperate}
            currentConversationId={currentConversationId}
          />
        )}
      </div>
      {/* <div className="flex shrink-0 items-center  p-3 text-center">
        <MenuDropdown placement="top-start" data={appData?.site} />
        <div className="shrink-0">
          {!appData?.custom_config?.remove_webapp_brand && (
            <div className={cn("flex shrink-0 items-center gap-1.5 px-2")}>
              <div className="system-2xs-medium-uppercase text-center text-text-tertiary">
                POWERED BY INTELLIDO
              </div>

            </div>
          )}
        </div>
      </div> */}
      {!!showConfirm && (
        <Confirm
          title={t('share.chat.deleteConversation.title')}
          content={t('share.chat.deleteConversation.content') || ''}
          isShow
          onCancel={handleCancelConfirm}
          onConfirm={handleDelete}
        />
      )}
      {showRename && (
        <RenameModal
          isShow
          onClose={handleCancelRename}
          saveLoading={conversationRenaming}
          name={showRename?.name || ''}
          onSave={handleRename}
        />
      )}
    </div>
  );
};

export default Sidebar;
