import{d,c as a,a as l,o as r,b as e,t as n,w as i,x as c,y as m}from"./pnpm-pnpm-B4aX-tnA.js";import{a as p}from"./index-C4ad4gme.js";const f=d({name:"v3Confirms",components:{},props:{show:{type:Boolean,required:!0},title:{type:String,required:!0},message:{type:String,required:!0},confirmText:{type:String,default:"确认删除"}},emits:["close","confirm"],methods:{handleClose(){this.$emit("close")},handleConfirm(){this.$emit("confirm")}}}),h=o=>(c("data-v-9cb6d18d"),o=o(),m(),o),C={key:0,class:"modal-del"},u={class:"modal-del-container"},v={class:"flex justify-between items-center"},g={class:"modal-del-container-top font-zhongcu font20 leading-7 font-medium"},w=h(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none"},[e("path",{d:"M16.0771 2.74408C16.4025 2.41864 16.9304 2.41864 17.2558 2.74408C17.5812 3.06951 17.5812 3.59735 17.2558 3.92279L11.1786 9.99994L17.2558 16.0771C17.5812 16.4025 17.5812 16.9304 17.2558 17.2558C16.9304 17.5812 16.4025 17.5812 16.0771 17.2558L9.99994 11.1786L3.92279 17.2558C3.59735 17.5812 3.06951 17.5812 2.74408 17.2558C2.41864 16.9304 2.41864 16.4025 2.74408 16.0771L8.82123 9.99994L2.74408 3.92279C2.41864 3.59735 2.41864 3.06951 2.74408 2.74408C3.06951 2.41864 3.59735 2.41864 3.92279 2.74408L9.99994 8.82123L16.0771 2.74408Z",fill:"#697077"})],-1)),y=[w],b={class:"modal-del-container-center font-zhongcu font14 !text-[#697077]"},_={class:"modal-del-container-bottom font14"};function L(o,t,S,k,$,B){return o.show?(r(),a("div",C,[e("div",u,[e("div",v,[e("div",g,n(o.title),1),e("div",{onClick:t[0]||(t[0]=i((...s)=>o.handleClose&&o.handleClose(...s),["stop"])),class:"cursor-pointer hover:opacity-70 transition-opacity"},y)]),e("div",b,n(o.message),1),e("div",_,[e("button",{type:"button",onClick:t[1]||(t[1]=i((...s)=>o.handleClose&&o.handleClose(...s),["stop"])),class:"common-cancel-btn"}," 取消 "),e("button",{type:"button",onClick:t[2]||(t[2]=i((...s)=>o.handleConfirm&&o.handleConfirm(...s),["stop"])),class:"common-confirm-btn"},n(o.confirmText),1)])])])):l("",!0)}const M=p(f,[["render",L],["__scopeId","data-v-9cb6d18d"]]);export{M as v};
