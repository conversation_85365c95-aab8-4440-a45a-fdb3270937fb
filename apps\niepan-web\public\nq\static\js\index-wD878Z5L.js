import{d as b,A as $,o as l,m as p,c as o,b as t,n as w,M as K,x as c3,y as p3,s as R,a7 as d3,al as e3,e as E,a as z,f as c,aa as V,t as _,w as j,r as v,N as W,aC as G,z as s3,R as Q,P as u3,J as S,K as X,am as f3,p as D,aD as C3,aE as _3,u as m3,ar as v3,_ as h3,E as B}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as l3}from"./SearchIcon.vue_vue_type_script_setup_true_lang-DDwCl3ZY.js";import{_ as A,a as U,f as g3,v as x3,b as w3,j as $3,u as y3,p as V3}from"./index-C4ad4gme.js";import{E as b3,_ as H3,g as k3}from"./apiUrl-B-GJIBbf.js";const M3={width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"},z3={id:"small arrow"},A3=["fill"],Z3={name:"SmallArrowIcon"},L3=b({...Z3,props:{color:{default:"#129BFE"},size:{default:18},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[(l(),o("svg",M3,[t("g",z3,[t("path",{id:"Vector","fill-rule":"evenodd","clip-rule":"evenodd",d:"M13.0688 5.33625C13.0313 5.25 12.9788 5.1675 12.9075 5.09625C12.9075 5.09625 12.9075 5.09625 12.9037 5.0925C12.7687 4.9575 12.5813 4.875 12.375 4.875H5.625C5.2125 4.875 4.875 5.2125 4.875 5.625C4.875 6.0375 5.2125 6.375 5.625 6.375H10.5638L5.47125 11.4713C5.17875 11.7638 5.17875 12.24 5.47125 12.5325C5.76375 12.825 6.24 12.825 6.5325 12.5325L11.6288 7.43625V12.375C11.6288 12.7875 11.9663 13.125 12.3788 13.125C12.7913 13.125 13.1288 12.7875 13.1288 12.375V5.625C13.1288 5.52 13.1063 5.42625 13.0725 5.33625H13.0688Z",fill:e.color},null,8,A3)])]))]),_:1},8,["color","size","class"]))}}),B3="/nq/static/png/empty-Cr4DhK-q.png",F3=d=>(c3("data-v-6584dd31"),d=d(),p3(),d),T3=F3(()=>t("img",{src:B3,alt:"empty",class:"w-full h-full"},null,-1)),E3=[T3],N3={name:"EmptyStateIcon"},S3=b({...N3,props:{size:{default:160},className:{default:""}},setup(d){return(e,h)=>(l(),o("div",{class:"empty-state-icon",style:K({width:`${e.size}px`,height:`${e.size}px`})},E3,4))}}),I3=U(S3,[["__scopeId","data-v-6584dd31"]]),j3={name:"BorderlessButton",emits:["click"]},D3=b({...j3,props:{bgColor:{default:"blue"},textColor:{default:"blue"},className:{default:""}},setup(d){const e=d,h=R(()=>{switch(e.bgColor){case"blue":return"bg-[#CFEAFE]";case"gray":return"bg-gray-100";case"white":return"bg-white";default:return`bg-[${e.bgColor}]`}}),x=R(()=>{switch(e.textColor){case"blue":return"text-[#129BFE]";case"gray":return"text-gray-600";case"black":return"text-gray-900";default:return`text-[${e.textColor}]`}});return(s,r)=>(l(),o("button",{class:w(["flex items-center justify-center gap-[2px] rounded-[6px] px-[12px] py-[5px] pl-4 text-[14px] font-medium leading-[22px] transition-colors duration-200",h.value,x.value,s.className]),onClick:r[0]||(r[0]=H=>s.$emit("click")),type:"button"},[d3(s.$slots,"default",{},void 0,!0)],2))}}),U3=U(D3,[["__scopeId","data-v-379d06ad"]]),O3={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},P3=["fill"],J3={name:"AllAppsIcon"},q3=b({...J3,props:{color:{default:"#129BFE"},size:{default:16},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[(l(),o("svg",O3,[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.72353 1.72378C1.97358 1.47373 2.31272 1.33325 2.66634 1.33325H5.99967C6.3533 1.33325 6.69244 1.47373 6.94248 1.72378C7.19253 1.97382 7.33301 2.31296 7.33301 2.66659V5.99992C7.33301 6.35354 7.19253 6.69268 6.94248 6.94273C6.69244 7.19278 6.3533 7.33325 5.99967 7.33325H2.66634C2.31272 7.33325 1.97358 7.19278 1.72353 6.94273C1.47348 6.69268 1.33301 6.35354 1.33301 5.99992V2.66659C1.33301 2.31296 1.47348 1.97382 1.72353 1.72378ZM5.99967 2.66659H2.66634V5.99992H5.99967V2.66659ZM9.05687 1.72378C9.30691 1.47373 9.64605 1.33325 9.99967 1.33325H13.333C13.6866 1.33325 14.0258 1.47373 14.2758 1.72378C14.5259 1.97383 14.6663 2.31296 14.6663 2.66659V5.99992C14.6663 6.35354 14.5259 6.69268 14.2758 6.94273C14.0258 7.19278 13.6866 7.33325 13.333 7.33325H9.99967C9.64605 7.33325 9.30691 7.19278 9.05687 6.94273C8.80682 6.69268 8.66634 6.35354 8.66634 5.99992V2.66659C8.66634 2.31296 8.80682 1.97382 9.05687 1.72378ZM13.333 2.66659H9.99967V5.99992H13.333V2.66659ZM1.72353 9.05711C1.97358 8.80706 2.31272 8.66659 2.66634 8.66659H5.99967C6.3533 8.66659 6.69244 8.80706 6.94248 9.05711C7.19253 9.30716 7.33301 9.6463 7.33301 9.99992V13.3333C7.33301 13.6869 7.19253 14.026 6.94248 14.2761C6.69243 14.5261 6.3533 14.6666 5.99967 14.6666H2.66634C2.31272 14.6666 1.97358 14.5261 1.72353 14.2761C1.47348 14.026 1.33301 13.6869 1.33301 13.3333V9.99992C1.33301 9.6463 1.47348 9.30716 1.72353 9.05711ZM5.99967 9.99992H2.66634V13.3333H5.99967V9.99992ZM9.05687 9.05711C9.30691 8.80706 9.64605 8.66659 9.99967 8.66659H13.333C13.6866 8.66659 14.0258 8.80706 14.2758 9.05711C14.5259 9.30716 14.6663 9.6463 14.6663 9.99992V13.3333C14.6663 13.6869 14.5259 14.026 14.2758 14.2761C14.0258 14.5261 13.6866 14.6666 13.333 14.6666H9.99967C9.64605 14.6666 9.30691 14.5261 9.05687 14.2761C8.80682 14.026 8.66634 13.6869 8.66634 13.3333V9.99992C8.66634 9.6463 8.80682 9.30716 9.05687 9.05711ZM13.333 9.99992H9.99967V13.3333H13.333V9.99992Z",fill:e.color},null,8,P3)]))]),_:1},8,["color","size","class"]))}}),K3={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},R3=["fill"],W3={name:"ChatIcon"},G3=b({...W3,props:{color:{default:"#343A3F"},size:{default:16},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[(l(),o("svg",K3,[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.33268 1.3335C0.964492 1.3335 0.666016 1.63197 0.666016 2.00016V12.0002C0.666016 12.3684 0.964492 12.6668 1.33268 12.6668H3.66602V13.6668C3.66602 13.8979 3.78565 14.1125 3.98219 14.2339C4.17874 14.3554 4.42417 14.3664 4.63082 14.2631L7.82339 12.6668H14.666C15.0342 12.6668 15.3327 12.3684 15.3327 12.0002V2.00016C15.3327 1.63197 15.0342 1.3335 14.666 1.3335H1.33268ZM1.99935 11.3335V2.66683H13.9993V11.3335H7.66602C7.56252 11.3335 7.46044 11.3576 7.36787 11.4039L4.99935 12.5881V12.0002C4.99935 11.632 4.70087 11.3335 4.33268 11.3335H1.99935ZM5.33268 6.50016C5.33268 6.13197 5.03421 5.8335 4.66602 5.8335C4.29783 5.8335 3.99935 6.13197 3.99935 6.50016V7.50016C3.99935 7.86835 4.29783 8.16683 4.66602 8.16683C5.03421 8.16683 5.33268 7.86835 5.33268 7.50016V6.50016ZM7.99935 5.8335C8.36754 5.8335 8.66602 6.13197 8.66602 6.50016V7.50016C8.66602 7.86835 8.36754 8.16683 7.99935 8.16683C7.63116 8.16683 7.33268 7.86835 7.33268 7.50016V6.50016C7.33268 6.13197 7.63116 5.8335 7.99935 5.8335ZM11.9993 6.50016C11.9993 6.13197 11.7009 5.8335 11.3327 5.8335C10.9645 5.8335 10.666 6.13197 10.666 6.50016V7.50016C10.666 7.86835 10.9645 8.16683 11.3327 8.16683C11.7009 8.16683 11.9993 7.86835 11.9993 7.50016V6.50016Z",fill:e.color},null,8,R3)]))]),_:1},8,["color","size","class"]))}}),Q3={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},X3=["fill"],Y3={name:"AgentIcon"},e6=b({...Y3,props:{color:{default:"#343A3F"},size:{default:16},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[(l(),o("svg",Q3,[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.49967 1.85402C5.41634 1.49402 5.05967 1.27069 4.69967 1.35402C4.33967 1.43735 4.11634 1.79402 4.19967 2.15402L4.59301 3.85735H3.92301C2.49301 3.85735 1.33301 5.01735 1.33301 6.44735V9.78069C1.33301 11.214 2.49301 12.374 3.92634 12.374H12.073C13.5063 12.374 14.6663 11.214 14.6663 9.78069V6.44735C14.6663 5.01402 13.5063 3.85402 12.073 3.85402H11.403L11.7963 2.15069C11.8797 1.79069 11.6563 1.43402 11.2963 1.35069C10.9363 1.26735 10.5797 1.49069 10.4963 1.85069L10.033 3.85069H5.95967L5.49634 1.85069L5.49967 1.85402ZM3.92634 5.19069H12.073C12.7697 5.19069 13.333 5.75402 13.333 6.45069V9.78402C13.333 10.4807 12.7697 11.044 12.073 11.044H3.92634C3.22967 11.044 2.66634 10.4807 2.66634 9.78402V6.45069C2.66634 5.75402 3.22967 5.19069 3.92634 5.19069ZM6.07301 7.37402C6.07301 7.00736 5.77301 6.70736 5.40634 6.70736C5.03967 6.70736 4.73967 7.00736 4.73967 7.37402V8.11402C4.73967 8.48069 5.03967 8.78069 5.40634 8.78069C5.77301 8.78069 6.07301 8.48069 6.07301 8.11402V7.37402ZM4.18634 14.004C4.18634 13.6374 4.48634 13.3374 4.85301 13.3374H11.1497C11.5163 13.3374 11.8163 13.6374 11.8163 14.004C11.8163 14.3707 11.5163 14.6707 11.1497 14.6707H4.85301C4.48634 14.6707 4.18634 14.3707 4.18634 14.004ZM11.2597 7.37402C11.2597 7.00736 10.9597 6.70736 10.593 6.70736C10.2263 6.70736 9.92634 7.00736 9.92634 7.37402V8.11402C9.92634 8.48069 10.2263 8.78069 10.593 8.78069C10.9597 8.78069 11.2597 8.48069 11.2597 8.11402V7.37402Z",fill:e.color},null,8,X3)]))]),_:1},8,["color","size","class"]))}}),t6={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},s6={"clip-path":"url(#clip0_756_19731)"},l6=["fill"],o6=t("defs",null,[t("clipPath",{id:"clip0_756_19731"},[t("rect",{width:"16",height:"16",fill:"white"})])],-1),a6={name:"WorkflowIcon"},n6=b({...a6,props:{color:{default:"#343A3F"},size:{default:16},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[(l(),o("svg",t6,[t("g",s6,[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.00033 0.666748C7.63366 0.666748 7.33366 0.966748 7.33366 1.33341C7.33366 1.70008 7.63366 2.00008 8.00033 2.00008C10.927 2.00008 13.3637 4.09675 13.8937 6.86675L12.7437 6.29008C12.4137 6.12675 12.0137 6.26008 11.8503 6.58675C11.687 6.91675 11.8203 7.31675 12.147 7.48008L14.3703 8.59008C14.577 8.69341 14.8237 8.68342 15.0203 8.56008C15.217 8.44008 15.337 8.22341 15.337 7.99341C15.3337 3.95008 12.0503 0.666748 8.00033 0.666748ZM2.95366 4.57008C2.95366 3.67675 3.67699 2.95008 4.57366 2.95008C5.47033 2.95008 6.19366 3.67341 6.19366 4.57008C6.19366 5.46675 5.47033 6.19008 4.57366 6.19008C3.67699 6.19008 2.95366 5.46675 2.95366 4.57008ZM4.57033 1.62008C2.94033 1.62008 1.62033 2.94008 1.62033 4.57008C1.62033 6.20008 2.94366 7.52341 4.57366 7.52341C6.20366 7.52341 7.52699 6.20008 7.52699 4.57008C7.52699 2.94008 6.20366 1.62008 4.57033 1.62008ZM9.81033 11.4301C9.81033 10.5367 10.5337 9.81008 11.4303 9.81008C12.327 9.81008 13.0503 10.5334 13.0503 11.4301C13.0503 12.3267 12.327 13.0501 11.4303 13.0501C10.5337 13.0501 9.81033 12.3267 9.81033 11.4301ZM11.427 8.47675C9.79699 8.47675 8.47366 9.80008 8.47366 11.4301C8.47366 13.0601 9.79699 14.3834 11.427 14.3834C13.057 14.3834 14.3803 13.0601 14.3803 11.4301C14.3803 9.80008 13.057 8.47675 11.427 8.47675ZM0.983659 7.43341C1.18033 7.31341 1.42699 7.30008 1.63366 7.40341L3.85699 8.51342C4.18699 8.67675 4.32033 9.08008 4.15366 9.40675C3.99033 9.73675 3.58699 9.87008 3.26033 9.70341L2.11033 9.12675C2.64033 11.9001 5.07699 13.9934 8.00366 13.9934C8.37033 13.9934 8.67033 14.2934 8.67033 14.6601C8.67033 15.0267 8.37033 15.3267 8.00366 15.3267C3.95033 15.3334 0.666992 12.0501 0.666992 8.00008C0.666992 7.77008 0.786992 7.55341 0.983659 7.43341Z",fill:e.color},null,8,l6)]),o6]))]),_:1},8,["color","size","class"]))}}),i6={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},r6=["fill"],c6={name:"PinTopIcon"},p6=b({...c6,props:{color:{default:"#A2A9B0"},size:{default:18},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[(l(),o("svg",i6,[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10.03 1.33325C9.67663 1.33325 9.33663 1.47325 9.08663 1.72325C8.83663 1.97325 8.69663 2.31325 8.69663 2.66659V5.99992C8.69663 6.35325 8.83663 6.69325 9.08663 6.94325C9.33663 7.19325 9.67663 7.33325 10.03 7.33325H13.3633C13.7166 7.33325 14.0566 7.19325 14.3066 6.94325C14.5566 6.69325 14.6966 6.35325 14.6966 5.99992V2.66659C14.6966 2.31325 14.5566 1.97325 14.3066 1.72325C14.0566 1.47325 13.7166 1.33325 13.3633 1.33325H10.03ZM10.03 2.66659H13.3633V5.99992H10.03V2.66659ZM2.69663 8.66659C2.34329 8.66659 2.00329 8.80659 1.75329 9.05659C1.50329 9.30659 1.36329 9.64659 1.36329 9.99992V13.3333C1.36329 13.6866 1.50329 14.0266 1.75329 14.2766C2.00329 14.5266 2.34329 14.6666 2.69663 14.6666H6.02996C6.38329 14.6666 6.72329 14.5266 6.97329 14.2766C7.22329 14.0266 7.36329 13.6866 7.36329 13.3333V9.99992C7.36329 9.64659 7.22329 9.30659 6.97329 9.05659C6.72329 8.80659 6.38329 8.66659 6.02996 8.66659H2.69663ZM2.69663 9.99992H6.02996V13.3333H2.69663V9.99992ZM10.03 8.66659C9.67663 8.66659 9.33663 8.80659 9.08663 9.05659C8.83663 9.30659 8.69663 9.64659 8.69663 9.99992V13.3333C8.69663 13.6866 8.83663 14.0266 9.08663 14.2766C9.33663 14.5266 9.67663 14.6666 10.03 14.6666H13.3633C13.7166 14.6666 14.0566 14.5266 14.3066 14.2766C14.5566 14.0266 14.6966 13.6866 14.6966 13.3333V9.99992C14.6966 9.64659 14.5566 9.30659 14.3066 9.05659C14.0566 8.80659 13.7166 8.66659 13.3633 8.66659H10.03ZM10.03 9.99992H13.3633V13.3333H10.03V9.99992ZM7.35663 4.31992C7.35663 4.68659 7.05663 4.98659 6.68996 4.98659H3.58663L4.59329 5.99325C4.85329 6.25325 4.85329 6.67659 4.59329 6.93659C4.33329 7.19659 3.90996 7.19659 3.64996 6.93659L1.52996 4.81659C1.26996 4.55659 1.26996 4.13325 1.52996 3.87325L1.55329 3.84992L3.64996 1.75325C3.90996 1.49325 4.33329 1.49325 4.59329 1.75325C4.85329 2.01325 4.85329 2.43659 4.59329 2.69659L3.63329 3.65659H6.68996C7.05663 3.65659 7.35663 3.95659 7.35663 4.32325V4.31992Z",fill:e.color},null,8,r6)]))]),_:1},8,["color","size","class"]))}}),d6=t("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},[t("path",{d:"M6.02634 8.66659C6.37967 8.66659 6.71967 8.80659 6.96967 9.05659C7.21967 9.30659 7.35967 9.64659 7.35967 9.99992V13.3333C7.35967 13.6866 7.21967 14.0266 6.96967 14.2766C6.71967 14.5266 6.37967 14.6666 6.02634 14.6666H2.69301C2.33967 14.6666 1.99967 14.5266 1.74967 14.2766C1.49967 14.0266 1.35967 13.6866 1.35967 13.3333V9.99992C1.35967 9.64659 1.49967 9.30659 1.74967 9.05659C1.99967 8.80659 2.33967 8.66659 2.69301 8.66659H6.02634ZM13.3597 8.66659C13.713 8.66659 14.053 8.80659 14.303 9.05659C14.553 9.30659 14.693 9.64659 14.693 9.99992V13.3333C14.693 13.6866 14.553 14.0266 14.303 14.2766C14.053 14.5266 13.713 14.6666 13.3597 14.6666H10.0263C9.67301 14.6666 9.33301 14.5266 9.08301 14.2766C8.83301 14.0266 8.69301 13.6866 8.69301 13.3333V9.99992C8.69301 9.64659 8.83301 9.30659 9.08301 9.05659C9.33301 8.80659 9.67301 8.66659 10.0263 8.66659H13.3597ZM2.69301 13.3333H6.02634V9.99992H2.69301V13.3333ZM10.0263 13.3333H13.3597V9.99992H10.0263V13.3333ZM13.3597 1.33325C13.713 1.33325 14.053 1.47325 14.303 1.72325C14.553 1.97325 14.693 2.31325 14.693 2.66659V5.99992C14.693 6.35325 14.553 6.69325 14.303 6.94325C14.053 7.19325 13.713 7.33325 13.3597 7.33325H10.0263C9.67301 7.33325 9.33301 7.19325 9.08301 6.94325C8.83301 6.69325 8.69301 6.35325 8.69301 5.99992V2.66659C8.69301 2.31325 8.83301 1.97325 9.08301 1.72325C9.33301 1.47325 9.67634 1.33325 10.0263 1.33325H13.3597ZM4.09634 1.75325C4.35634 1.49325 4.77967 1.49325 5.03967 1.75325L7.15967 3.87325C7.41967 4.13325 7.41967 4.55659 7.15967 4.81659L5.03967 6.93659C4.77967 7.19659 4.35634 7.19659 4.09634 6.93659C3.83634 6.67659 3.83634 6.25325 4.09634 5.99325L5.10301 4.98659H1.99967C1.63301 4.98659 1.33301 4.68659 1.33301 4.31992C1.33301 3.95325 1.63301 3.65659 1.99967 3.65659H5.05634L4.09634 2.69659C3.83634 2.43659 3.83634 2.01325 4.09634 1.75325ZM10.0263 5.99992H13.3597V2.66659H10.0263V5.99992Z",fill:"#A2A9B0"})],-1),u6={name:"UnpinIcon"},f6=b({...u6,props:{color:{default:"#A2A9B0"},size:{default:18},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[d6]),_:1},8,["color","size","class"]))}}),C6=t("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.99967 1.33325C1.63301 1.33325 1.33301 1.63325 1.33301 1.99992V7.99992C1.33301 8.17659 1.40301 8.34659 1.52967 8.47325L6.91967 13.8566C7.09301 14.0299 7.29967 14.1666 7.52634 14.2599C7.75301 14.3533 7.99634 14.4033 8.23967 14.4033C8.48301 14.4033 8.72634 14.3533 8.95301 14.2599C9.17967 14.1666 9.38634 14.0299 9.55967 13.8566L13.863 9.55325C14.2097 9.20325 14.4063 8.72992 14.4063 8.23659C14.4063 7.74325 14.2097 7.26992 13.863 6.91992L8.46967 1.52992C8.34301 1.40325 8.17634 1.33325 7.99967 1.33325H1.99967ZM2.66634 4.82992V7.72325L5.23967 10.2933L2.66634 4.82992ZM2.66634 4.82992L5.23967 10.2966L7.85967 12.9133C7.90967 12.9633 7.96967 13.0033 8.03301 13.0299C8.09634 13.0566 8.16634 13.0699 8.23634 13.0699C8.30634 13.0699 8.37634 13.0566 8.43967 13.0299C8.50301 13.0033 8.56301 12.9633 8.61301 12.9133L12.913 8.61325C13.013 8.51325 13.0663 8.37659 13.0663 8.23659C13.0663 8.09659 13.0097 7.95992 12.913 7.85992L7.71967 2.66659H2.66634V4.82992ZM6.08967 6.08992C5.93301 6.24659 5.72301 6.33325 5.49967 6.33325C5.27634 6.33325 5.06634 6.24659 4.90967 6.08992C4.75301 5.93325 4.66634 5.72325 4.66634 5.49992C4.66634 5.27659 4.75301 5.06659 4.90967 4.90992C5.06634 4.75325 5.27634 4.66659 5.49967 4.66659C5.72301 4.66659 5.93301 4.75325 6.08967 4.90992C6.24634 5.06659 6.33301 5.27659 6.33301 5.49992C6.33301 5.72325 6.24634 5.93325 6.08967 6.08992Z",fill:"currentColor"})],-1),_6={name:"TagIcon"},Y=b({..._6,props:{color:{default:"currentColor"},size:{default:16},className:{default:""}},setup(d){return(e,h)=>(l(),$(A,{color:e.color,size:e.size,class:w(e.className)},{default:p(()=>[C6]),_:1},8,["color","size","class"]))}}),m6={key:0,class:"absolute top-0 right-0 w-[52px] h-[32px] flex justify-center items-center gap-[2px] rounded-bl-[20px] rounded-tr-[20px] bg-[#EBF7FF] text-[#343A3F] text-[10px] font-medium leading-[14px]"},v6={class:"flex items-start space-x-4"},h6={class:"relative"},g6={key:1,class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium"},x6=["src","alt"],w6={class:"flex-1 min-w-0 !ml-[14px]"},$6={class:"text-[14px] mb-3 leading-[18px] font-semibold text-gray-900 truncate"},y6={class:"flex items-center text-[10px] font-medium leading-[18px] text-[#A2A9B0]"},V6={class:"truncate"},b6=["title"],H6={class:"text-xs leading-[16px] text-gray-600 line-clamp-3"},k6={class:"absolute bottom-6 left-6"},M6={key:0,class:"flex items-center"},z6={class:"flex items-center gap-1 text-xs text-[#A2A9B0]"},A6={class:"relative top-[2px] w-[24px] h-[24px] p-[3px] flex items-center justify-center rounded-[6px] hover:bg-[#F5F5F5] transition-colors duration-200"},Z6={name:"AppCard"},L6=b({...Z6,props:{app:{}},emits:["delete","cardClick","pinTop","unpinTop"],setup(d){const{t:e}=e3(),h=s=>s==="chat"?e("apps.types.chat"):s==="agent-chat"?e("apps.types.agentchat"):s==="workflow"?e("apps.types.workflow"):s==="advanced-chat"?e("apps.types.advancedchat"):s==="completion"?e("apps.types.completion"):s,x=s=>k3(s);return(s,r)=>{var g;const H=E("el-tooltip");return l(),o("div",{class:w(["bg-white border-[1px] border-solid border-[#E5E5E5] rounded-[20px] h-[186px] w-full min-w-[360px] p-[20px] relative group cursor-pointer transition-all duration-300",{"hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.08)]":!0}]),onClick:r[3]||(r[3]=n=>s.$emit("cardClick",s.app))},[s.app.is_pinned?(l(),o("div",m6," 已置顶 ")):z("",!0),t("div",v6,[t("div",h6,[s.app.icon_type==="emoji"?(l(),o("div",{key:0,class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:K(`background-color: ${s.app.icon_background||"#FFEAD5"}`)},[c(b3,{emojiId:s.app.icon,size:24},null,8,["emojiId"])],4)):s.app.icon_url?(l(),o("div",g6,[t("img",{src:x(s.app.icon_url),alt:V(e)("apps.appIcon"),class:"w-[40px] h-[40px] object-contain rounded-full"},null,8,x6)])):(l(),o("div",{key:2,class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:K(`background-color: ${s.app.icon_background||"#FFEAD5"}`)},_((g=s.app.name)==null?void 0:g.charAt(0)),5)),c(H3,{type:s.app.mode,wrapperClassName:"!rounded-full absolute -bottom-[1px] -right-0.5 w-4 h-4 shadow-sm",class:"h-4 w-4 rounded-full"},null,8,["type"])]),t("div",w6,[t("div",$6,_(s.app.name),1),t("div",y6,[t("div",V6,_(h(s.app.mode).toUpperCase()),1)])])]),t("div",{class:"mt-3",title:s.app.description||V(e)("apps.noDescription")},[t("p",H6,_(s.app.description||V(e)("apps.noDescription")),1)],8,b6),t("div",k6,[s.app.tags&&s.app.tags.length>0?(l(),o("div",M6,[t("span",z6,[c(Y,{size:"14",color:"#A2A9B0"}),t("span",null,_(s.app.tags.map(n=>n.name).join("，")),1)])])):z("",!0)]),t("div",{class:"absolute bottom-6 right-6",onClick:r[2]||(r[2]=j(()=>{},["stop"]))},[c(H,{effect:"light",content:s.app.is_pinned?V(e)("apps.unpinFromTop"):V(e)("apps.pinToTop"),placement:"bottom"},{default:p(()=>[t("div",A6,[s.app.is_pinned?(l(),$(f6,{key:0,size:"18",color:"#A2A9B0",class:"relative top-[-1px]",onClick:r[0]||(r[0]=j(n=>s.$emit("unpinTop",s.app),["stop"]))})):(l(),$(p6,{key:1,size:"18",color:"#A2A9B0",class:"relative top-[-1px]",onClick:r[1]||(r[1]=j(n=>s.$emit("pinTop",s.app),["stop"]))}))])]),_:1},8,["content"])])])}}}),B6={class:"relative"},F6={class:"p-[1px]"},T6={class:"text-[14px] leading-[22px] text-[#A2A9B0] font-normal flex-1 truncate"},E6={key:0,class:"text-xs font-medium leading-[18px] text-[#A2A9B0]"},N6={key:1,class:"p-[1px]"},S6={class:"relative rounded-lg bg-white"},I6={class:"p-3"},j6={class:"max-h-72 overflow-auto px-2 py-1"},D6=["onClick"],U6=["title"],O6={key:0,class:"text-[#129BFE]"},P6={key:1,class:"flex flex-col items-center gap-1 p-3"},J6={class:"text-xs leading-[14px] text-[#C9CDD4]"},q6={__name:"TagFilter",props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue"],setup(d,{emit:e}){const{t:h}=e3(),x=d,s=e,r=v(!1),H=v([]),g=v(""),n=v([...x.modelValue]);W(()=>x.modelValue,a=>{JSON.stringify(n.value)!==JSON.stringify(a)&&(n.value=[...a])},{deep:!0}),W(n,a=>{JSON.stringify(x.modelValue)!==JSON.stringify(a)&&s("update:modelValue",a)},{deep:!0});const Z=R(()=>g.value?H.value.filter(a=>a.name.toLowerCase().includes(g.value.toLowerCase())):H.value),L=G(()=>{},300),M=a=>{const m=n.value.indexOf(a);m>-1?n.value.splice(m,1):n.value.push(a)},O=()=>{n.value=[]},F=a=>n.value.includes(a),P=a=>{const m=H.value.find(u=>u.id===a);return m?m.name:""},I=a=>{const m=document.querySelector(".tag-filter-popover"),u=document.querySelector(".tag-filter-trigger");r.value&&m&&!m.contains(a.target)&&u&&!u.contains(a.target)&&(r.value=!1)};return s3(async()=>{try{const a=await g3();H.value=a.data,Q(()=>{document.addEventListener("click",I)})}catch(a){console.error(h("apps.tags.loadFailed"),a)}}),u3(()=>{document.removeEventListener("click",I)}),(a,m)=>{const u=E("el-icon"),J=E("el-input"),q=E("el-popover");return l(),o("div",B6,[c(q,{visible:r.value,trigger:"click",placement:"bottom-start",width:180,"popper-class":"tag-filter-popover","hide-after":0,onHide:m[2]||(m[2]=y=>r.value=!1)},{reference:p(()=>[t("div",{class:w(["flex h-10 cursor-pointer items-center gap-2 rounded-lg bg-white px-4 tag-filter-trigger w-[148px]",{"tag-filter-active":r.value}]),onClick:m[0]||(m[0]=y=>r.value=!r.value)},[t("div",F6,[c(u,{size:"16",color:"#697077"},{default:p(()=>[c(Y)]),_:1})]),t("div",T6,[n.value.length===0?(l(),o(S,{key:0},[D(_(a.$t("apps.tags.all")),1)],64)):(l(),o(S,{key:1},[D(_(P(n.value[0])),1)],64))]),n.value.length>1?(l(),o("div",E6," +"+_(n.value.length-1),1)):z("",!0),n.value.length===0?(l(),o("div",N6,[c(u,{size:"16",color:"#A2A9B0"},{default:p(()=>[c(V(C3))]),_:1})])):(l(),o("div",{key:2,class:"group cursor-pointer p-[1px]",onClick:j(O,["stop"])},[c(u,{size:"16",color:"#A2A9B0"},{default:p(()=>[c(V(_3))]),_:1})]))],2)]),default:p(()=>[t("div",S6,[t("div",I6,[c(J,{modelValue:g.value,"onUpdate:modelValue":m[1]||(m[1]=y=>g.value=y),placeholder:a.$t("common.search"),size:"default",clearable:"",class:"tag-search-input",onInput:V(L)},{prefix:p(()=>[c(l3)]),_:1},8,["modelValue","placeholder","onInput"])]),t("div",j6,[Z.value.length>0?(l(!0),o(S,{key:0},X(Z.value,y=>(l(),o("div",{key:y.id,class:"flex cursor-pointer items-center gap-2 rounded-lg px-4 py-3 hover:bg-[#F2F3F5]",onClick:t3=>M(y.id)},[t("div",{title:y.name,class:"grow truncate text-sm leading-5 text-[#4E5969]"},_(y.name),9,U6),F(y.id)?(l(),o("div",O6,[c(u,null,{default:p(()=>[c(V(f3))]),_:1})])):z("",!0)],8,D6))),128)):(l(),o("div",P6,[c(u,{size:24,class:"text-[#C9CDD4]"},{default:p(()=>[c(Y)]),_:1}),t("div",J6,_(a.$t("apps.tags.noTag")),1)]))])])]),_:1},8,["visible"])])}}},K6=U(q6,[["__scopeId","data-v-18381e4b"]]),R6={class:"apps-container w-full h-full !pt-[50px]"},W6={class:"page-header mx-6 pb-4 border-0 border-b border-solid border-[#E5E5E5]"},G6={class:"text-[24px] font-semibold text-[#121619] leading-[32px]"},Q6={class:"text-[15px] font-medium text-[#697077] leading-[20px] mt-1"},X6={class:"flex justify-between items-center mt-4 mb-4 px-6"},Y6={class:"flex border-b"},ee=["onClick"],te={key:0,class:"absolute bottom-0 left-1/2 w-6 h-[2px] bg-[#129BFE] transform -translate-x-1/2"},se={class:"flex items-center space-x-4"},le={class:"flex items-center space-x-2"},oe={class:"relative"},ae={key:0,class:"col-span-full relative flex h-full w-full flex-col items-center justify-center py-20 mt-[120px]"},ne={class:"mb-6"},ie={class:"mb-2 text-lg font-medium text-gray-700"},re={class:"mb-6 text-sm text-gray-500"},ce={class:"col-span-full"},pe={key:0,class:"flex justify-center items-center py-4 text-gray-500"},de={class:"ml-2 text-sm"},ue={key:1,class:"flex justify-center py-4 text-gray-500 text-sm"},fe={__name:"index",setup(d){const{t:e}=e3(),h=m3(),x=v(null),s=v([]),r=v(1),H=v(100),g=v(!1),n=v(!1),Z=v(""),L=v([]),M=v("all"),O=[{value:"all",icon:q3},{value:"chat",icon:G3},{value:"agent-chat",icon:e6},{value:"workflow",icon:n6}],F=v(!1),P=v(e("apps.delete")),I=v(e("apps.confirmDelete")),a=v(null),m=i=>{M.value=i,r.value=1,n.value=!1,u()},u=async(i=!1)=>{if(!g.value){g.value=!0;try{const f={page:r.value,limit:H.value};Z.value&&(f.name=Z.value),L.value.length>0&&(f.tag_ids=L.value.join(",")),M.value!=="all"&&(f.mode=M.value);const N=await w3(f),{installed_apps:k,has_more:C}=N.data;for(const T of k)T.app&&(delete T.app.id,Object.assign(T,T.app));if(i){if(k.length===0){n.value=!0;return}s.value.push(...k)}else s.value=k;n.value=!C,k.length>0&&r.value++,Q(()=>{setTimeout(()=>{x.value},300)})}catch(f){console.error("加载数据失败:",f),B.error(e("apps.loadFailed"))}finally{setTimeout(()=>{g.value=!1},200)}}};W(L,()=>{r.value=1,n.value=!1,u()},{deep:!0});const J=G(()=>{r.value=1,n.value=!1,u()},300),q=i=>{a.value=i.id,F.value=!0},y=()=>{F.value=!1,a.value=null},t3=async()=>{F.value=!1;try{B.success(e("common.success")),s.value=s.value.filter(i=>i.id!==a.value)}catch(i){console.error("删除失败:",i),B.error(e("common.error"))}finally{a.value=null}},o3=i=>{console.log("点击应用:",i),h.push(`/app-chat/${i.id}`)},a3=async i=>{try{await V3(i.id),B.success(e("apps.pinSuccess")),r.value=1,n.value=!1,u()}catch(f){console.error("置顶失败:",f),B.error(e("apps.pinFailed"))}},n3=async i=>{try{await y3(i.id),B.success(e("apps.unpinSuccess")),r.value=1,n.value=!1,u()}catch(f){console.error("取消置顶失败:",f),B.error(e("apps.unpinFailed"))}},i3=()=>{$3("/apps")},r3=()=>{if(!x.value)return;const i=G(()=>{const f=x.value;if(!f)return;const{scrollTop:N,scrollHeight:k,clientHeight:C}=f;k-N-C<100&&!g.value&&!n.value&&u(!0)},100);x.value.addEventListener("scroll",i)};return s3(()=>{u(),Q(()=>{r3()})}),(i,f)=>{const N=E("el-input"),k=E("el-icon");return l(),o("div",R6,[t("div",W6,[t("h1",G6,_(i.$t("apps.pageTitle")),1),t("p",Q6,_(i.$t("apps.pageSubtitle")),1)]),t("div",X6,[t("div",Y6,[(l(),o(S,null,X(O,(C,T)=>t("div",{key:T,class:w(["mr-[36px] font-medium text-[14px] h-[40px] flex items-center py-2 px-0 cursor-pointer relative",M.value===C.value?"text-[#129BFE] font-medium":"text-gray-500 hover:text-gray-700"]),onClick:Ce=>m(C.value)},[(l(),$(v3(C.icon),{class:"mr-2",color:M.value===C.value?"#129BFE":"#343A3F"},null,8,["color"])),D(" "+_(i.$t(`apps.types.${C.value==="all"?"all":C.value.replace("-","")}`))+" ",1),M.value===C.value?(l(),o("div",te)):z("",!0)],10,ee)),64))]),t("div",se,[t("div",le,[c(K6,{modelValue:L.value,"onUpdate:modelValue":f[0]||(f[0]=C=>L.value=C)},null,8,["modelValue"])]),t("div",oe,[c(N,{modelValue:Z.value,"onUpdate:modelValue":f[1]||(f[1]=C=>Z.value=C),placeholder:i.$t("apps.search"),onInput:V(J),size:"default",class:"w-[200px] search-input"},{prefix:p(()=>[c(l3)]),_:1},8,["modelValue","placeholder","onInput"])])])]),t("div",{ref_key:"containerRef",ref:x,class:w(["grid auto-rows-[186px] gap-6 overflow-y-auto h-[calc(100vh-280px)] custom-scrollbar p-2",s.value.length<=1?"grid-cols-[repeat(auto-fit,minmax(360px,0.5fr))]":"grid-cols-[repeat(auto-fit,minmax(360px,1fr))]"])},[(l(!0),o(S,null,X(s.value,C=>(l(),$(L6,{key:C.id,app:C,onDelete:q,onCardClick:o3,onPinTop:a3,onUnpinTop:n3},null,8,["app"]))),128)),!g.value&&s.value.length===0?(l(),o("div",ae,[t("div",ne,[c(I3,{size:"160"})]),t("div",ie,_(i.$t("apps.emptyState.title")),1),t("div",re,_(i.$t("apps.emptyState.description")),1),c(U3,{onClick:i3},{default:p(()=>[D(_(i.$t("apps.emptyState.createButton"))+" ",1),c(L3,{size:"18"})]),_:1})])):z("",!0),t("div",ce,[g.value?(l(),o("div",pe,[c(k,{class:"is-loading"},{default:p(()=>[c(V(h3))]),_:1}),t("span",de,_(i.$t("common.loading"))+"...",1)])):z("",!0),n.value&&s.value.length>0?(l(),o("div",ue,_(i.$t("apps.noMoreData")),1)):z("",!0)])],2),c(x3,{show:F.value,title:P.value,message:I.value,onClose:y,onConfirm:t3},null,8,["show","title","message"])])}}},ge=U(fe,[["__scopeId","data-v-bca2a819"]]);export{ge as default};
