'use client';

import LogoSite from '@/app/components/base/logo/logo-site';
import { useEffect, useState } from 'react';

import { fetchGetVersion } from '@/service/common';
import type { GetVersionResponse } from '@/models/common';
import { useTranslation } from 'react-i18next';

export default function AccountAbout() {
  const { t } = useTranslation();
  const [versionsData, setVersionsData] = useState<GetVersionResponse | null>(null);
  useEffect(() => {
    fetchGetVersion().then((res: GetVersionResponse) => {
      setVersionsData(res);
    });
  }, []);

  return (
    <>
      <div className="mx-auto mt-5 w-[720px]">
        <div className="relative pt-4">
          <div>
            <LogoSite className="w-42 mt-15 mx-auto h-11" />
            {versionsData && (
              <div className="">
                <div className="my-8">
                  <div className="mb-2 text-center text-base font-medium text-text-primary">
                    Version {versionsData.current_version}
                  </div>
                  <div className="text-center text-[15px] text-text-primary">
                    {versionsData.author}
                  </div>
                </div>
                <div className="mt-2 max-h-[500px] overflow-auto">
                  <table className="w-full table-fixed border-separate border-spacing-0 overflow-hidden rounded-xl border border-divider-regular text-sm">
                    <thead className="text-text-primary">
                      <tr className="bg-state-base-hover font-medium">
                        <td className="w-[66px] p-3">{t('common.version.version')}</td>
                        <td className="w-[120px] p-3">{t('common.version.date')}</td>
                        <td className="p-3">{t('common.version.description')}</td>
                      </tr>
                    </thead>
                    <tbody className="text-text-secondary">
                      {versionsData.versions.map((v, index) => (
                        <tr
                          key={index}
                          className="cursor-pointer hover:bg-background-default-hover"
                        >
                          <td
                            className={`w-[66px] p-3 ${
                              index !== versionsData.versions.length - 1
                                ? 'border-b border-divider-subtle'
                                : ''
                            }`}
                          >
                            {v.version}
                          </td>
                          <td
                            className={`w-[120px] p-3 ${
                              index !== versionsData.versions.length - 1
                                ? 'border-b border-divider-subtle'
                                : ''
                            }`}
                          >
                            {v.date}
                          </td>
                          <td
                            className={`p-3 ${
                              index !== versionsData.versions.length - 1
                                ? 'border-b border-divider-subtle'
                                : ''
                            }`}
                            dangerouslySetInnerHTML={{ __html: v.description }}
                          ></td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
