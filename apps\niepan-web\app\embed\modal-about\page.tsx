'use client';

import ModalAccountAbout from '@/app/components/header/new-account-setting/account-about';
import Loading from '@/app/components/base/loading';
import { useAppContext } from '@/context/app-context';
import Button from '@/app/components/base/button';
import { RiCloseLine } from '@remixicon/react';

export default function EmbedDataSourcesPage() {
  const { isLoadingCurrentWorkspace } = useAppContext();

  const handleClose = () => {
    // 向父窗口发送关闭消息
    window.parent.postMessage({ type: 'close-modal', from: 'niepan-embed' }, '*');
  };

  return (
    <div className="h-full overflow-hidden rounded-xl bg-components-panel-bg p-9">
      <div className="">
        <div className="fixed right-9 top-9 z-[9999]">
          <Button variant="tertiary" size="large" className="px-2" onClick={handleClose}>
            <RiCloseLine className="h-5 w-5" />
          </Button>
        </div>
        {isLoadingCurrentWorkspace ? (
          <Loading type="area" />
        ) : (
          <>
            <ModalAccountAbout />
          </>
        )}
      </div>
    </div>
  );
}
