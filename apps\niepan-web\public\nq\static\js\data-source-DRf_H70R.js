import{w as s,x as t}from"./index-C4ad4gme.js";import{d as r,r as c,z as l,c as n,o as u,b as i}from"./pnpm-pnpm-B4aX-tnA.js";const d={class:"bg-white rounded-[20px] overflow-hidden h-[calc(100vh-120px)] relative z-50"},p=["src"],g=r({__name:"data-source",setup(f){const a=s(),{globalConfig:e}=t(),o=c("");return l(()=>{a.setActiveMenu("data_source"),e.workspaceSettings&&e.workspaceSettings.dataSources?o.value=e.workspaceSettings.dataSources:(console.warn("数据来源嵌入URL未配置，请检查config.js文件"),o.value="http://localhost:3000/embed/data-sources")}),(m,_)=>(u(),n("div",d,[i("iframe",{src:o.value,class:"w-full h-full border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""},null,8,p)]))}});export{g as default};
