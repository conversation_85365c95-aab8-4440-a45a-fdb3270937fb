import { memo, useState } from 'react';
import { RiDownloadLine, RiEyeLine } from '@remixicon/react';
import FileTypeIcon from '../file-type-icon';
import { downloadFile, fileIsUploaded, getFileAppearanceType, getFileExtension } from '../utils';
import FileImageRender from '../file-image-render';
import type { FileEntity } from '../types';
import ActionButton from '@/app/components/base/action-button';
import ProgressCircle from '@/app/components/base/progress-bar/progress-circle';
import { formatFileSize } from '@/utils/format';
import cn from '@/utils/classnames';
import { ReplayLine } from '@/app/components/base/icons/src/vender/other';
import { SupportUploadFileTypes } from '@/app/components/workflow/types';
import ImagePreview from '@/app/components/base/image-uploader/image-preview';
import type { SVGProps } from 'react';

type FileInAttachmentItemProps = {
  file: FileEntity;
  showDeleteAction?: boolean;
  showDownloadAction?: boolean;
  onRemove?: (fileId: string) => void;
  onReUpload?: (fileId: string) => void;
  canPreview?: boolean;
};

const RiDeleteBinLineIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      className={className ?? ''}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.72508 0.875C4.44216 0.875 4.19716 1.07917 4.1505 1.35917L3.88217 2.91667H1.75008C1.42925 2.91667 1.16675 3.17917 1.16675 3.5C1.16675 3.82083 1.42925 4.08333 1.75008 4.08333H2.66008L3.20841 12.5796C3.22883 12.8858 3.48258 13.125 3.79175 13.125H10.2084C10.5147 13.125 10.7713 12.8858 10.7917 12.5796L11.3401 4.08333H12.2501C12.5709 4.08333 12.8334 3.82083 12.8334 3.5C12.8334 3.17917 12.5709 2.91667 12.2501 2.91667H10.118L9.84967 1.35917C9.803 1.07917 9.558 0.875 9.27508 0.875L4.72508 0.875ZM8.93383 2.91667H5.06633L5.21508 2.04167H8.78217L8.93092 2.91667H8.93383ZM4.34008 11.9583L3.83258 4.08333H10.1705L9.663 11.9583H4.34008ZM5.54175 9.625C5.22091 9.625 4.95841 9.8875 4.95841 10.2083C4.95841 10.5292 5.22091 10.7917 5.54175 10.7917H8.45842C8.77925 10.7917 9.04175 10.5292 9.04175 10.2083C9.04175 9.8875 8.77925 9.625 8.45842 9.625H5.54175Z"
        fill="currentColor" // 使用 currentColor 让样式由 CSS 控制
      />
    </svg>
  );
};

const FileInAttachmentItem = ({
  file,
  showDeleteAction,
  showDownloadAction = true,
  onRemove,
  onReUpload,
  canPreview,
}: FileInAttachmentItemProps) => {
  const { id, name, type, progress, supportFileType, base64Url, url, isRemote } = file;
  const ext = getFileExtension(name, type, isRemote);
  const isImageFile = supportFileType === SupportUploadFileTypes.image;
  const [imagePreviewUrl, setImagePreviewUrl] = useState('');
  return (
    <>
      <div
        className={cn(
          'flex h-12 items-center rounded-lg border-[0.5px] border-components-panel-border bg-components-panel-on-panel-item-bg pr-3 shadow-xs',
          progress === -1 && 'border-state-destructive-border bg-state-destructive-hover'
        )}
      >
        <div className="flex h-12 w-12 items-center justify-center">
          {isImageFile && <FileImageRender className="h-8 w-8" imageUrl={base64Url || url || ''} />}
          {!isImageFile && <FileTypeIcon type={getFileAppearanceType(name, type)} size="lg" />}
        </div>
        <div className="mr-1 w-0 grow">
          <div
            className="system-xs-medium mb-0.5 flex items-center truncate text-text-secondary"
            title={file.name}
          >
            <div className="truncate">{name}</div>
          </div>
          <div className="system-2xs-medium-uppercase flex items-center text-text-tertiary">
            {ext && <span>{ext.toLowerCase()}</span>}
            {ext && <span className="system-2xs-medium mx-1">•</span>}
            {!!file.size && <span>{formatFileSize(file.size)}</span>}
          </div>
        </div>
        <div className="flex shrink-0 items-center">
          {progress >= 0 && !fileIsUploaded(file) && (
            <ProgressCircle className="mr-2.5" percentage={progress} />
          )}
          {progress === -1 && (
            <ActionButton className="mr-1" onClick={() => onReUpload?.(id)}>
              <ReplayLine className="h-4 w-4 text-text-tertiary" />
            </ActionButton>
          )}
          {showDeleteAction && (
            <ActionButton onClick={() => onRemove?.(id)}>
              <RiDeleteBinLineIcon className="h-4 w-4" />
            </ActionButton>
          )}
          {canPreview && isImageFile && (
            <ActionButton className="mr-1" onClick={() => setImagePreviewUrl(url || '')}>
              <RiEyeLine className="h-4 w-4" />
            </ActionButton>
          )}
          {showDownloadAction && (
            <ActionButton
              onClick={e => {
                e.stopPropagation();
                downloadFile(url || base64Url || '', name);
              }}
            >
              <RiDownloadLine className="h-4 w-4" />
            </ActionButton>
          )}
        </div>
      </div>
      {imagePreviewUrl && canPreview && (
        <ImagePreview title={name} url={imagePreviewUrl} onCancel={() => setImagePreviewUrl('')} />
      )}
    </>
  );
};

export default memo(FileInAttachmentItem);
