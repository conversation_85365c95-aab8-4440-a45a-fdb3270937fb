import{d as r,u as g,c as A,o,b as t,t as a}from"./pnpm-pnpm-B4aX-tnA.js";import{aV as p}from"./index-C4ad4gme.js";const m="data:image/png;base64,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",f="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAExSURBVHgB7ZTNTcNAEIXfs5MrsggHjqYD6CB0QAcJEvGdCpJUgUSQcDqgA1xCSvAdjCxxA3mHNYID2GYHsUiJ5O/iv+f5Vt4ZAz27Dn8TPljJWCBzAY7tZWRfzgLh5UPCjbaGWji6kYmIpK1FyGlxwTUUqISHVxK/hHJv03FHpNwb8Cg/Z+mqFUDBa4jxD7Ka6NlUZ1CgEgpN7MwYOjNqISXI3RlxZtTCaog7e+jeHyKvhmEGX8LSNgNt+3c9pzFLm8nhS1hTJEyN8IRE9rmO+jwAT4tkkKLng87B//4bg47SFtzYGVl3feZW4f5K5nayFvgTXDzNuHQKR9cyFcotPFA31OOM2dd7jSXIBJ6otwSNRTRC73vmSdis1TaH2gbREGmE/8oWCAU5fEGPtXq2hjf+1WBh5QrMqAAAAABJRU5ErkJggg==",w="/nq/static/png/20250114-153443-BKxvaETg.png",h={class:"flex items-center gap-2 mb-4 min-w-0"},x=t("div",{class:"w-[24px] h-[24px] flex-shrink-0"},[t("img",{src:m,alt:"问答图标",class:"w-full h-full"})],-1),C={class:"text-[#121619] text-base font-semibold truncate min-w-0"},B={class:"flex flex-col gap-2 w-full min-w-0 h-[calc(100%-44px)]"},v={class:"flex items-start gap-3 w-full min-w-0"},T=t("div",{class:"w-4 h-4 flex-shrink-0 mt-[6px] relative left-[4px]"},[t("img",{src:f,alt:"提问者",class:"w-full h-full"})],-1),Q={class:"bg-white py-2 px-3 rounded-[6px] flex-1 min-w-0"},k=["title"],U={class:"flex items-start gap-3 w-full min-w-0 flex-1"},V=t("div",{class:"w-4 h-4 flex-shrink-0 mt-[6px] relative left-[4px]"},[t("img",{src:w,alt:"回答者",class:"w-full h-full"})],-1),E={class:"bg-white py-2 px-3 rounded-[6px] flex-1 min-w-0 overflow-auto"},b=["title"],F={key:1,class:"flex"},N=t("div",{class:"text-[#A3ADB8] text-xs italic flex text-left"}," 暂无回答内容 ",-1),j=[N],K=r({__name:"LastQuestionComponent",props:{data:{}},setup(n){const c=n;g();const d=()=>{const{question:s,type:i,targetId:l}=c.data;let e="";i==="app"?e=`/chat?app_id=${l}&show_history=1`:e=`/worker?qid=${l}&show_history=1`,p(e)};return(s,i)=>(o(),A("div",{class:"h-[258px] p-4 rounded-[20px] border border-solid border-[#F8E3E1] bg-gradient-to-b from-[#FFE7E6] to-[rgba(255,255,255,0)] to-75% w-full min-w-0 cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5",onClick:d},[t("div",h,[x,t("span",C,a(s.data.title),1)]),t("div",B,[t("div",v,[T,t("div",Q,[t("div",{class:"text-[#343A3F] text-xs leading-5 line-clamp-2 break-all",title:s.data.question},a(s.data.question),9,k)])]),t("div",U,[V,t("div",E,[s.data.answer&&s.data.answer.trim()?(o(),A("div",{key:0,class:"text-[#343A3F] text-xs leading-5 overflow-hidden line-clamp-5 break-all",title:s.data.answer},a(s.data.answer),9,b)):(o(),A("div",F,j))])])])]))}});export{K as default};
