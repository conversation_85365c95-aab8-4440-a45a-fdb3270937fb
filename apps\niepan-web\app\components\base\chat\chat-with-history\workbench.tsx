import type { FC } from 'react';
import cn from '@/utils/classnames';
import { useChatWithHistoryContext } from './context';
import { useWorkbenchComponents } from './hooks/useWorkbenchComponents';

type WorkbenchProps = {
  isPanel?: boolean;
};

const Workbench: FC<WorkbenchProps> = ({ isPanel }) => {
  const { isMobile } = useChatWithHistoryContext();
  const { renderComponents } = useWorkbenchComponents();

  return (
    <div
      className={cn(
        'flex h-full flex-col overflow-hidden',
        isPanel && 'rounded-2xl bg-components-panel-bg'
      )}
    >
      {isPanel && (
        <div className="flex items-center justify-between px-4 py-3">
          <div className="body-lg-semibold text-text-primary">工作台</div>
        </div>
      )}

      <div className="grow overflow-auto px-4 pb-4">{renderComponents('main')}</div>
    </div>
  );
};

export default Workbench;
