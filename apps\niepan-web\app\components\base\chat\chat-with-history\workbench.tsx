import type { <PERSON> } from 'react';
import cn from '@/utils/classnames';
import { useChatWithHistoryContext } from './context';

type WorkbenchProps = {
  isPanel?: boolean;
};

const Workbench: FC<WorkbenchProps> = ({ isPanel }) => {
  const { isMobile } = useChatWithHistoryContext();

  return (
    <div
      className={cn(
        'flex h-full flex-col overflow-hidden',
        isPanel && 'rounded-2xl bg-components-panel-bg'
      )}
    >
      {isPanel && (
        <div className="flex items-center justify-between px-4 py-3">
          <div className="body-lg-semibold text-text-primary">工作台</div>
        </div>
      )}

      <div className="grow overflow-auto px-4 pb-4">
        <div className="body-md mb-4 text-text-secondary">工作台区域内容</div>
      </div>
    </div>
  );
};

export default Workbench;
