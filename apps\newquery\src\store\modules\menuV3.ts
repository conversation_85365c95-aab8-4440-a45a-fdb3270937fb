import { defineStore } from 'pinia';
import { ref, computed, markRaw } from 'vue';
import DepartmentIcon from '@/components/icons/DepartmentIcon.vue';
import UserIcon from '@/components/icons/UserIcon.vue';
import RoleIcon from '@/components/icons/RoleIcon.vue';
import LicenseIcon from '@/components/icons/LicenseIcon.vue';
import ModelProviderIcon from '@/components/icons/ModelProviderIcon.vue';
import DataSourceIcon from '@/components/icons/DataSourceIcon.vue';
import ApiExtensionIcon from '@/components/icons/ApiExtensionIcon.vue';
import SystemConfigIcon from '@/components/icons/SystemConfigIcon.vue';

export interface SubMenuItem {
  key: string;
  label: string;
  path: string;
  icon?: any;
}

export interface MenuItem {
  key: string;
  label: string;
  path: string;
  icon?: any;
  children?: SubMenuItem[];
  developing?: boolean; // 是否正在开发中
  isTitle?: boolean; // 是否为标题
  permissions?: string[]; // 需要的权限列表，满足其中一个即可查看
  roles?: string[]; // 需要的角色列表，满足其中一个即可查看
}

export const useMenuV3Store = defineStore('menuV3', () => {
  // 菜单配置
  const menuItems = ref<MenuItem[]>([
    {
      key: 'enterprise_title',
      label: '企业管理',
      path: '',
      isTitle: true,
    },
    {
      key: 'department',
      label: '部门管理',
      path: '/setting/department',
      icon: markRaw(DepartmentIcon),
    },
    {
      key: 'user',
      label: '用户管理',
      path: '/setting/user',
      icon: markRaw(UserIcon),
    },
    {
      key: 'role',
      label: '角色管理',
      path: '/setting/role',
      icon: markRaw(RoleIcon),
    },

    {
      key: 'workspace_title',
      label: '工作空间设置',
      path: '',
      isTitle: true,
    },
    {
      key: 'model_provider',
      label: '模型供应商',
      path: '/setting/model-provider',
      icon: markRaw(ModelProviderIcon),
    },
    {
      key: 'data_source',
      label: '数据来源',
      path: '/setting/data-source',
      icon: markRaw(DataSourceIcon),
    },
    {
      key: 'api_extension',
      label: 'API扩展',
      path: '/setting/api-extension',
      icon: markRaw(ApiExtensionIcon),
    },
    {
      key: 'license',
      label: '授权管理',
      path: '/setting/license',
      icon: markRaw(LicenseIcon),
      roles: ['super'], // 只有超级管理员可见
    },
    {
      key: 'system_title',
      label: '系统设置',
      path: '',
      isTitle: true,
    },
    {
      key: 'system-config',
      label: '设置',
      path: '/setting/system-config',
      icon: markRaw(SystemConfigIcon),
      roles: ['super', 'normal'], // 超级管理员、普通管理员可见
    },
  ]);

  // 当前激活的一级菜单
  const activeMenu = ref<string>('department');

  // 当前激活的二级菜单
  const activeSubMenu = ref<string>('');

  // 计算当前是否应该显示二级菜单
  const showSubMenu = computed(() => {
    const currentMenu = menuItems.value.find(item => item.key === activeMenu.value);
    return !!currentMenu?.children?.length;
  });

  // 获取当前激活菜单的二级菜单
  const currentSubMenus = computed(() => {
    const currentMenu = menuItems.value.find(item => item.key === activeMenu.value);
    return currentMenu?.children || [];
  });

  // 设置当前激活的一级菜单
  const setActiveMenu = (key: string) => {
    activeMenu.value = key;
  };

  // 设置当前激活的二级菜单
  const setActiveSubMenu = (key: string) => {
    activeSubMenu.value = key;
  };

  return {
    menuItems,
    activeMenu,
    activeSubMenu,
    showSubMenu,
    currentSubMenus,
    setActiveMenu,
    setActiveSubMenu,
  };
});
