// router/index.js
import { createRouter, createWebHistory } from 'vue-router';
import LoginView from '../views/login.vue';
import IndexView from '../views/index.vue';
import KBaseListView from '../views/kbase/kBaseList.vue';
import QueryAppListView from '@/views/queryapp/queryappList.vue';
import DocListListView from '@/views/kbase/docList.vue';
import DocAddFileView from '@/views/kbase/docAddFile.vue';
import QueryappDetailView from '@/views/queryapp/queryappDetail.vue';
import SiteuserListView from '@/views/department/siteuserList.vue';
import DepartmentListView from '@/views/department/departmentList.vue';
import OrganizationView from '@/views/department/organization.vue';
import SiteuserAddView from '@/views/department/siteuserAdd.vue';
import DepartmentAddView from '@/views/department/departmentAdd.vue';
import DepartmentPositionListView from '@/views/department/departmentPositionList.vue';
import SiteuserPositionListView from '@/views/department/siteuserPositionList.vue';
import DocFragmentLIstView from '@/views/kbase/docFragmentList.vue';
import Tool from '@/views/tool/index.vue';
import ToolEditView from '@/views/tool/components/toolEdit.vue';
import ToolSettingView from '@/views/tool/components/toolSetting.vue';
import ToolTestView from '@/views/tool/components/toolTest.vue';
import TestView from '@/views/test.vue';
import DocAddTextView from '@/views/kbase/docAddText.vue';
import TestDocView from '@/views/testDoc.vue';
import AIOSIndexView from '@/views/aiosIndex.vue';
import FileTemplateListView from '@/views/template/fileTemplateList.vue';
import FileTemplateAddView from '@/views/template/fileTemplateAdd.vue';
import FeiMaIndexView from '@/views/feima/index.vue';
import AppLayout from '@/components/layout/AppLayout.vue';
import AdminLayout from '@/components/layout/v2/AdminLayout.vue';
import AdminLayoutV3 from '@/components/layout/v3/AdminLayout.vue';

import timerManager from '@/utils/timerManager'; //定时器全局文件
import { decodeUrlAndBase64 } from '@/utils/encoding';

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Login',
    component: LoginView,
    meta: { title: '登录-INTELLIDO' },
  },
  {
    path: '/login',
    name: 'LoginQuery',
    component: LoginView,
    props: route => ({
      sso: route.query.sso,
      ssourl: route.query.ssourl,
    }), //ssourl:来源页面地址  sso:1 其他系统跳转来的
    meta: { title: '登录-INTELLIDO' },
  },
  {
    path: '/worker',
    name: 'Worker',
    component: IndexView,
    meta: { requiresAuth: true, title: '数字员工-INTELLIDO' },
  },
  {
    path: '/worker', // 路径参数
    name: 'IndexWithQid',
    component: IndexView,
    meta: { requiresAuth: true, title: '数字员工-INTELLIDO' },
    props: route => ({ qid: route.query.qid }), // 将查询参数传递给组件,  跳转到首页时，加参数qid (应用id)
  },
  {
    path: '/kbase-list',
    name: 'KBaseList',
    component: KBaseListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/doc-list/:kid',
    name: 'DocList',
    component: DocListListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/queryapp-list',
    name: 'QueryAppList',
    component: QueryAppListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/doc-add-file/:kid/:dcid?', //kid知识库id  dcid 文档id
    name: 'DocAddFile',
    component: DocAddFileView,
    meta: { requiresAuth: true },
  },
  {
    path: '/queryapp-detail/:qid',
    name: 'QueryappDetail',
    component: QueryappDetailView,
    meta: { requiresAuth: true },
  },
  {
    path: '/siteuser-list/',
    name: 'SiteuserList',
    component: SiteuserListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/department-list/',
    name: 'DepartmentList',
    component: DepartmentListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/organization/',
    name: 'Organization',
    component: OrganizationView,
    meta: { requiresAuth: true },
  },
  {
    path: '/siteuser-add/:sid?', //id后面加?，说明访问此连接时，加不加参数都可以
    name: 'SiteuserAdd',
    component: SiteuserAddView,
    meta: { requiresAuth: true },
  },
  {
    path: '/department-add/:did?', //id后面加?，说明访问此连接时，加不加参数都可以，部门id
    name: 'DepartmentAdd',
    component: DepartmentAddView,
    meta: { requiresAuth: true },
  },
  {
    path: '/department-position-list/:did', //id后面加?，说明访问此连接时，加不加参数都可以
    name: 'DepartmentPositionList',
    component: DepartmentPositionListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/siteuser-position-list/:sid', //id后面加?，说明访问此连接时，加不加参数都可以  用户id
    name: 'SiteuserPositionList',
    component: SiteuserPositionListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/docfragment-list/:kid/:dcid', //id后面加?，说明访问此连接时，加不加参数都可以  dcid:document的id   kid:kbase的id
    name: 'DocFragmentLIst',
    component: DocFragmentLIstView,
    meta: { requiresAuth: true },
  },
  {
    path: '/tool-list/',
    name: 'ToolList',
    component: Tool,
    meta: { requiresAuth: true },
  },
  {
    path: '/tool-edit/:tid?',
    name: 'ToolEdit',
    component: ToolEditView,
    meta: { requiresAuth: true },
  },
  {
    path: '/tool-setting/:tid',
    name: 'ToolSetting',
    component: ToolSettingView,
    meta: { requiresAuth: true },
  },
  {
    path: '/tool-test/:tid',
    name: 'ToolTest',
    component: ToolTestView,
    meta: { requiresAuth: true },
  },
  {
    path: '/test',
    name: 'Test',
    component: TestView,
    meta: { requiresAuth: true },
  },
  {
    path: '/doc-add-text/:kid', //kid知识库id
    name: 'DocAddText',
    component: DocAddTextView,
    meta: { requiresAuth: true },
  },
  {
    path: '/test-doc', //kid知识库id
    name: 'TestDoc',
    component: TestDocView,
    meta: { requiresAuth: true },
  },
  {
    path: '/aios',
    name: 'AIOSIndex',
    component: AIOSIndexView,
    meta: { requiresAuth: false }, //一体机远程桌面地址，不需要验证登录
  },
  {
    path: '/filetemplate-list',
    name: 'FileTemplateList',
    component: FileTemplateListView,
    meta: { requiresAuth: true },
  },
  {
    path: '/filetemplate-add/:ftid?',
    name: 'FileTemplateAdd',
    component: FileTemplateAddView,
    meta: { requiresAuth: true },
  },
  {
    path: '/fema',
    name: 'FeiMaIndex',
    component: FeiMaIndexView,
  },
  // {
  //     path: '/home',
  //     name: 'home',
  //     component: import('@/views/Home/index.vue'),
  //     meta: { requiresAuth: true }
  // },
  {
    path: '/',
    component: AppLayout,
    children: [
      {
        path: '/index',
        // name: "Home",
        name: 'Index',
        component: () => import('@/views/Apps/index.vue'),
        // component: () => import('@/views/Home/index.vue'), // 注意这里的改变
        meta: { requiresAuth: true, title: 'INTELLIDO' },
      },
      {
        path: '/chat/:token?', // 通用app聊天页面
        name: 'Chat',
        component: () => import('@/views/Chat/ChatView.vue'), // 注意这里的改变
        meta: { requiresAuth: true, title: '应用-INTELLIDO' },
        props: route => ({ question: route.query.question }), // question：用户输入的内容
      },

      {
        path: '/apps',
        name: 'Apps',
        component: () => import('@/views/Apps/index.vue'),
        meta: { requiresAuth: true, title: 'Apps-INTELLIDO' },
      },
    ],
  },
  {
    path: '/app-chat/:appId?', // 通用app聊天页面
    name: 'AppChat',
    component: () => import('@/views/V3/Chat/index.vue'), // 注意这里的改变
    meta: { requiresAuth: true, title: '应用-INTELLIDO' },
  },
  // 添加文件预览页面作为顶级路由
  {
    path: '/file-preview',
    name: 'FilePreview',
    component: () => import('@/views/FilePreview/FilePreviewPage.vue'),
    meta: { requiresAuth: true, title: '文件预览-INTELLIDO' },
  },
  {
    path: '/appremodel',
    name: 'AppRemodel',
    component: () => import('@/views/v2/appReModel/index.vue'),
  },
  {
    path: '/admin',
    component: AdminLayout,
    children: [
      {
        path: 'home',
        name: 'AdminHome',
        component: () => import('@/views/Home/index.vue'),
        meta: {
          title: '首页',
          activeMenu: 'home',
        },
      },
      {
        path: 'knowledge',
        name: 'Knowledge',
        component: () => import('@/views/v2/knowledge/index.vue'),
        meta: {
          title: '知识库',
          activeMenu: 'knowledge',
        },
      },
      {
        path: 'knowledge/docfragments/:kbaseId/:docId',
        name: 'KnowledgeDocFragments',
        component: () => import('@/views/v2/knowledge/detail/Docfragments.vue'),
        meta: {
          title: '文档切片列表',
          activeMenu: 'knowledge',
        },
      },
      {
        path: 'knowledge/detail/:id',
        name: 'KnowledgeDetail',
        component: () => import('@/views/v2/knowledge/detail/Detail.vue'),
        meta: {
          title: '知识库详情',
          activeMenu: 'knowledge',
        },
        redirect: to => {
          return {
            name: 'KnowledgeDetailDocs',
            params: { id: to.params.id },
          };
        },
        children: [
          {
            path: 'docs',
            name: 'KnowledgeDetailDocs',
            component: () => import('@/views/v2/knowledge/detail/Docs.vue'),
            meta: {
              title: '文档列表',
              activeMenu: 'knowledge',
              activeSubMenu: 'doc_list',
            },
          },
          {
            path: 'docfragments/:docId',
            name: 'KnowledgeDetailDocFragments',
            component: () => import('@/views/v2/knowledge/detail/Docfragments.vue'),
            meta: {
              title: '文档切片列表',
              activeMenu: 'knowledge',
              activeSubMenu: 'doc_list',
            },
          },
          {
            path: 'test',
            name: 'KnowledgeDetailTest',
            component: () => import('@/views/v2/knowledge/detail/Test.vue'),
            meta: {
              title: '知识库测试',
              activeMenu: 'knowledge',
              activeSubMenu: 'test',
            },
          },
          {
            path: 'settings',
            name: 'KnowledgeDetailSettings',
            component: () => import('@/views/v2/knowledge/detail/Settings.vue'),
            meta: {
              title: '知识库设置',
              activeMenu: 'knowledge',
              activeSubMenu: 'settings',
            },
          },
        ],
      },
      {
        path: 'application',
        name: 'Application',
        component: () => import('@/views/v2/application/index.vue'),
        meta: {
          title: '应用',
          activeMenu: 'application',
        },
      },
      {
        path: 'application/detail/:qid',
        name: 'ApplicationDetail',
        component: () => import('@/views/v2/application/detail/Detail.vue'),
        meta: {
          title: '应用详情',
          activeMenu: 'application',
        },
        redirect: to => {
          return {
            name: 'ApplicationDetailSettings',
            params: { qid: to.params.qid },
          };
        },
        children: [
          {
            path: 'settings',
            name: 'ApplicationDetailSettings',
            component: () => import('@/views/v2/application/detail/Settings.vue'),
            meta: {
              title: '应用设置',
              activeMenu: 'application',
              activeSubMenu: 'settings',
              hideSubMenu: true,
            },
          },
        ],
      },
      {
        path: 'model',
        name: 'Model',
        component: () => import('@/views/v2/appReModel/index.vue'),
        meta: {
          title: '模型管理',
          activeMenu: 'model',
        },
      },
    ],
  },
  {
    path: '/setting',
    component: AdminLayoutV3,
    children: [
      {
        path: 'department',
        name: 'SettingDepartment',
        component: () => import('@/views/setting/department.vue'),
        meta: {
          title: '部门管理',
          activeMenu: 'department',
          requiresAuth: true,
        },
      },
      {
        path: 'user',
        name: 'SettingUser',
        component: () => import('@/views/setting/user.vue'),
        meta: {
          title: '用户管理',
          activeMenu: 'user',
          requiresAuth: true,
        },
      },
      {
        path: 'role',
        name: 'SettingRole',
        component: () => import('@/views/setting/role.vue'),
        meta: {
          title: '角色管理',
          activeMenu: 'role',
          requiresAuth: true,
        },
      },
      {
        path: 'model-provider',
        name: 'SettingModelProvider',
        component: () => import('@/views/setting/model-provider.vue'),
        meta: {
          title: '模型供应商',
          activeMenu: 'model_provider',
          requiresAuth: true,
        },
      },
      {
        path: 'data-source',
        name: 'SettingDataSource',
        component: () => import('@/views/setting/data-source.vue'),
        meta: {
          title: '数据来源',
          activeMenu: 'data_source',
          requiresAuth: true,
        },
      },
      {
        path: 'api-extension',
        name: 'SettingApiExtension',
        component: () => import('@/views/setting/api-extension.vue'),
        meta: {
          title: 'API扩展',
          activeMenu: 'api_extension',
          requiresAuth: true,
        },
      },
      {
        path: 'license',
        name: 'SettingLicense',
        component: () => import('@/views/setting/license.vue'),
        meta: {
          title: '授权管理',
          activeMenu: 'license',
          requiresAuth: true,
        },
      },
      {
        path: 'system-config',
        name: 'SystemConfig',
        component: () => import('@/views/setting/system-config.vue'),
        meta: {
          title: '设置',
          activeMenu: 'system-config',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('../views/privacy.vue'),
    meta: { title: '隐私政策-INTELLIDO', requiresAuth: false },
  },
  {
    path: '/favorite',
    name: 'Favorite',
    component: () => import('../views/v2/favorite.vue'),
    meta: { title: '收藏夹-INTELLIDO', requiresAuth: true },
  },
];

const router = createRouter({
  history: createWebHistory('/nq/'),
  routes,
});

// 判断如果没有登录，会重定向到登录页面
router.beforeEach((to, from, next) => {
  timerManager.clearAllTimers();
  // 优先使用 URL 中的 _t 参数，并进行解码
  const titleFromQuery = to.query._t ? decodeUrlAndBase64(to.query._t) : null;
  document.title = titleFromQuery || to.meta.title || 'INTELLIDO';
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const token = localStorage.getItem('console_token');
    if (!token) {
      // 存储用户访问的页面路径，方便登录成功后跳转回原页面
      localStorage.setItem('redirectAfterLogin', to.fullPath);
      next({ name: 'Login' });
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;
