import{_ as l}from"./<EMAIL>";import{d as p,c as e,o as t,b as s,t as a,J as m,K as _,a as x}from"./pnpm-pnpm-B4aX-tnA.js";const b={class:"w-[360px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs"},h={class:"flex justify-between items-center"},u={class:"flex items-start"},f=["src"],g={key:1,src:l,alt:"",class:"w-[20px] h-[20px] object-contain object-center mt-[2px]"},k={class:"font-zhongcu ml-1.5 text-15 leading-[24px] break-words"},y={class:"mt-[10px]"},w={class:"truncate w-[10em] text-[#A2A9B0] shrink-0 mr-[5px]"},j={key:0},E=p({__name:"TextDetailComponent",props:{data:{}},setup(v){return(o,C)=>{var n,i,c;return t(),e("div",b,[s("div",h,[s("div",u,[(n=o.data)!=null&&n.titleImgUrl?(t(),e("img",{key:0,src:(i=o.data)==null?void 0:i.titleImgUrl,alt:"",class:"w-[20px] h-[20px] object-contain object-center mt-[2px]"},null,8,f)):(t(),e("img",g)),s("span",k,a(o.data.title||"详细内容"),1)])]),s("div",y,[(t(!0),e(m,null,_((c=o.data)==null?void 0:c.items,(r,d)=>(t(),e("div",{key:d,class:"flex item-center py-[8px] border-0 border-b border-[#ECECF2] border-solid mt-[10px]"},[s("span",w,a(r.title),1),["singleLine","multiLine"].includes(r.itemType)?(t(),e("span",j,a(r.content),1)):x("",!0)]))),128))])])}}});export{E as default};
