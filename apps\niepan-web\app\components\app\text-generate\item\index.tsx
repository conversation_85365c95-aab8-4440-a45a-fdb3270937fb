'use client';
import type { FC } from 'react';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  RiReplay15Line,
  RiSparklingFill,
  RiSparklingLine,
  RiThumbDownLine,
  RiThumbUpLine,
} from '@remixicon/react';
import copy from 'copy-to-clipboard';
import { useParams } from 'next/navigation';
import { useBoolean } from 'ahooks';
import ResultTab from './result-tab';
import { Markdown } from '@/app/components/base/markdown';
import Loading from '@/app/components/base/loading';
import Toast from '@/app/components/base/toast';
import type { FeedbackType } from '@/app/components/base/chat/chat/type';
import { fetchMoreLikeThis, updateFeedback } from '@/service/share';
import { fetchTextGenerationMessage } from '@/service/debug';
import { useStore as useAppStore } from '@/app/components/app/store';
import WorkflowProcessItem from '@/app/components/base/chat/chat/answer/workflow-process';
import type { WorkflowProcess } from '@/app/components/base/chat/types';
import type { SiteInfo } from '@/models/share';
import { useChatContext } from '@/app/components/base/chat/chat/context';
import ActionButton, { ActionButtonState } from '@/app/components/base/action-button';
import NewAudioButton from '@/app/components/base/new-audio-button';
import cn from '@/utils/classnames';
import { CopyOne } from '@/app/components/base/icons/src/public/apps';
import type { SVGProps } from 'react';

const MAX_DEPTH = 3;

export type IGenerationItemProps = {
  isWorkflow?: boolean;
  workflowProcessData?: WorkflowProcess;
  className?: string;
  isError: boolean;
  onRetry: () => void;
  content: any;
  messageId?: string | null;
  conversationId?: string;
  isLoading?: boolean;
  isResponding?: boolean;
  isInWebApp?: boolean;
  moreLikeThis?: boolean;
  depth?: number;
  feedback?: FeedbackType;
  onFeedback?: (feedback: FeedbackType) => void;
  onSave?: (messageId: string) => void;
  isMobile?: boolean;
  isInstalledApp: boolean;
  installedAppId?: string;
  taskId?: string;
  controlClearMoreLikeThis?: number;
  supportFeedback?: boolean;
  isShowTextToSpeech?: boolean;
  hideProcessDetail?: boolean;
  siteInfo: SiteInfo | null;
  inSidePanel?: boolean;
};

export const copyIcon = (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.3335 2.33341C9.87598 2.33341 10.1472 2.33341 10.3698 2.39304C10.9737 2.55486 11.4454 3.02657 11.6072 3.63048C11.6668 3.85302 11.6668 4.12426 11.6668 4.66675V10.0334C11.6668 11.0135 11.6668 11.5036 11.4761 11.8779C11.3083 12.2072 11.0406 12.4749 10.7113 12.6427C10.337 12.8334 9.84692 12.8334 8.86683 12.8334H5.1335C4.1534 12.8334 3.66336 12.8334 3.28901 12.6427C2.95973 12.4749 2.69201 12.2072 2.52423 11.8779C2.3335 11.5036 2.3335 11.0135 2.3335 10.0334V4.66675C2.3335 4.12426 2.3335 3.85302 2.39313 3.63048C2.55494 3.02657 3.02665 2.55486 3.63056 2.39304C3.8531 2.33341 4.12435 2.33341 4.66683 2.33341M5.60016 3.50008H8.40016C8.72686 3.50008 8.89021 3.50008 9.01499 3.4365C9.12475 3.38058 9.21399 3.29134 9.26992 3.18158C9.3335 3.05679 9.3335 2.89345 9.3335 2.56675V2.10008C9.3335 1.77338 9.3335 1.61004 9.26992 1.48525C9.21399 1.37549 9.12475 1.28625 9.01499 1.23033C8.89021 1.16675 8.72686 1.16675 8.40016 1.16675H5.60016C5.27347 1.16675 5.11012 1.16675 4.98534 1.23033C4.87557 1.28625 4.78634 1.37549 4.73041 1.48525C4.66683 1.61004 4.66683 1.77338 4.66683 2.10008V2.56675C4.66683 2.89345 4.66683 3.05679 4.73041 3.18158C4.78634 3.29134 4.87557 3.38058 4.98534 3.4365C5.11012 3.50008 5.27347 3.50008 5.60016 3.50008Z"
      stroke="#344054"
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const CollectIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.99914 1C8.25298 0.99935 8.48517 1.14291 8.59802 1.37029L10.507 5.21658L14.7642 5.83734C15.0149 5.87391 15.2233 6.04949 15.3018 6.29044C15.3804 6.53138 15.3155 6.79602 15.1344 6.97333L12.0379 10.0059L12.7732 14.2187C12.817 14.4697 12.7139 14.7237 12.5076 14.8732C12.3012 15.0227 12.0277 15.0415 11.8029 14.9217L8.00089 12.8952L4.19974 14.9216C3.97476 15.0416 3.70107 15.0227 3.49469 14.873C3.2883 14.7233 3.18537 14.469 3.22953 14.2179L3.97013 10.0059L0.868609 6.97374C0.687257 6.79644 0.622213 6.5316 0.7008 6.29046C0.779386 6.04932 0.98799 5.87366 1.23898 5.83727L5.51931 5.21659L7.40217 1.37336C7.51385 1.14541 7.7453 1.00065 7.99914 1ZM8.00473 3.17407L6.56187 6.11917C6.465 6.31689 6.27676 6.45404 6.05886 6.48563L2.76517 6.96324L5.15367 9.29833C5.31102 9.45217 5.38233 9.67375 5.34422 9.89048L4.77961 13.1015L7.68723 11.5514C7.88324 11.447 8.11841 11.4469 8.31443 11.5514L11.2249 13.1027L10.6641 9.88966C10.6264 9.67337 10.6975 9.45236 10.8544 9.29874L13.2399 6.96251L9.9689 6.48556C9.75241 6.45399 9.56518 6.31821 9.46792 6.12224L8.00473 3.17407Z"
        fill="currentColor"
      />
    </svg>
  );
};

const HamburgerButtonIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.25 4.5C2.25 4.0875 2.5875 3.75 3 3.75H15C15.4125 3.75 15.75 4.0875 15.75 4.5C15.75 4.9125 15.4125 5.25 15 5.25H3C2.5875 5.25 2.25 4.9125 2.25 4.5ZM2.25 9C2.25 8.5875 2.5875 8.25 3 8.25H15C15.4125 8.25 15.75 8.5875 15.75 9C15.75 9.4125 15.4125 9.75 15 9.75H3C2.5875 9.75 2.25 9.4125 2.25 9ZM3 12.75C2.5875 12.75 2.25 13.0875 2.25 13.5C2.25 13.9125 2.5875 14.25 3 14.25H15C15.4125 14.25 15.75 13.9125 15.75 13.5C15.75 13.0875 15.4125 12.75 15 12.75H3Z"
        fill="currentColor"
      />
    </svg>
  );
};

const GenerationItem: FC<IGenerationItemProps> = ({
  isWorkflow,
  workflowProcessData,
  className,
  isError,
  onRetry,
  content,
  messageId,
  isLoading,
  isResponding,
  moreLikeThis,
  isInWebApp = false,
  feedback,
  onFeedback,
  onSave,
  depth = 1,
  isMobile,
  isInstalledApp,
  installedAppId,
  taskId,
  controlClearMoreLikeThis,
  supportFeedback,
  isShowTextToSpeech,
  hideProcessDetail,
  siteInfo,
  inSidePanel,
}) => {
  const { t } = useTranslation();
  const params = useParams();
  const isTop = depth === 1;
  const [completionRes, setCompletionRes] = useState('');
  const [childMessageId, setChildMessageId] = useState<string | null>(null);
  const [childFeedback, setChildFeedback] = useState<FeedbackType>({
    rating: null,
  });
  const { config } = useChatContext();

  const setCurrentLogItem = useAppStore(s => s.setCurrentLogItem);
  const setShowPromptLogModal = useAppStore(s => s.setShowPromptLogModal);

  const handleFeedback = async (childFeedback: FeedbackType) => {
    await updateFeedback(
      { url: `/messages/${childMessageId}/feedbacks`, body: { rating: childFeedback.rating } },
      isInstalledApp,
      installedAppId
    );
    setChildFeedback(childFeedback);
  };

  const [isQuerying, { setTrue: startQuerying, setFalse: stopQuerying }] = useBoolean(false);

  const childProps = {
    isInWebApp: true,
    content: completionRes,
    messageId: childMessageId,
    depth: depth + 1,
    moreLikeThis: true,
    onFeedback: handleFeedback,
    isLoading: isQuerying,
    feedback: childFeedback,
    onSave,
    isShowTextToSpeech,
    isMobile,
    isInstalledApp,
    installedAppId,
    controlClearMoreLikeThis,
    isWorkflow,
    siteInfo,
    taskId,
  };

  const handleMoreLikeThis = async () => {
    if (isQuerying || !messageId) {
      Toast.notify({ type: 'warning', message: t('appDebug.errorMessage.waitForResponse') });
      return;
    }
    startQuerying();
    const res: any = await fetchMoreLikeThis(messageId as string, isInstalledApp, installedAppId);
    setCompletionRes(res.answer);
    setChildFeedback({
      rating: null,
    });
    setChildMessageId(res.id);
    stopQuerying();
  };

  useEffect(() => {
    if (controlClearMoreLikeThis) {
      setChildMessageId(null);
      setCompletionRes('');
    }
  }, [controlClearMoreLikeThis]);

  // regeneration clear child
  useEffect(() => {
    if (isLoading) setChildMessageId(null);
  }, [isLoading]);

  const handleOpenLogModal = async () => {
    const data = await fetchTextGenerationMessage({
      appId: params.appId as string,
      messageId: messageId!,
    });
    const logItem = {
      ...data,
      log: [
        ...data.message,
        ...(data.message[data.message.length - 1].role !== 'assistant'
          ? [
              {
                role: 'assistant',
                text: data.answer,
                files:
                  data.message_files?.filter((file: any) => file.belongs_to === 'assistant') || [],
              },
            ]
          : []),
      ],
    };
    setCurrentLogItem(logItem);
    setShowPromptLogModal(true);
  };

  const [currentTab, setCurrentTab] = useState<string>('DETAIL');
  const showResultTabs = !!workflowProcessData?.resultText || !!workflowProcessData?.files?.length;
  const switchTab = async (tab: string) => {
    setCurrentTab(tab);
  };
  useEffect(() => {
    if (workflowProcessData?.resultText || !!workflowProcessData?.files?.length)
      switchTab('RESULT');
    else switchTab('DETAIL');
  }, [workflowProcessData?.files?.length, workflowProcessData?.resultText]);

  return (
    <>
      <div className={cn('relative mb-5', !isTop && 'mt-3', className)}>
        {isLoading && (
          <div
            className={cn(
              'flex h-10 items-center',
              !inSidePanel && 'rounded-2xl border-t border-divider-subtle bg-chat-bubble-bg p-4'
            )}
          >
            <Loading type="area" />
          </div>
        )}
        {!isLoading && (
          <>
            {/* result content */}
            <div
              className={cn(
                'relative',
                !inSidePanel && 'rounded-2xl border border-divider-subtle bg-chat-bubble-bg p-4'
              )}
            >
              {workflowProcessData && (
                <>
                  <div className={cn(showResultTabs && 'border-b border-divider-subtle')}>
                    {taskId && (
                      <div
                        className={cn(
                          'system-2xs-medium-uppercase mb-2 flex items-center text-text-accent-secondary',
                          isError && 'text-text-destructive'
                        )}
                      >
                        <HamburgerButtonIcon className="mr-1 h-[18px] w-[18px]" />
                        <span>{t('share.generation.execution')}</span>
                        <span className="px-1">·</span>
                        <span>{taskId}</span>
                      </div>
                    )}
                    {siteInfo && workflowProcessData && (
                      <WorkflowProcessItem
                        data={workflowProcessData}
                        expand={workflowProcessData.expand}
                        hideProcessDetail={hideProcessDetail}
                        hideInfo={hideProcessDetail}
                        readonly={!siteInfo.show_workflow_steps}
                      />
                    )}
                    {showResultTabs && (
                      <div className="flex items-center space-x-6 px-1">
                        <div
                          className={cn(
                            'system-sm-semibold-uppercase relative cursor-pointer border-b-2 border-transparent py-5 pt-[30px] text-text-tertiary',
                            currentTab === 'RESULT' &&
                              'text-text-accent after:absolute after:bottom-[-2px] after:left-1/2 after:h-[2px] after:w-6 after:-translate-x-1/2 after:bg-[#129BFE]'
                          )}
                          onClick={() => switchTab('RESULT')}
                        >
                          {t('runLog.result')}
                        </div>
                        <div
                          className={cn(
                            'system-sm-semibold-uppercase relative cursor-pointer border-b-2 border-transparent py-5 pt-[30px] text-text-tertiary',
                            currentTab === 'DETAIL' &&
                              'text-text-accent after:absolute after:bottom-[-2px] after:left-1/2 after:h-[2px] after:w-6 after:-translate-x-1/2 after:bg-[#129BFE]'
                          )}
                          onClick={() => switchTab('DETAIL')}
                        >
                          {t('runLog.detail')}
                        </div>
                      </div>
                    )}
                  </div>
                  {!isError && (
                    <ResultTab
                      data={workflowProcessData}
                      content={content}
                      currentTab={currentTab}
                    />
                  )}
                </>
              )}
              {!workflowProcessData && taskId && (
                <div
                  className={cn(
                    'system-2xs-medium-uppercase sticky left-0 top-0 flex w-full items-center py-[5px] text-sm text-text-accent-secondary',
                    isError && 'text-text-destructive'
                  )}
                >
                  <HamburgerButtonIcon className="mr-1 h-[18px] w-[18px]" />
                  <span>{t('share.generation.execution')}</span>
                  <span className="px-1">·</span>
                  <span>{`${taskId}${depth > 1 ? `-${depth - 1}` : ''}`}</span>
                </div>
              )}
              {isError && (
                <div className="body-lg-regular text-text-quaternary">
                  {t('share.generation.batchFailed.outputPlaceholder')}
                </div>
              )}
              {!workflowProcessData && !isError && typeof content === 'string' && (
                <div>
                  <Markdown content={content} />
                </div>
              )}
            </div>
            {/* meta data */}
            <div
              className={cn(
                'system-xs-regular relative mt-1 h-4 px-4 text-text-quaternary',
                isMobile && (childMessageId || isQuerying) && depth < 3 && 'pl-10'
              )}
            >
              {!isWorkflow && (
                <span>
                  {content?.length} {t('common.unit.char')}
                </span>
              )}
              {/* action buttons */}
              <div className="absolute -bottom-2 right-2 flex items-center">
                {!isInWebApp && !isInstalledApp && !isResponding && (
                  <div className="ml-1 flex items-center gap-0.5 rounded-[10px]  backdrop-blur-sm">
                    <ActionButton disabled={isError || !messageId} onClick={handleOpenLogModal}>
                      <CopyOne className="h-4 w-4" />
                    </ActionButton>
                  </div>
                )}
                <div className="ml-1 flex items-center gap-0.5 rounded-[10px]  backdrop-blur-sm">
                  {moreLikeThis && (
                    <ActionButton
                      state={
                        depth === MAX_DEPTH ? ActionButtonState.Disabled : ActionButtonState.Default
                      }
                      disabled={depth === MAX_DEPTH}
                      onClick={handleMoreLikeThis}
                    >
                      <RiSparklingLine className="h-4 w-4" />
                    </ActionButton>
                  )}
                  {isShowTextToSpeech && (
                    <NewAudioButton id={messageId!} voice={config?.text_to_speech?.voice} />
                  )}
                  {((currentTab === 'RESULT' && workflowProcessData?.resultText) ||
                    !isWorkflow) && (
                    <ActionButton
                      disabled={isError || !messageId}
                      onClick={() => {
                        const copyContent = isWorkflow ? workflowProcessData?.resultText : content;
                        if (typeof copyContent === 'string') copy(copyContent);
                        else copy(JSON.stringify(copyContent));
                        Toast.notify({
                          type: 'success',
                          message: t('common.actionMsg.copySuccessfully'),
                        });
                      }}
                    >
                      <CopyOne className="h-4 w-4" />
                    </ActionButton>
                  )}
                  {isInWebApp && isError && (
                    <ActionButton onClick={onRetry}>
                      <RiReplay15Line className="h-4 w-4" />
                    </ActionButton>
                  )}
                  {isInWebApp && !isWorkflow && (
                    <ActionButton
                      disabled={isError || !messageId}
                      onClick={() => {
                        onSave?.(messageId as string);
                      }}
                    >
                      <CollectIcon className="h-4 w-4" />
                    </ActionButton>
                  )}
                </div>
                {(supportFeedback || isInWebApp) && !isWorkflow && !isError && messageId && (
                  <div className="ml-1 flex items-center gap-0.5 rounded-[10px]  backdrop-blur-sm">
                    {!feedback?.rating && (
                      <>
                        <ActionButton onClick={() => onFeedback?.({ rating: 'like' })}>
                          <RiThumbUpLine className="h-4 w-4" />
                        </ActionButton>
                        <ActionButton onClick={() => onFeedback?.({ rating: 'dislike' })}>
                          <RiThumbDownLine className="h-4 w-4" />
                        </ActionButton>
                      </>
                    )}
                    {feedback?.rating === 'like' && (
                      <ActionButton
                        state={ActionButtonState.Active}
                        onClick={() => onFeedback?.({ rating: null })}
                      >
                        <RiThumbUpLine className="h-4 w-4" />
                      </ActionButton>
                    )}
                    {feedback?.rating === 'dislike' && (
                      <ActionButton
                        state={ActionButtonState.Destructive}
                        onClick={() => onFeedback?.({ rating: null })}
                      >
                        <RiThumbDownLine className="h-4 w-4" />
                      </ActionButton>
                    )}
                  </div>
                )}
              </div>
            </div>
            {/* more like this elements */}
            {!isTop && (
              <div
                className={cn(
                  'absolute top-[-32px] flex h-[33px] w-4 justify-center',
                  isMobile ? 'left-[17px]' : 'left-[50%] translate-x-[-50%]'
                )}
              >
                <div className="h-full w-0.5 bg-divider-regular"></div>
                <div
                  className={cn(
                    'absolute left-0 flex h-4 w-4 items-center justify-center rounded-2xl border-[0.5px] border-divider-subtle bg-util-colors-blue-blue-500 shadow-xs',
                    isMobile ? 'top-[3.5px]' : 'top-2'
                  )}
                >
                  <RiSparklingFill className="h-3 w-3 text-text-primary-on-surface" />
                </div>
              </div>
            )}
          </>
        )}
      </div>
      {(childMessageId || isQuerying) && depth < 3 && <GenerationItem {...(childProps as any)} />}
    </>
  );
};
export default React.memo(GenerationItem);
