import type { FC } from 'react';
import { useState } from 'react';

interface TextDetailProps {
  data: {
    title?: string;
    content: string;
    type?: 'info' | 'warning' | 'error' | 'success';
    collapsible?: boolean;
    defaultExpanded?: boolean;
  };
}

const TextDetailComponent: FC<TextDetailProps> = ({ data }) => {
  const [isExpanded, setIsExpanded] = useState(data.defaultExpanded ?? true);

  const getTypeStyles = () => {
    switch (data.type) {
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  const getIconForType = () => {
    switch (data.type) {
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      case 'success':
        return '✅';
      default:
        return 'ℹ️';
    }
  };

  return (
    <div className={`w-full border rounded-lg ${getTypeStyles()}`}>
      {/* 标题栏 */}
      {data.title && (
        <div 
          className={`px-4 py-3 border-b ${data.collapsible ? 'cursor-pointer hover:bg-opacity-80' : ''}`}
          onClick={data.collapsible ? () => setIsExpanded(!isExpanded) : undefined}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-lg">{getIconForType()}</span>
              <h3 className="text-lg font-medium text-gray-900">{data.title}</h3>
            </div>
            {data.collapsible && (
              <button className="text-gray-500 hover:text-gray-700">
                {isExpanded ? '▼' : '▶'}
              </button>
            )}
          </div>
        </div>
      )}

      {/* 内容区域 */}
      {(!data.collapsible || isExpanded) && (
        <div className="p-4">
          <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
            {data.content}
          </div>
        </div>
      )}
    </div>
  );
};

export default TextDetailComponent;
