'use client';
import type { FC } from 'react';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import ExploreContext from '@/context/explore-context';
import Sidebar from '@/app/components/explore/sidebar';
import { useAppContext } from '@/context/app-context';
import { fetchMembers } from '@/service/common';
import type { InstalledApp } from '@/models/explore';
import { fetchInstalledAppList as doFetchInstalledAppList } from '@/service/explore';

export type IExploreProps = {
  children: React.ReactNode;
};

const Explore: FC<IExploreProps> = ({ children }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [controlUpdateInstalledApps, setControlUpdateInstalledApps] = useState(0);
  const { userProfile, isCurrentWorkspaceDatasetOperator } = useAppContext();
  const [hasEditPermission, setHasEditPermission] = useState(false);
  const [installedApps, setInstalledApps] = useState<InstalledApp[]>([]);

  const fetchInstalledAppList = async () => {
    try {
      const { installed_apps }: any = await doFetchInstalledAppList();
      setInstalledApps(installed_apps || []);
    } catch (error) {
      console.error('获取已安装应用列表失败:', error);
    }
  };

  useEffect(() => {
    document.title = `${t('explore.title')} - INTELLIDO`;
    (async () => {
      const { accounts } = await fetchMembers({
        url: '/workspaces/current/members',
        params: {},
      });
      if (!accounts) return;
      const currUser = accounts.find(account => account.id === userProfile.id);
      setHasEditPermission(currUser?.role !== 'normal');
    })();

    // 初始加载已安装应用列表
    fetchInstalledAppList();
  }, []);

  useEffect(() => {
    // 当controlUpdateInstalledApps变化时更新列表
    if (controlUpdateInstalledApps > 0) fetchInstalledAppList();
  }, [controlUpdateInstalledApps]);

  useEffect(() => {
    if (isCurrentWorkspaceDatasetOperator) return router.replace('/datasets');
  }, [isCurrentWorkspaceDatasetOperator]);

  return (
    <div className="flex h-full overflow-hidden border-t border-divider-regular bg-background-body">
      <ExploreContext.Provider
        value={{
          controlUpdateInstalledApps,
          setControlUpdateInstalledApps,
          hasEditPermission,
          installedApps,
          setInstalledApps,
        }}
      >
        {installedApps.length > 0 && (
          <Sidebar controlUpdateInstalledApps={controlUpdateInstalledApps} />
        )}
        <div className="w-0 grow">{children}</div>
      </ExploreContext.Provider>
    </div>
  );
};
export default React.memo(Explore);
