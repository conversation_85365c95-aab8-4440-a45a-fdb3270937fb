const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/CenterProjectCollectionComponent-Dti08BlD.js","static/js/pnpm-pnpm-B4aX-tnA.js","static/css/pnpm-pnpm-BdGb95pV.css","static/js/index-C4ad4gme.js","static/css/index-CKcViCi5.css","static/js/CenterOpportunityListComponent-DpGqJBR3.js","static/css/CenterOpportunityListComponent-CGiSChd0.css","static/js/CenterProjectListComponent-3ADz8uHP.js","static/js/CenterBusinessAmountComponent-DygCq3R0.js","static/css/CenterBusinessAmountComponent-DfAB3T1P.css","static/js/RecommendSystemLinkComponent-CdOlREsg.js","static/js/quesheng-C5oWzANf.js","static/css/RecommendSystemLinkComponent-Ch-7pybL.css","static/js/ListComponent-CcpNjhG1.js","static/js/TotalSummaryComponent-CmDj5vpK.js","static/js/<EMAIL>","static/js/TextDetailComponent-DIge5GVL.js","static/js/WorkbenchTextDetailComponent-DxQOi6lL.js","static/js/PDFViewerComponent-mCQw6aWd.js","static/css/PDFViewerComponent-CIGfg6C1.css","static/js/FileListAndPagesComponent-BDeCCCD6.js","static/css/FileListAndPagesComponent-BDDxDDwF.css","static/js/NotFound-CesNKmI4.js"])))=>i.map(i=>d[i]);
import{aF as Xe,ap as yt,aG as xt,d as G,A as C,o as i,m as y,b as l,n as N,s as et,q as At,u as Lt,r as v,N as fe,B as Se,O as ve,z as bt,P as It,aH as Tt,R as ne,aC as Ve,e as me,c as k,a as S,t as De,J as _e,K as Rt,f as R,aa as D,_ as He,ar as he,M as Et,w as Bt,Q as Mt,E,x as St,y as Vt,p as Dt}from"./pnpm-pnpm-B4aX-tnA.js";import{c as M,_ as W,a as Ht,d as Ut,e as zt,g as Pt,h as Kt,s as tt,S as ot,i as Qt,k as Ot,l as rt,m as Nt,n as jt,o as Ft,q as Jt,r as it,t as qt}from"./index-C4ad4gme.js";const Gt="/nq/static/gif/loading-Bytg4oyB.gif",Wt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIBSURBVHgB7ZjrUQIxEMd3gwyjjsrgCx8fYgdagdoBVoAdYAdACVYgdkAJlmAH5gt6jo+50fE9ZE0Yc3DiY+EO8UN+X0hyCflfdpPbLIAnGThIZyll/u7pZR8I85AySKhmprNNpVQYa+f+QWFptQpANRgtKpMVe1et1qlrYAmcX1zbJ9RH8Deo2ancllvJCdYQoctAPXUEBWlDID9K8v7hrWR+G7bCEkgdn3MKsX57eV6DlCksrZxZcZ35kKRrF7zhlPqm4MIUOD76TLxQXN8haleMQTejxq5/WCqF5ZUyDIHZkQq0OL65ajW4Y2IC7VGidbv28xBjboKhTE7Wx1DvGH+rmJ26+/nM+4rIxMViUf7BOefYvHt8PeB0jFbwlbAUe4JwaLbTr284EEhl5y6ItM0Z0jVx7CgBdXt5wXrDQTAuZCeqdqaL+/W3/Ptd7AUmxQtMiheYFC8wKV5gUrzApPBudaMhb6Kbmi0IpDndG90gRWHeWAW60EtT/IFGarryPzQx1cMgUK42zhWMJQBMAHuaEXR4HQQnvV3GKdBG7Ru/dfLHzLcgsC5kkYltfs7kRFxVmsv5GaQNdXMu5hI/mMD2W7Ypci/VKIvAvHUNDdIxp1tk4jBUodZibySptT6ofhMEDU7PvgRm3qR5M8/PJdJCQtqYL4SezDVCRsrDkxbv5DqnDwYw+OgAAAAASUVORK5CYII=",$t="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANjSURBVHgB7VhNVhpBEK5qBPzJzwQeCGbh5ATBXZZwAzyBeAJ1l516guQGIScQVlkGTyA5gZNFEiLP2AsjgtqV6hEfDGF+GNB5yfN7zzctXV39dXV1V1UDPOI/B8IUMBiQSBQFxlaBsODsVZYScAiXl03JgJAIRTCdzZd55BYRFIPIE0JDKKqetlsfYUJMRNBIZ8siFnvHTRPCwUKivVGiRi5nCiWOeClS0fW6bLebd32xIFr1Vi49f/EJUbzV//4tQTwxaKUNQrQEUQtQaLn5UVWAWF5YemYmny59uTw/t7d+cfGJXvQb3Y+I853f5/W7AXPgR85eHX7m7TQdlJgQCqiqLtSkbH0dO9bImRCHIk+6iw6rU4V1Fll3SbZaltf8c0HIgUM58jaobRnAn5i4xZ+q/jMyucoIUVu3JgnKXYfwmkAo2BgmR0QN1bt4JUM4O4+pUo9KBFgb+vl2dwANVw7gAUWxurZYn131rN0qTXNlaIuenXxfZ/cYXqDJVi1DGIKy/a2pempNkVj71W5twoxw1v5R4UPVCCLre0j6fjRz3Fwl1zHRPUKfK8vTgvcJKS3JPr3vJxcZQQ374AA2vWQiJaiBSFWv/sgJKqS6V3/kBPuRxHLrd5xiHXNFfGFXt9VVZ3+aO28SIII1GkphHEGbHML2bTupPzvwAODTbLklVs4tRhqEHHQPPzMH4WCniBy75npRu5n8PqCukvsi3um3u4670ZUgZx4mPBD0pQ0u7uR1ik0jn1+FiOGdbl2rMkQM73sQRQUiht9FXUhnchsQIXwjCRdBe3b9GxGChDoT4wsHEBECxWIORcVUZuUDzAip7Mrei2z+yOCvn6wrQQKo0XAQR6qw0gNd6UFIaFdJL+e5SiRd3RUEf/3cx5UgcpV/W4UNSOriRldhYQ4Oj6mIxOLx8HOJ1u2XkHgXTVyPjJJkmHxwqqls/lgT9bKo7kst57e0LI9hFxnEeq1T6wYfBCqa+IWghHHgTAcro0S5dtY+JQmpKQgkp/C6bdrFkBofz2336XU2g6RzjhzHyLwsIKoD5EJal5ujFd2Y14GJYFvt5mZHnp7Ugo4J9fymifKr0wZbsBhEXr9IkKL38vRnHSbE1A+Yc3Pzr/mhsgij6ZnApgK0oHtx+FCZ+SP+SfwBvgpvDgQ/rOEAAAAASUVORK5CYII=";function _a(h,u=!1){let g=parseFloat(`${h}`);const d=["","万","亿","万亿"];let c=0,m="";for(;g>=1e4&&c<d.length-1;)g/=1e4,c++;return g=parseFloat(g.toFixed(1)),g<1?{value:0,unit:""}:(g%1===0&&(g=parseInt(g.toString())),m=`${d[c]}${u?"元":""}`,{value:g,unit:m})}function Yt(){const h=new Date,u=h.getFullYear(),g=h.getMonth()+1,d=h.getDate(),c=h.getHours(),m=h.getMinutes();return[u,g.toString().padStart(2,"0"),d.toString().padStart(2,"0")].join("-")+" "+[c.toString().padStart(2,"0"),m.toString().padStart(2,"0")].join(":")}const Zt={"project-collection-component":()=>M(()=>import("./CenterProjectCollectionComponent-Dti08BlD.js"),__vite__mapDeps([0,1,2,3,4])),"opportunity-list-component":()=>M(()=>import("./CenterOpportunityListComponent-DpGqJBR3.js"),__vite__mapDeps([5,1,2,3,4,6])),"project-list-component":()=>M(()=>import("./CenterProjectListComponent-3ADz8uHP.js"),__vite__mapDeps([7,1,2,3,4])),"business-amount-component":()=>M(()=>import("./CenterBusinessAmountComponent-DygCq3R0.js"),__vite__mapDeps([8,1,2,3,4,9])),"recommend-system-link-component":()=>M(()=>import("./RecommendSystemLinkComponent-CdOlREsg.js"),__vite__mapDeps([10,11,1,2,3,4,12])),"list-component":()=>M(()=>import("./ListComponent-CcpNjhG1.js"),__vite__mapDeps([13,1,2])),"total-summary-component":()=>M(()=>import("./TotalSummaryComponent-CmDj5vpK.js"),__vite__mapDeps([14,15,1,2])),"text-detail-component":()=>M(()=>import("./TextDetailComponent-DIge5GVL.js"),__vite__mapDeps([16,15,1,2])),"workbench-text-detail-component":()=>M(()=>import("./WorkbenchTextDetailComponent-DxQOi6lL.js"),__vite__mapDeps([17,1,2])),"pdf-viewer-component":()=>M(()=>import("./PDFViewerComponent-mCQw6aWd.js"),__vite__mapDeps([18,1,2,3,4,19])),"file-list-component":()=>M(()=>import("./FileListAndPagesComponent-BDeCCCD6.js"),__vite__mapDeps([20,1,2,3,4,21]))},at={template:`
    <div class="flex items-center justify-center h-full">
      <div class="text-gray-500">加载中...</div>
    </div>
  `},nt={template:`
    <div class="flex items-center justify-center h-full">
      <div class="text-red-500">加载失败</div>
    </div>
  `};function ct(h){const u=Zt[h];return u?Xe({loader:u,loadingComponent:at,errorComponent:nt,delay:100,timeout:1e4,suspensible:!1,onError(g,d,c,m){m<=3?d():(console.error("Component load failed:",g),c())}}):Xe({loader:()=>M(()=>import("./NotFound-CesNKmI4.js"),__vite__mapDeps([22,1,2,3,4])),loadingComponent:at,errorComponent:nt})}function Xt(){const h=d=>`${d}-${Date.now()}`,u=(d,c)=>({component:yt(ct(d)),data:c,key:h(d)});return{updatePreviewComponents:d=>(Array.isArray(d)||(d=[d]),d.map(c=>{if(!(c!=null&&c.uid)||!(c!=null&&c.area))return c;const m=u(c.uid,c.data);return{...c,componentKey:m.key,component:m.component,data:m.data}}))}}const q={TRIGGER_PREVIEW:"trigger_preview",TRIGGER_WORKBENCH:"trigger_workbench"},ge=xt();function st(){return{triggerPreview:c=>{ge.emit(q.TRIGGER_PREVIEW,c)},triggerWorkbench:(c,m)=>{ge.emit(q.TRIGGER_WORKBENCH,{uid:c,data:m})},onMessage:(c,m)=>{ge.on(c,m)},offMessage:(c,m)=>{ge.off(c,m)}}}const eo=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},[l("path",{d:"M2.63682 7.54976L2.63716 7.54958L5.41216 6.03708L5.41236 6.03697C6.20941 5.60142 6.8588 4.95643 7.29879 4.16357L8.407 2.17212L8.40722 2.17174C8.64951 1.73417 9.11286 1.46248 9.61246 1.46248H9.71246C10.3368 1.46248 10.9212 1.70331 11.361 2.14313C11.814 2.59618 12.0962 3.19999 12.1464 3.83718L12.1465 3.83781L12.184 4.29614L12.184 4.29676C12.2614 5.20842 12.1963 6.11994 11.9887 7.01544L11.9885 7.01606L11.8885 7.45356L11.8303 7.70832H12.0916H16.4C16.8598 7.70832 17.2855 7.91948 17.5627 8.28775C17.8403 8.65669 17.9277 9.11877 17.8035 9.56464C17.8035 9.56472 17.8035 9.5648 17.8034 9.56489L15.5661 17.4808L15.566 17.4812C15.3914 18.1033 14.8113 18.5417 14.1625 18.5417H3.33329C2.52752 18.5417 1.87496 17.8891 1.87496 17.0833V8.82915C1.87496 8.29324 2.16631 7.80508 2.63682 7.54976ZM9.79257 2.71935L9.6678 2.46981L9.47609 2.81564L8.39291 4.76952C7.83697 5.7686 7.01547 6.58598 6.00874 7.13364L6.00859 7.13372L3.23359 8.64622L3.12496 8.70543V8.82915V17.0833V17.2917H3.33329H14.1625H14.3201L14.3629 17.14L16.6004 9.22331L16.6753 8.95832H16.4H11.6166C11.285 8.95832 10.978 8.80866 10.7712 8.54927C10.5647 8.29029 10.4886 7.95588 10.5651 7.63594L10.5654 7.63447L10.7738 6.73447L10.7739 6.734C10.9495 5.96734 11.0052 5.18342 10.9409 4.40383C10.9409 4.40379 10.9409 4.40375 10.9409 4.4037L10.9034 3.94549L10.9034 3.94494C10.8743 3.60052 10.724 3.27526 10.4806 3.03184C10.2965 2.84775 10.0494 2.73759 9.79257 2.71935Z",fill:"#121619",stroke:"#121619","stroke-width":"0.416667"})],-1),to={name:"LikeIcon"},oo=G({...to,props:{color:{default:"white"},size:{default:20},className:{default:""}},setup(h){return(u,g)=>(i(),C(W,{color:u.color,size:u.size,class:N(u.className)},{default:y(()=>[eo]),_:1},8,["color","size","class"]))}}),ao=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none"},[l("path",{d:"M1.63682 11.4544L1.63716 11.4546L4.41216 12.9671L4.41236 12.9672C5.20941 13.4027 5.8588 14.0477 6.29879 14.8406L7.407 16.832L7.40722 16.8324C7.64951 17.27 8.11286 17.5417 8.61246 17.5417H8.71246C9.33677 17.5417 9.92116 17.3008 10.361 16.861C10.814 16.408 11.0962 15.8042 11.1464 15.167L11.1465 15.1663L11.184 14.708L11.184 14.7074C11.2614 13.7957 11.1963 12.8842 10.9887 11.9887L10.9885 11.9881L10.8885 11.5506L10.8303 11.2958H11.0916H15.4C15.8598 11.2958 16.2855 11.0847 16.5627 10.7164C16.8403 10.3475 16.9277 9.88538 16.8035 9.43951C16.8035 9.43943 16.8035 9.43935 16.8034 9.43926L14.5661 1.52333L14.566 1.52297C14.3914 0.900888 13.8113 0.4625 13.1625 0.4625H2.33329C1.52752 0.4625 0.874959 1.11506 0.874959 1.92083V10.175C0.874959 10.7109 1.16631 11.1991 1.63682 11.4544ZM8.79257 16.2848L8.6678 16.5343L8.47609 16.1885L7.39291 14.2346C6.83697 13.2356 6.01547 12.4182 5.00874 11.8705L5.00859 11.8704L2.23359 10.3579L2.12496 10.2987V10.175V1.92083V1.7125H2.33329H13.1625H13.3201L13.3629 1.86417L15.6004 9.78084L15.6753 10.0458H15.4H10.6166C10.285 10.0458 9.97799 10.1955 9.77119 10.4549C9.5647 10.7139 9.48857 11.0483 9.56509 11.3682L9.56543 11.3697L9.77376 12.2697L9.77387 12.2702C9.94947 13.0368 10.0052 13.8207 9.94093 14.6003C9.94093 14.6004 9.94093 14.6004 9.94092 14.6004L9.90343 15.0587L9.90339 15.0592C9.87428 15.4036 9.72402 15.7289 9.48061 15.9723C9.29653 16.1564 9.04937 16.2666 8.79257 16.2848Z",fill:"#121619",stroke:"#121619","stroke-width":"0.416667"})],-1),no={name:"NoLikeIcon"},so=G({...no,props:{color:{default:"white"},size:{default:20},className:{default:""}},setup(h){return(u,g)=>(i(),C(W,{color:u.color,size:u.size,class:N(u.className)},{default:y(()=>[ao]),_:1},8,["color","size","class"]))}}),lo=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},[l("path",{d:"M11.8885 7.45356L11.8303 7.70832H12.0916H16.4C16.8598 7.70832 17.2855 7.91948 17.5627 8.28775C17.8403 8.65669 17.9277 9.11877 17.8035 9.56464C17.8035 9.56472 17.8035 9.5648 17.8034 9.56489L15.5664 17.4801C15.5663 17.4801 15.5663 17.4802 15.5663 17.4802C15.3875 18.104 14.8104 18.5417 14.1625 18.5417H3.33329C2.52752 18.5417 1.87496 17.8891 1.87496 17.0833V8.82915C1.87496 8.29711 2.16657 7.80494 2.63682 7.54976L2.63716 7.54958L5.41216 6.03708L5.41278 6.03674C6.20497 5.60147 6.85859 4.9568 7.29879 4.16357L8.407 2.17212L8.40722 2.17174C8.64951 1.73417 9.11286 1.46248 9.61246 1.46248H9.71246C10.3368 1.46248 10.9212 1.70331 11.361 2.14313C11.8133 2.59546 12.0922 3.19923 12.1465 3.838C12.1465 3.83815 12.1465 3.83829 12.1465 3.83843L12.184 4.29614L12.184 4.29676C12.2614 5.20851 12.1962 6.12412 11.9887 7.01523L11.9885 7.01606L11.8885 7.45356Z",fill:"#A2A9B0",stroke:"#A2A9B0","stroke-width":"0.416667"})],-1),ro={name:"LikeActiveIcon"},io=G({...ro,props:{color:{default:"white"},size:{default:20},className:{default:""}},setup(h){return(u,g)=>(i(),C(W,{color:u.color,size:u.size,class:N(u.className)},{default:y(()=>[lo]),_:1},8,["color","size","class"]))}}),co=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none"},[l("path",{d:"M11.8885 12.5506L11.8303 12.2958H12.0916H16.4C16.8598 12.2958 17.2855 12.0847 17.5627 11.7164C17.8403 11.3475 17.9277 10.8854 17.8035 10.4395C17.8035 10.4394 17.8035 10.4393 17.8034 10.4393L15.5664 2.52405C15.5663 2.524 15.5663 2.52395 15.5663 2.5239C15.3875 1.90016 14.8104 1.4625 14.1625 1.4625H3.33329C2.52752 1.4625 1.87496 2.11506 1.87496 2.92083V11.175C1.87496 11.707 2.16657 12.1992 2.63682 12.4544L2.63716 12.4546L5.41216 13.9671L5.41278 13.9674C6.20497 14.4027 6.85859 15.0474 7.29879 15.8406L8.407 17.832L8.40722 17.8324C8.64951 18.27 9.11286 18.5417 9.61246 18.5417H9.71246C10.3368 18.5417 10.9212 18.3008 11.361 17.861C11.8133 17.4087 12.0922 16.8049 12.1465 16.1661C12.1465 16.166 12.1465 16.1659 12.1465 16.1657L12.184 15.708L12.184 15.7074C12.2614 14.7956 12.1962 13.88 11.9887 12.9889L11.9885 12.9881L11.8885 12.5506Z",fill:"#A2A9B0",stroke:"#A2A9B0","stroke-width":"0.416667"})],-1),uo={name:"NoLikeActiveIcon"},po=G({...uo,props:{color:{default:"white"},size:{default:20},className:{default:""}},setup(h){return(u,g)=>(i(),C(W,{color:u.color,size:u.size,class:N(u.className)},{default:y(()=>[co]),_:1},8,["color","size","class"]))}}),fo=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},[l("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.99795 1.25C10.3153 1.24919 10.6055 1.42864 10.7465 1.71286L13.1327 6.52073L18.4542 7.29668C18.7677 7.34239 19.0282 7.56186 19.1263 7.86304C19.2245 8.16422 19.1434 8.49502 18.9171 8.71666L15.0464 12.5074L15.9655 17.7734C16.0203 18.0871 15.8914 18.4047 15.6335 18.5915C15.3756 18.7784 15.0337 18.8019 14.7526 18.6521L10.0001 16.119L5.2487 18.652C4.96747 18.802 4.62536 18.7784 4.36738 18.5912C4.1094 18.4041 3.98074 18.0862 4.03593 17.7724L4.96169 12.5074L1.08479 8.71717C0.858095 8.49555 0.77679 8.1645 0.875023 7.86308C0.973256 7.56165 1.23401 7.34208 1.54775 7.29658L6.89816 6.52074L9.25174 1.7167C9.39134 1.43176 9.68065 1.25082 9.99795 1.25ZM10.0049 3.96758L8.20136 7.64896C8.08027 7.89612 7.84497 8.06755 7.5726 8.10704L3.45549 8.70405L6.44111 11.6229C6.6378 11.8152 6.72693 12.0922 6.67929 12.3631L5.97353 16.3769L9.60806 14.4393C9.85307 14.3087 10.147 14.3087 10.3921 14.4393L14.0302 16.3784L13.3292 12.3621C13.282 12.0917 13.3709 11.8154 13.567 11.6234L16.5489 8.70314L12.4601 8.10695C12.1895 8.06749 11.9555 7.89777 11.8339 7.6528L10.0049 3.96758Z",fill:"#A2A9B0"})],-1),vo={name:"CollectIcon"},lt=G({...vo,props:{color:{default:"#A2A9B0"},size:{default:20},className:{default:""}},setup(h){return(u,g)=>(i(),C(W,{color:u.color,size:u.size,class:N(u.className)},{default:y(()=>[fo]),_:1},8,["color","size","class"]))}}),mo=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},[l("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10.7466 1.7125C10.6049 1.42917 10.3174 1.25 9.99661 1.25C9.67995 1.25 9.38828 1.43333 9.25078 1.71667L6.89661 6.52083L1.54661 7.29583C1.23411 7.34167 0.971614 7.5625 0.875781 7.8625C0.775781 8.1625 0.859114 8.49583 1.08411 8.71667L4.95911 12.5083L4.03411 17.775C3.97995 18.0875 4.10911 18.4083 4.36745 18.5958C4.62578 18.7833 4.96745 18.8083 5.25078 18.6583L10.0008 16.125L14.7549 18.6583C15.0341 18.8083 15.3758 18.7833 15.6341 18.5958C15.8924 18.4083 16.0216 18.0917 15.9674 17.7792L15.0466 12.5125L18.9174 8.72083C19.1424 8.5 19.2258 8.16667 19.1258 7.86667C19.0258 7.56667 18.7674 7.34583 18.4549 7.3L13.1341 6.525L10.7466 1.7125Z",fill:"#FFD447"})],-1),_o={name:"CollectActiveIcon"},ho=G({..._o,props:{color:{default:"#FFD447"},size:{default:20},className:{default:""}},setup(h){return(u,g)=>(i(),C(W,{color:u.color,size:u.size,class:N(u.className)},{default:y(()=>[mo]),_:1},8,["color","size","class"]))}}),go=[],Co=h=>{const u=h.toLowerCase(),d=go.map(c=>{let m=0;return c.question===h&&(m+=100),c.keywords.forEach(I=>{u.includes(I.toLowerCase())&&(m+=1)}),{answer:c.answer,score:m}}).reduce((c,m)=>m.score>c.score?m:c,{answer:void 0,score:0});return d.score>0?d.answer:void 0},H=h=>(St("data-v-0a1f0475"),h=h(),Vt(),h),ko={class:"flex"},wo={class:"container container-message"},yo={class:"container-center"},xo={class:"conatiner-message-div flex w-full h-full flex-col justify-between"},Ao={key:0,class:"opening-statement-div"},Lo={key:1,class:"mt-[65px]"},bo={key:0,class:"border-0 border-b border-[#ECECF2] border-solid pb-[20px]"},Io={class:"flex items-center"},To=["innerHTML"],Ro={key:0,class:"text-base text-[#909090] mt-[11px]"},Eo={key:1,class:"mb-[50px]"},Bo=H(()=>l("div",{class:"flex items-center text-lg font-zhongcu mt-[20px] mb-[10px]"},[l("img",{src:rt,alt:"",class:"w-[34px] h-[34px] mr-[13px]"}),Dt("回答 ")],-1)),Mo={class:"text-lg"},So=["innerHTML"],Vo={key:2,class:"mt-[0px]"},Do={key:0,src:Gt,alt:"",class:"h-[8px]"},Ho={key:1,class:"border-0 border-b border-[#ECECF2] border-solid pb-[15px] mt-[25px] flex justify-end items-center"},Uo=["onClick"],zo=H(()=>l("img",{src:Wt,alt:"",class:"w-[20px] h-[20px] object-contain object-center"},null,-1)),Po=[zo],Ko=["onClick"],Qo=H(()=>l("img",{src:$t,alt:"",class:"w-[20px] h-[20px] object-contain object-center"},null,-1)),Oo=[Qo],No=H(()=>l("div",{class:"w-[1px] h-[20px] bg-[#121619] ml-5"},null,-1)),jo=["onClick"],Fo=["onClick"],Jo=["onClick"],qo=["onClick"],Go={key:2,class:"mt-[15px] flex items-center flex-wrap"},Wo={class:"flex flex-1 items-center"},$o={class:"flex items-end"},Yo={class:"h-[52px] flex items-center"},Zo=["disabled"],Xo=H(()=>l("img",{src:Jt,alt:""},null,-1)),ea=[Xo],ta=H(()=>l("div",{class:"conatiner-tips-div font14"}," 内容由AI生成，无法确保真实准确，仅供参考，请阅读并遵守《AiALIGN用户协议》 ",-1)),oa=H(()=>l("img",{src:it,alt:"",class:"w-[18px]"},null,-1)),aa=[oa],na={class:"el-main-right-top flex justify-between items-center"},sa=H(()=>l("div",{class:"flex items-center"},[l("img",{src:rt,alt:"",class:"w-[30px] object-contain object-center mr-[13px]"}),l("span",{class:"text-26 font-semibold"},"超级工作台")],-1)),la=H(()=>l("img",{src:it,alt:"",class:"w-[18px]"},null,-1)),ra=[la],ia={class:"el-main-right-bottom mt-[20px]"},ca={key:1,class:"w-full flex justify-center mt-[188px]"},ua=H(()=>l("div",{class:"w-[144px] h-[110px] flex flex-col items-center justify-center"},[l("img",{src:qt,class:"w-[85px] h-[85px]"}),l("div",{class:"font18 font-zhongcu",style:{color:"#666666",width:"10em"}}," 本轮会话暂无内容输出 ")],-1)),da=[ua],pa={__name:"ChatView",setup(h){const u=Ut(),g=et(()=>u.userInfo),d=At();Lt();const c=v(!1),m=v(!1),I=v(""),B=v(!0),r=v([]),K=v(null),ut=v(null),Ue=v("");let Ce=0,dt=null;const ze=v("");let ke=0,pt=null;const $=v(d.query.app_id||null),Pe=v(null),F=v(d.query.topic_id||null),Ke=v(1),x=v(!1),Y=v(!1),Qe=v(""),V=v(!0),J=v(""),ft=v(),L=v(!1),Q=v(!1),Z=v(null),X=v(!0),O=v(""),we=d.query.question,se=v("");if(we)try{se.value=decodeURIComponent(we)}catch(e){f.error("URL解码失败:",e),se.value=we}d.query.app_id&&($.value=d.query.app_id);const Oe=e=>{document.title=e},{processText:le,getThinkingContent:re,hasThinkingContent:ie,resetThinking:ye,isThinkingCompleted:ce}=zt();fe(r,async()=>{for(let e=r.value.length-1;e>=0;e--){const a=r.value[e].attachments_data;if(a&&a.length>0){ft.value=a[a.length-1],a[a.length-1].file_url.endsWith(".docx")&&(L.value=!0);break}}await ne(),document.querySelectorAll("pre code").forEach(e=>{e.dataset.highlighted||(ve.highlightElement(e),e.dataset.highlighted="yes")})});const Ne=async()=>{let e=`Hello，${g.value.nickname}`;Ce<e.length?(Ue.value+=e[Ce],Ce++,setTimeout(Ne,100)):(je(),clearInterval(dt))},je=()=>{let e="";Pe.value?e=Pe.value:e="很高兴见到你，开始探索吧！",ke<e.length?(ze.value+=e[ke],ke++,setTimeout(je,100)):clearInterval(pt)};Se.setOptions({highlight:function(e,a){return ve.getLanguage(a)?ve.highlight(e,{language:a}).value:ve.highlightAuto(e).value}});const Fe=e=>typeof e=="object"?Se(JSON.stringify(e,null,2)):Se(String(e)),f={enabled:!1,log(...e){this.enabled&&console.log("[Stream]",...e)},error(...e){this.enabled&&console.error("[Stream]",...e)}},vt=e=>{O.value+=e;const a=t=>{try{return JSON.parse(t)}catch{return null}};if(O.value.includes("}{")){const t=O.value.split("}{"),s=[];for(let o=0;o<t.length;o++){let p=t[o];o===0?p=p+"}":o===t.length-1?p="{"+p:p="{"+p+"}";const n=a(p);n?(s.push(n),o===t.length-1&&(O.value="")):(f.log("JSON解析失败，保留在缓存中:",p),o===t.length-1&&(O.value=p))}return s.length>0?s:[]}else{const t=a(O.value);return t?(O.value="",[t]):(f.log("JSON解析失败，保留在缓存中:",O.value),[])}},Je=e=>{try{f.log("原始数据:",e);const a=vt(e);f.log("JSON解析后的数据:",a);const t=a.map(s=>{try{const o=s;if(o.error)return f.log("发现错误数据:",o.error),{topic_id:null,content:[{content:""}],taskmessage_id:"",error:o.error};if(o.topic_id){f.log("发现topic数据:",o);const p=o.topic_id;F.value=p;const n=o.data||"",w=o.taskmessage_id||o.message_id;return{topic_id:p,content:[{content:n}],taskmessage_id:w}}else return o.uid==="file-list-component"&&f.log("发现文件列表数据:",o),o.content=o.data,o}catch(o){return f.error("解析单条消息失败:",o),null}}).filter(Boolean);if(f.log("最终解析结果:",{数据条数:t.length,是否包含错误:t.length===1&&t[0].error,是否包含Topic:t.length===1&&typeof t[0].topic_id=="number"}),t.length===1&&t[0].error)return t[0];if(t.length===1&&typeof t[0].topic_id=="number")return t[0];{const s=t.find(o=>o.taskmessage_id&&o.topic_id);return s?{topic_id:s.topic_id,taskmessage_id:s.taskmessage_id,content:t}:{topic_id:null,content:t}}}catch(a){return f.error("解析流数据失败:",a),{topic_id:null,content:[{content:""}],taskmessage_id:""}}},mt=e=>{if(e.key==="Enter"){if(I.value.trim()===""){e.preventDefault();return}e.shiftKey||e.ctrlKey?(I.value+=`
`,e.preventDefault()):xe()}},ue=()=>{j.value=null,L.value=!1},xe=async()=>{X.value=!0;const e=I.value,a={role:"user",content:e,taskmessage_id:"",create_time:Yt(),favorite:null};r.value.push(a),J.value="",ye();let t={role:"assistant",content:"",taskmessage_id:"",thinkingContent:"",thinkingCompleted:!1,message_comment:{}};r.value.push(t),b(),Y.value=!1,I.value="",x.value=!0,V.value=!1,B.value=!1,Q.value=!0,L.value=!1,ue(),m.value=!1;try{const s=Co(e);if(s){await new Promise(p=>setTimeout(p,2e3));const o=`mock-${Date.now()}`;if(ye(o),Array.isArray(s))s.forEach(p=>{if(p.uid==="default"){const n=le(p.data||"",o);t.content+=n.normalText,ie(o)&&(t.thinkingContent=re(o),t.thinkingCompleted=ce(o))}else p.topic_id||te(p)});else{const p=le(s,o);t.content=p.normalText,ie(o)&&(t.thinkingContent=re(o),t.thinkingCompleted=ce(o))}t.taskmessage_id=o,a.taskmessage_id=o,r.value=[...r.value],x.value=!1,B.value=!0,V.value=!0,Q.value=!1,b();return}else{const o=await tt(e,$.value,F.value,$.value?"":"default");if(Ke.value==1)if(o&&o.getReader){const p=o.getReader(),n=new TextDecoder;let w=!1;(async()=>{for(;!w;){const{value:U,done:Re}=await p.read();if(w=Re,U){const Ee=n.decode(U,{stream:!0});f.log("[Stream] 收到新的数据块");let Be="";const{content:z,taskmessage_id:T,error:oe}=Je(Ee);if(T&&(Be=T),f.log("[Stream] 解析后的数据结构:",{content:z,hasFileList:z.some&&z.some(_=>_.uid==="file-list-component"),taskmessage_id:T,error:oe}),oe=="1"){E.error("应用不存在"),x.value=!1;return}let ae="";z.map(_=>{if(f.log("[Stream] 处理单条数据:",{type:_.type,uid:_.uid,topic_id:_.topic_id,area:_.area}),_.type==ot.StartTool)x.value=!0,Y.value=!0,Qe.value=_.content;else if(_.uid!=="default")_.topic_id||(f.log("[Stream] 准备处理非默认消息:",_),te(_));else{const P=t.taskmessage_id,Me=le(_.data||"",P);ae+=Me.normalText,ie(P)&&(t.thinkingContent=re(P),t.thinkingCompleted=ce(P))}}),J.value+=ae;try{t.content=J.value,T&&(console.log(T,"====================="),t.taskmessage_id=T,a.taskmessage_id=T),r.value=[...r.value],b()}catch{}}}x.value=!1,B.value=!0,Z.value.focus(),b(),r.value=[...r.value],V.value=!0,Q.value=!1})().catch(U=>{f.error("处理流数据失败:",U),x.value=!1,B.value=!0,r.value.pop(),r.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),b()})}else throw new Error("Invalid response format for stream mode");else if(B.value=!0,o.data.error=="1")E.error("应用不存在");else if(o.data.error=="0"){x.value=!1,Q.value=!1;const{topic_id:p,content:n,taskmessage_id:w}=o.data;r.value.push({role:"assistant",content:n,taskmessage_id:w}),F.value=p,a.taskmessage_id=w,b(),V.value=!0}}}catch(s){f.error("发送消息失败:",s),x.value=!1,B.value=!0,r.value.pop(),r.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),b()}},_t=async e=>{const a=await Nt(e);if(a.data.error=="0"){r.value=r.value.filter(o=>!(o.taskmessage_id===e&&o.role==="assistant"));const t=r.value.find(o=>o.taskmessage_id===e&&o.role==="user");J.value="",ye();let s={role:"assistant",content:"",taskmessage_id:"",thinkingContent:"",thinkingCompleted:!1,message_comment:{}};if(r.value.push(s),b(),Y.value=!1,I.value="",x.value=!0,V.value=!1,B.value=!1,Q.value=!0,L.value=!1,ue(),m.value=!1,t)try{const o=await tt(t.content,$.value,F.value,$.value?"":"default");if(b(),Ke.value==1)if(o&&o.getReader){const p=o.getReader(),n=new TextDecoder;let w=!1;(async()=>{for(;!w;){const{value:U,done:Re}=await p.read();if(w=Re,U){const Ee=n.decode(U,{stream:!0});f.log("[Stream] 收到新的数据块");let Be="";const{content:z,taskmessage_id:T,error:oe}=Je(Ee);if(T&&(Be=T),f.log("[Stream] 解析后的数据结构:",{content:z,hasFileList:z.some&&z.some(_=>_.uid==="file-list-component"),taskmessage_id:T,error:oe}),oe=="1"){E.error("应用不存在"),x.value=!1;return}let ae="";z.map(_=>{if(f.log("[Stream] 处理单条数据:",{type:_.type,uid:_.uid,topic_id:_.topic_id,area:_.area}),_.type==ot.StartTool)x.value=!0,Y.value=!0,Qe.value=_.content;else if(_.uid!=="default")_.topic_id||(f.log("[Stream] 准备处理非默认消息:",_),te(_));else{const P=s.taskmessage_id,Me=le(_.data||"",P);ae+=Me.normalText,ie(P)&&(s.thinkingContent=re(P),s.thinkingCompleted=ce(P))}}),J.value+=ae;try{s.content=J.value,T&&(s.taskmessage_id=T,t.taskmessage_id=T),r.value=[...r.value],b()}catch{}}}x.value=!1,B.value=!0,Z.value.focus(),b(),r.value=[...r.value],V.value=!0,Q.value=!1})().catch(U=>{f.error("处理流数据失败:",U),x.value=!1,B.value=!0,r.value.pop(),r.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话",taskmessage_id:"",thinkingContent:"",thinkingCompleted:!1,message_comment:{}}),b()})}else throw new Error("Invalid response format for stream mode");else if(B.value=!0,o.data.error=="1")E.error("应用不存在");else if(o.data.error=="0"){x.value=!1,Q.value=!1;const{topic_id:p,content:n,taskmessage_id:w}=o.data;r.value.push({role:"assistant",content:n,taskmessage_id:w}),F.value=p,t.taskmessage_id=w,b(),V.value=!0}}catch{s.content="当前请求网络可能有问题，请重新发起对话",r.value=[...r.value],Y.value=!1,I.value="",x.value=!1,V.value=!1,B.value=!1,Q.value=!0,L.value=!1,ue(),m.value=!1}}else a.message&&E.error(a.message)};fe(I,e=>{e!==""?c.value=!0:c.value=!1});const ht=e=>{K.value&&e.deltaY<0&&(X.value=!1)},b=()=>{ne(()=>{const e=K.value;e&&(e.addEventListener("wheel",ht),X.value&&(e.scrollTop=e.scrollHeight)),Z.value?Z.value.focus():f.error("inputTxtRef is null")})},Ae=v(!0);let de=0;const Le=()=>{const e=K.value.scrollTop;Math.abs(e-de)>1&&(de==0?Ae.value=!0:e>de?Ae.value=!1:Ae.value=!0),de=e},gt=e=>{try{var a=/([\n\r])+/g;const t=e.replace(a,`
`),s=document.createElement("textarea");s.value=t,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s),E.success("内容已复制")}catch{E.error("复制失败")}},Ct=et(()=>d.query.topic_id);fe(Ct,e=>{e&&(F.value=e,Ge())});const qe=v(null),Ge=async()=>{qe.value=Mt.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"}),r.value=[];const e=await Kt(d.query.topic_id);qe.value.close();for(const a of e.data){const t={role:"user",content:a.input_content,taskmessage_id:a.id,create_time:a.create_time,favorite:a.favorite};r.value.push(t);const s={role:"assistant",content:"",taskmessage_id:a.id,create_time:a.create_time,thinkingContent:"",thinkingCompleted:!1,message_comment:a.message_comment?a.message_comment:{}};r.value.push(s);try{const o=JSON.parse(a.output_content);o?await Promise.all(o.map(async p=>{p.uid!=="default"?await new Promise(n=>{te(p),setTimeout(n,100)}):s.content=p.data})):s.content="暂无回答"}catch{s.content=a.output_content}}ue(),X.value=!0,b()};bt(async()=>{if(d.query.title)try{const a=decodeURIComponent(d.query.title);Oe(a)}catch(a){f.error("标题解码失败:",a),Oe(d.query.title)}d.query.topic_id&&Ge(),se.value?(I.value=se.value,xe()):d.query.content?I.value=decodeURIComponent(d.query.content):(g.value||await u.fetchUserInfo(),setTimeout(()=>{Ne()},1e3)),K.value&&K.value.addEventListener("scroll",Le);const{onMessage:e}=st();e(q.TRIGGER_PREVIEW,We),e(q.TRIGGER_WORKBENCH,$e)}),It(()=>{K.value&&K.value.removeEventListener("scroll",Le);const{offMessage:e}=st();e(q.TRIGGER_PREVIEW,We),e(q.TRIGGER_WORKBENCH,$e)});const{updatePreviewComponents:kt}=Xt(),be=v([]);let pe=null;const j=Tt(null),Ie=v(null),ee=v(null),We=e=>{te(e)},$e=({uid:e,data:a})=>{f.log("收到工作台消息:",{uid:e,data:a}),Promise.resolve().then(()=>(j.value=ct(e),Ie.value=`${e}-${Date.now()}`,ee.value=a,ne())).then(()=>{L.value=!0,f.log("工作台状态更新:",{component:j.value,showPreview:L.value,rightBottomData:ee.value})})},te=e=>{e.uid==="file-list-component"&&f.log("收到文件列表消息:",e),be.value.push(e),pe&&clearTimeout(pe),pe=setTimeout(()=>{const a=kt(be.value),t=r.value[r.value.length-1];t&&t.role==="assistant"&&(a.forEach(s=>{s.area==="mid-txt-bottom"&&s.uid==="file-list-component"&&f.log("文件列表组件更新:",{组件名:s.component,组件Key:s.componentKey}),s.area==="mid-def-top"?(t.topComponent=s.component,t.topComponentKey=s.componentKey,t.topData=s.data):s.area==="mid-def-bottom"?(t.bottomComponent=s.component,t.bottomComponentKey=s.componentKey,t.bottomData=s.data):s.area==="mid-txt-bottom"?(t.txtBottomComponent=s.component,t.txtBottomComponentKey=s.componentKey,t.txtBottomData=s.data):s.area==="right-bottom"&&(j.value=s.component,Ie.value=s.componentKey,ee.value=s.data)}),r.value=[...r.value],ne(()=>{t.txtBottomComponent==="file-list-component"&&f.log("DOM更新后文件列表组件状态:",{是否存在组件:!!t.txtBottomComponent,组件数据:t.txtBottomData}),setTimeout(()=>{X.value&&b()},100)})),be.value=[],pe=null},0)};fe([j],([e])=>{e&&ne(()=>{L.value=!0,f.log("工作台watch更新状态:",{showPreview:L.value,component:e})})});const wt=()=>{L.value&&(L.value=!1)},Ye=Ve(async(e,a)=>{console.log(e,"----点赞");try{const t=await Ft(e.taskmessage_id,a);t.data.error=="0"?e.message_comment.vote=a:E.error(t.data.message)}catch(t){E.error("操作错误",t)}},100),Ze=Ve(async e=>{try{const a=await jt(e.taskmessage_id);a.data.error=="0"?e.message_comment.vote="":E.error(a.data.message)}catch(a){E.error("操作错误",a)}},100),Te=Ve(async(e,a)=>{e.favoriteLoading=!0;try{if(a=="0"){const t=await Qt(e.favorite.id);t.data.error=="0"?e.favorite=null:E.error(t.data.message)}else{const t=await Ot(e.taskmessage_id,e.content,d.query.app_id||"");t.data.error=="0"?e.favorite={id:t.data.favorite_id}:E.error(t.data.message)}}catch(t){E.error("操作错误",t)}finally{e.favoriteLoading=!1}},100);return(e,a)=>{const t=me("el-icon"),s=me("el-button"),o=me("el-tooltip"),p=me("el-input");return i(),k("div",ko,[l("div",{class:"el-main-left",onClick:wt},[l("div",wo,[l("div",yo,[l("div",xo,[l("div",{ref_key:"scrollContainer",ref:K,class:"taskmessage-div overflow-y",onScroll:Le},[l("div",{ref_key:"innerRef",ref:ut},[r.value.length<=0?(i(),k("div",Ao,[l("div",null,De(Ue.value),1),l("div",null,De(ze.value),1)])):(i(),k("div",Lo,[(i(!0),k(_e,null,Rt(r.value,(n,w)=>(i(),k(_e,{key:w},[n.role==="user"?(i(),k("div",bo,[l("div",Io,[l("div",{class:"text-26 font-zhongcu leading-30 whitespace-pre-wrap markdown-content user-message max-w-[96%]",innerHTML:Fe(n.content)},null,8,To),n.favorite&&n.favorite.id?(i(),C(o,{key:0,class:"box-item",effect:"dark",content:"取消收藏",placement:"bottom"},{default:y(()=>[R(s,{type:"",size:"small",text:"",onClick:A=>D(Te)(n,"0"),class:"flex-shrink-0 w-[20px] h-[20px] ml-1",disabled:n.favoriteLoading},{default:y(()=>[n.favoriteLoading?(i(),C(t,{key:0,class:"is-loading"},{default:y(()=>[R(D(He))]),_:1})):(i(),C(ho,{key:1,size:"20"}))]),_:2},1032,["onClick","disabled"])]),_:2},1024)):(i(),k(_e,{key:1},[w!==r.value.length-2?(i(),C(o,{key:0,class:"box-item",effect:"dark",content:"收藏指令",placement:"bottom"},{default:y(()=>[R(s,{type:"",size:"small",text:"",onClick:A=>D(Te)(n,"1"),class:"flex-shrink-0 w-[20px] h-[20px] ml-1",disabled:n.favoriteLoading},{default:y(()=>[n.favoriteLoading?(i(),C(t,{key:0,class:"is-loading"},{default:y(()=>[R(D(He))]),_:1})):(i(),C(lt,{key:1,size:"20"}))]),_:2},1032,["onClick","disabled"])]),_:2},1024)):(i(),k(_e,{key:1},[V.value?(i(),C(o,{key:0,class:"box-item",effect:"dark",content:"收藏指令",placement:"bottom"},{default:y(()=>[R(s,{type:"",size:"small",text:"",onClick:A=>D(Te)(n,"1"),class:"flex-shrink-0 w-[20px] h-[20px] ml-1",disabled:n.favoriteLoading},{default:y(()=>[n.favoriteLoading?(i(),C(t,{key:0,class:"is-loading"},{default:y(()=>[R(D(He))]),_:1})):(i(),C(lt,{key:1,size:"20"}))]),_:2},1032,["onClick","disabled"])]),_:2},1024)):S("",!0)],64))],64))]),n.create_time?(i(),k("div",Ro,De(n.create_time),1)):S("",!0)])):n.role==="assistant"?(i(),k("div",Eo,[Bo,l("div",Mo,[n.topComponent?(i(),C(he(n.topComponent),{key:n.topComponentKey,data:n.topData,"onUpdate:data":A=>n.topData=A},null,40,["data","onUpdate:data"])):S("",!0),n.thinkingContent?(i(),C(Pt,{key:1,content:n.thinkingContent,completed:n.thinkingCompleted},null,8,["content","completed"])):S("",!0),l("div",{class:"mt-[20px] leading-30 text-base",innerHTML:Fe(n.content)},null,8,So),n.txtBottomComponent?(i(),k("div",Vo,[(i(),C(he(n.txtBottomComponent),{key:n.txtBottomComponentKey,data:n.txtBottomData,"onUpdate:data":A=>n.txtBottomData=A},null,40,["data","onUpdate:data"]))])):S("",!0)]),x.value&&w==r.value.length-1?(i(),k("img",Do)):S("",!0),w!==r.value.length-1||V.value?(i(),k("div",Ho,[R(o,{effect:"dark",content:"复制",placement:"bottom"},{default:y(()=>[l("button",{class:"p-0 border-none bg-transparent",onClick:A=>gt(n.content)},Po,8,Uo)]),_:2},1024),n.role==="assistant"&&w===r.value.length-1?(i(),C(o,{key:0,effect:"dark",content:"重新生成",placement:"bottom"},{default:y(()=>[l("button",{class:"p-0 border-none bg-transparent ml-5",onClick:A=>_t(n.taskmessage_id)},Oo,8,Ko)]),_:2},1024)):S("",!0),No,R(o,{effect:"dark",content:"赞",placement:"bottom"},{default:y(()=>[n.message_comment&&n.message_comment.vote=="1"?(i(),k("button",{key:0,class:"p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]",onClick:A=>D(Ze)(n)},[R(io,{size:"20"})],8,jo)):(i(),k("button",{key:1,class:"p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]",onClick:A=>D(Ye)(n,"1")},[R(oo,{color:"#121619",size:"20",style:{width:"20px",height:"20px"}})],8,Fo))]),_:2},1024),R(o,{effect:"dark",content:"踩",placement:"bottom"},{default:y(()=>[n.message_comment&&n.message_comment.vote=="0"?(i(),k("button",{key:0,class:"p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]",onClick:A=>D(Ze)(n)},[R(po,{size:"21"})],8,Jo)):(i(),k("button",{key:1,class:"p-0 border-none bg-transparent ml-5 w-[20px] h-[20px]",onClick:A=>D(Ye)(n,"0")},[R(so,{color:"#121619",size:"20"})],8,qo))]),_:2},1024)])):S("",!0),n.bottomComponent?(i(),k("div",Go,[(i(),C(he(n.bottomComponent),{key:n.bottomComponentKey,data:n.bottomData,"onUpdate:data":A=>n.bottomData=A},null,40,["data","onUpdate:data"]))])):S("",!0)])):S("",!0)],64))),128))]))],512)],544),l("div",{class:"chat-input-box",style:Et({borderColor:m.value?"":"#d9d9d9"})},[l("div",Wo,[R(p,{modelValue:I.value,"onUpdate:modelValue":a[0]||(a[0]=n=>I.value=n),autosize:{minRows:1,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!B.value,maxlength:"20000",onKeydown:mt,onFocus:a[1]||(a[1]=n=>m.value=!0),ref_key:"inputTxtRef",ref:Z},null,8,["modelValue","disabled"])]),l("div",$o,[l("div",Yo,[l("button",{class:N([c.value?"":"send-btn-no","flex items-center justify-center"]),disabled:!c.value,onClick:xe},ea,10,Zo)])])],4)])]),ta])]),L.value?S("",!0):(i(),k("button",{key:0,class:"border-0 bg-transparent p-0",onClick:a[2]||(a[2]=n=>L.value=!0),style:{position:"fixed",top:"40px",right:"20px"}},aa)),l("div",{class:N(["el-main-right z-10 relative overflow-hidden",{open:L.value}])},[l("div",na,[sa,l("button",{class:"border-0 bg-transparent p-0",onClick:a[3]||(a[3]=Bt(n=>L.value=!1,["stop"]))},ra)]),l("div",ia,[j.value?(i(),C(he(j.value),{key:Ie.value,data:ee.value,"onUpdate:data":a[4]||(a[4]=n=>ee.value=n)},null,40,["data"])):(i(),k("div",ca,da))])],2)])}}},fa=Ht(pa,[["__scopeId","data-v-0a1f0475"]]),ha=Object.freeze(Object.defineProperty({__proto__:null,default:fa},Symbol.toStringTag,{value:"Module"}));export{ha as C,_a as f,st as u};
