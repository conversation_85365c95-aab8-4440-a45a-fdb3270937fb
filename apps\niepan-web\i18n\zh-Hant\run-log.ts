const translation = {
  input: '輸入',
  result: '執行結果',
  detail: '結果詳情',
  tracing: '追蹤',
  resultPanel: {
    status: '狀態',
    time: '執行時間',
    tokens: '總 token 數',
  },
  meta: {
    title: '元資料',
    status: '狀態',
    version: '版本',
    executor: '執行人',
    startTime: '開始時間',
    time: '執行時間',
    tokens: '總 token 數',
    steps: '執行步數',
  },
  resultEmpty: {
    title: '本運行僅輸出 JSON 格式，',
    tipLeft: '請到',
    link: '詳細資訊面板',
    tipRight: '查看它。',
  },
  circularInvocationTip: '當前工作流中存在工具/節點的迴圈調用。',
  actionLogs: '作日誌',
};

export default translation;
