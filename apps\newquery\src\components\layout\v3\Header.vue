<template>
  <div>
    <!-- 嵌入页面弹窗蒙版 -->
    <div
      v-if="showEmbedModalOverlay"
      class="fixed inset-0 bg-[#10182899] bg-opacity-50 z-40"
      @click="handleOverlayClick"
    ></div>
    <!-- 顶部导航栏 -->
    <div
      class="top-0 left-0 right-0 h-[56px] flex items-center justify-between px-4 border-solid border-0 border-b border-[#EAEAEA]"
      :class="{
        'z-30': showEmbedModalOverlay,
        'z-[60]': !showEmbedModalOverlay,
      }"
    >
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <img src="@/assets/admin/admin-logo.png" :alt="t('layout.logo')" class="h-[24px]" />
        </a>

        <!-- 工作空间下拉菜单 -->
        <div class="relative" ref="workspaceDropdownRef">
          <div class="flex items-center">
            <div class="text-[#10182824] mx-2 font-light">/</div>
            <div
              @click="toggleWorkspaceDropdown"
              class="flex items-center text-sm cursor-pointer hover:bg-[#f7f9fc] rounded px-1 py-0.5"
            >
              <div
                class="flex h-7 w-7 items-center justify-center rounded-lg bg-[#EBF7FF] text-xs font-medium text-[#129bfe]"
              >
                R
              </div>
              <div class="flex flex-row items-center">
                <div class="system-sm-medium max-w-[80px] truncate text-[#343a3f] ml-1.5">
                  <!-- {{ userStore.userInfo?.tenant?.name || t('layout.workspace') }} -->
                  {{ t('layout.workspace') }}
                </div>

                <svg
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="ml-1 transition-transform duration-200"
                  :class="{
                    'rotate-180': showWorkspaceDropdown,
                  }"
                >
                  <path
                    d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"
                  ></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- 下拉菜单内容 -->
          <div
            v-if="showWorkspaceDropdown && !showEmbedModalOverlay"
            class="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-xl w-[200px] z-[100]"
            style="border: 1px solid #eaeaea"
          >
            <div class="py-1">
              <div class="text-[#343a3f] text-xs px-3 py-1.5">
                {{ t('layout.workspace') }}
              </div>
              <div class="flex items-center px-3 py-2 hover:bg-[#F5F5F5] cursor-pointer">
                <div
                  class="flex h-6 w-6 items-center justify-center rounded-lg bg-[#EBF7FF] text-xs font-medium text-[#129bfe] mr-2"
                >
                  R
                </div>
                <span class="text-[#343a3f] text-[14px] leading-[20px]">
                  <!-- {{ userStore.userInfo?.tenant?.name || t('layout.workspace') }} -->
                  {{ t('layout.workspace') }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center gap-2 relative">
        <!-- 语言选择器 -->
        <!-- <LanguageSelector class="mr-2" /> -->

        <!-- 去工作空间按钮 -->
        <a
          v-if="showWorkspaceButton"
          href="/apps"
          class="flex items-center gap-1 text-[#129BFE] text-[14px] px-3 py-1.5 rounded-[6px] bg-[#CFEAFE] font-medium leading-[22px] font-['PingFang_SC']"
          style="font-family: 'PingFang SC', sans-serif"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            class="flex-shrink-0"
          >
            <g clip-path="url(#clip0_928_19457)">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.5 3.75C7.5 2.92125 8.17125 2.25 9 2.25C9.82875 2.25 10.5 2.92125 10.5 3.75V4.50375C10.5 4.91625 10.8375 5.25375 11.25 5.25375C11.6625 5.25375 12 4.91625 12 4.50375V3.75C12 2.0925 10.6575 0.75 9 0.75C7.3425 0.75 6 2.0925 6 3.75V8.9775C6 9.39 6.3375 9.7275 6.75 9.7275C7.1625 9.7275 7.5 9.39 7.5 8.9775V3.75ZM12 9C12 8.5875 11.6625 8.25 11.25 8.25C10.8375 8.25 10.5 8.5875 10.5 9V14.25C10.5 15.0788 9.82875 15.75 9 15.75C8.17125 15.75 7.5 15.0788 7.5 14.25V13.4888C7.5 13.0763 7.1625 12.7387 6.75 12.7387C6.3375 12.7387 6 13.0763 6 13.4888V14.25C6 15.9075 7.3425 17.25 9 17.25C10.6575 17.25 12 15.9075 12 14.25V9ZM3.7425 7.5C2.9175 7.5 2.25 8.17125 2.25 9C2.25 9.82875 2.92125 10.5 3.7425 10.5H9C9.4125 10.5 9.75 10.8375 9.75 11.25C9.75 11.6625 9.4125 12 9 12H3.7425C2.08875 12 0.75 10.6537 0.75 9C0.75 7.34625 2.08875 6 3.7425 6H4.4925C4.905 6 5.2425 6.3375 5.2425 6.75C5.2425 7.1625 4.905 7.5 4.4925 7.5H3.7425ZM9 6C8.5875 6 8.25 6.3375 8.25 6.75C8.25 7.1625 8.5875 7.5 9 7.5H14.2463C15.0788 7.5 15.75 8.17125 15.75 9C15.75 9.82875 15.0788 10.5 14.2463 10.5H13.5262C13.1137 10.5 12.7762 10.8375 12.7762 11.25C12.7762 11.6625 13.1137 12 13.5262 12H14.2463C15.9038 12 17.25 10.6575 17.25 9C17.25 7.3425 15.9038 6 14.2463 6H9Z"
                fill="#129BFE"
              />
            </g>
            <defs>
              <clipPath id="clip0_928_19457">
                <rect width="18" height="18" fill="white" />
              </clipPath>
            </defs>
          </svg>
          <span>{{ t('layout.goToWorkspace') }}</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            class="flex-shrink-0"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M13.0688 5.33625C13.0313 5.25 12.9788 5.1675 12.9075 5.09625C12.9075 5.09625 12.9075 5.09625 12.9037 5.0925C12.7687 4.9575 12.5813 4.875 12.375 4.875H5.625C5.2125 4.875 4.875 5.2125 4.875 5.625C4.875 6.0375 5.2125 6.375 5.625 6.375H10.5638L5.47125 11.4713C5.17875 11.7638 5.17875 12.24 5.47125 12.5325C5.76375 12.825 6.24 12.825 6.5325 12.5325L11.6288 7.43625V12.375C11.6288 12.7875 11.9663 13.125 12.3788 13.125C12.7913 13.125 13.1288 12.7875 13.1288 12.375V5.625C13.1288 5.52 13.1063 5.42625 13.0725 5.33625H13.0688Z"
              fill="#129BFE"
            />
          </svg>
        </a>
        <!-- 用户头像和用户名 -->
        <div
          class="flex items-center gap-2 cursor-pointer"
          @click="toggleUserInfoDropdown"
          ref="userInfoDropdownRef"
        >
          <div
            class="w-8 h-8 rounded-full bg-gradient-to-br from-[#5F6C90] from-[12.46%] to-[#393D49] to-[85.12%] ml-[10px]"
          >
            <div
              class="w-full h-full flex justify-center leading-7 text-white text-base font-medium"
            >
              {{ getFirstLetter(userStore.userInfo?.name || '') }}
            </div>
          </div>
          <div class="text-[14px] text-[#343A3F] font-medium">
            {{ userStore.userInfo?.name || t('layout.username') }}
          </div>
          <svg
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="currentColor"
            class="ml-1 transition-transform duration-200"
            :class="{
              'rotate-180': showUserInfoDropdown,
            }"
          >
            <path
              d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"
            ></path>
          </svg>
        </div>
        <!-- 用户头像下拉菜单 -->
        <div
          class="absolute top-full right-0 mt-1 bg-white shadow-lg rounded-xl w-[240px] z-[100] px-3 pb-2 border border-[#EAEAEA] text-[#343A3F]"
          v-if="showUserInfoDropdown"
        >
          <div
            class="flex flex-nowrap items-center justify-between p-2"
            style="border-bottom: 1px solid #e5e5e5"
          >
            <div class="text-[#343a3f] text-sm truncate text-text-primary font-medium">
              {{ userStore.userInfo?.email || '' }}
            </div>
            <div
              class="w-8 h-8 rounded-full bg-gradient-to-br from-[#5F6C90] from-[12.46%] to-[#393D49] to-[85.12%] ml-[10px] flex-shrink-0"
            >
              <div
                class="w-full h-full flex justify-center leading-7 text-white text-base font-medium"
              >
                {{ getFirstLetter(userStore.userInfo?.name || '') }}
              </div>
            </div>
          </div>
          <div
            class="p-2 flex items-center text-sm cursor-pointer hover:bg-[#F4F9FD]"
            @click="handleModalUserinfo()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.83892 0.606611C6.94569 0.575451 7.05915 0.575466 7.16591 0.606653L12.4134 2.13955C12.6622 2.21222 12.8332 2.44031 12.8332 2.69948V5.84308C12.8332 9.42479 10.5319 12.2716 7.185 13.3868C7.06531 13.4267 6.93593 13.4267 6.81625 13.3868C3.46851 12.2717 1.1665 9.42416 1.1665 5.84162V2.69948C1.1665 2.44026 1.33757 2.21213 1.58641 2.13951L6.83892 0.606611ZM2.33317 3.13691V5.84162C2.33317 8.8049 4.18075 11.2027 7.00057 12.2161C9.81957 11.2027 11.6665 8.80556 11.6665 5.84308V3.13679L7.00227 1.77428L2.33317 3.13691ZM6.99984 4.37492C6.51659 4.37492 6.12484 4.76667 6.12484 5.24992C6.12484 5.73317 6.51659 6.12492 6.99984 6.12492C7.48309 6.12492 7.87484 5.73317 7.87484 5.24992C7.87484 4.76667 7.48309 4.37492 6.99984 4.37492ZM4.95817 5.24992C4.95817 4.12234 5.87226 3.20825 6.99984 3.20825C8.12742 3.20825 9.0415 4.12234 9.0415 5.24992C9.0415 5.75485 8.85821 6.21697 8.55452 6.57337C9.37291 7.08993 9.9165 8.00227 9.9165 9.04159C9.9165 9.36375 9.65534 9.62492 9.33317 9.62492C9.011 9.62492 8.74984 9.36375 8.74984 9.04159C8.74984 8.07508 7.96634 7.29159 6.99984 7.29159C6.03333 7.29159 5.24984 8.07508 5.24984 9.04159C5.24984 9.36375 4.98867 9.62492 4.6665 9.62492C4.34434 9.62492 4.08317 9.36375 4.08317 9.04159C4.08317 8.00227 4.62677 7.08993 5.44515 6.57337C5.14147 6.21697 4.95817 5.75485 4.95817 5.24992Z"
                fill="#697077"
              />
            </svg>
            <div class="ml-2">{{ t('layout.accountInfo') }}</div>
          </div>
          <div
            class="p-2 flex items-center text-sm cursor-pointer hover:bg-[#F4F9FD]"
            @click="handleModalLanguage()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0.583496 6.99992C0.583496 3.45617 3.45641 0.583252 7.00016 0.583252C10.5439 0.583252 13.4168 3.45617 13.4168 6.99992C13.4168 10.5437 10.5439 13.4166 7.00016 13.4166C3.45641 13.4166 0.583496 10.5437 0.583496 6.99992ZM11.1243 10.2491C11.7164 9.4995 12.1072 8.58367 12.2181 7.58325H9.90516C9.88183 8.20159 9.82058 8.7995 9.72725 9.35659C10.2318 9.59284 10.7014 9.89617 11.1243 10.2491ZM10.2989 11.0833C10.0393 10.8733 9.75933 10.6895 9.46475 10.5291C9.38308 10.8178 9.29266 11.0891 9.19058 11.3399C9.11475 11.5266 9.036 11.7045 8.9485 11.8737C9.4385 11.6783 9.8935 11.407 10.2989 11.0803V11.0833ZM8.61016 8.95409C8.67433 8.52242 8.71808 8.0645 8.7385 7.58325H5.26183C5.28225 8.0645 5.326 8.52242 5.39016 8.95409C5.9035 8.81992 6.44308 8.74992 7.00016 8.74992C7.55725 8.74992 8.09683 8.81992 8.61016 8.95409ZM8.37391 10.0974C7.93641 9.97784 7.47558 9.91659 7.00016 9.91659C6.52475 9.91659 6.06391 9.98075 5.62641 10.0974C5.70516 10.392 5.79558 10.6633 5.89183 10.9083C6.08725 11.3953 6.30308 11.7512 6.516 11.9728C6.726 12.1945 6.88933 12.2499 7.00016 12.2499C7.111 12.2499 7.27433 12.1974 7.48433 11.9728C7.69725 11.7483 7.91308 11.3953 8.1085 10.9083C8.20766 10.6633 8.29516 10.392 8.37391 10.0974ZM4.27308 9.35659C4.17975 8.7995 4.1185 8.20159 4.09516 7.58325H1.78225C1.89308 8.58367 2.28391 9.4995 2.876 10.2491C3.29891 9.89325 3.7685 9.59284 4.27308 9.35659ZM3.70141 11.0833C3.961 10.8733 4.241 10.6895 4.53558 10.5291C4.61725 10.8178 4.70766 11.0891 4.80975 11.3399C4.88558 11.5266 4.96433 11.7045 5.05183 11.8737C4.55891 11.6783 4.10683 11.407 3.70141 11.0803V11.0833ZM9.90516 6.41659H12.2181C12.1072 5.41617 11.7164 4.50034 11.1243 3.75075C10.7014 4.10659 10.2318 4.407 9.72725 4.64325C9.82058 5.20034 9.88183 5.79825 9.90516 6.41659ZM10.3018 2.91659C10.0422 3.12659 9.76225 3.31034 9.46766 3.47075C9.386 3.182 9.29558 2.91075 9.1935 2.65992C9.11766 2.47325 9.03891 2.29534 8.95141 2.12617C9.44433 2.32159 9.89641 2.59284 10.3018 2.9195V2.91659ZM7.00016 4.08325C7.47558 4.08325 7.93641 4.01909 8.37391 3.90242C8.29516 3.60784 8.20475 3.33659 8.1085 3.09159C7.91308 2.6045 7.69725 2.24867 7.48433 2.027C7.27433 1.80534 7.111 1.74992 7.00016 1.74992C6.88933 1.74992 6.726 1.80242 6.516 2.027C6.30308 2.25159 6.08725 2.6045 5.89183 3.09159C5.79266 3.33659 5.70516 3.60784 5.62641 3.90242C6.06391 4.022 6.52475 4.08325 7.00016 4.08325ZM7.00016 5.24992C6.44308 5.24992 5.9035 5.17992 5.39016 5.04575C5.326 5.47742 5.28225 5.93534 5.26183 6.41659H8.7385C8.71808 5.93534 8.67433 5.47742 8.61016 5.04575C8.09683 5.17992 7.55725 5.24992 7.00016 5.24992ZM4.80975 2.65992C4.70766 2.91075 4.61725 3.182 4.53558 3.47075C4.23808 3.31325 3.961 3.12659 3.70141 2.91659C4.10683 2.58992 4.56183 2.31867 5.05183 2.12325C4.96433 2.29242 4.88266 2.47034 4.80975 2.657V2.65992ZM4.27308 4.64325C3.7685 4.407 3.29891 4.10367 2.876 3.75075C2.28391 4.50034 1.89308 5.41617 1.78225 6.41659H4.09516C4.1185 5.79825 4.17975 5.20034 4.27308 4.64325Z"
                fill="#697077"
              />
            </svg>
            <div class="ml-2">
              {{ t('layout.languageAndTimezone') }}
            </div>
          </div>
          <div
            class="p-2 flex justify-between items-center text-sm cursor-pointer hover:bg-[#F4F9FD]"
            style="border-bottom: 1px solid #e5e5e5"
            @click="linkSettingDepartment()"
          >
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M2.04167 1.3125C1.39708 1.3125 0.875 1.83458 0.875 2.47917V9.1875C0.875 9.83208 1.39708 10.3542 2.04167 10.3542H11.9583C12.6029 10.3542 13.125 9.83208 13.125 9.1875V2.47917C13.125 1.83458 12.6029 1.3125 11.9583 1.3125H2.04167ZM9.33333 11.5208H4.08333C3.7625 11.5208 3.5 11.7833 3.5 12.1042C3.5 12.425 3.7625 12.6875 4.08333 12.6875H9.91667C10.2375 12.6875 10.5 12.425 10.5 12.1042C10.5 11.7833 10.2375 11.5208 9.91667 11.5208H9.33333ZM2.04167 9.1875H11.9583V2.47917H2.04167V9.1875ZM7.95375 2.94583C8.27458 2.94583 8.53708 3.20833 8.53708 3.52917V5.27917C8.53708 5.6 8.27458 5.8625 7.95375 5.8625C7.63292 5.8625 7.37042 5.6 7.37042 5.27917V4.9875H3.57875C3.25792 4.9875 2.99542 4.725 2.99542 4.40417C2.99542 4.08333 3.25792 3.82083 3.57875 3.82083H7.37042V3.52917C7.37042 3.20833 7.63292 2.94583 7.95375 2.94583ZM9.12042 4.40417C9.12042 4.08333 9.38292 3.82083 9.70375 3.82083H10.5788C10.8996 3.82083 11.1621 4.08333 11.1621 4.40417C11.1621 4.725 10.8996 4.9875 10.5788 4.9875H9.70375C9.38292 4.9875 9.12042 4.725 9.12042 4.40417ZM6.20375 5.8625C6.52458 5.8625 6.78708 6.125 6.78708 6.44583V6.7375H10.5788C10.8996 6.7375 11.1621 7 11.1621 7.32083C11.1621 7.64167 10.8996 7.90417 10.5788 7.90417H6.78708V8.19583C6.78708 8.51667 6.52458 8.77917 6.20375 8.77917C5.88292 8.77917 5.62042 8.51667 5.62042 8.19583V6.44583C5.62042 6.125 5.88292 5.8625 6.20375 5.8625ZM2.70375 7.32083C2.70375 7 2.96625 6.7375 3.28708 6.7375H4.45375C4.77458 6.7375 5.03708 7 5.03708 7.32083C5.03708 7.64167 4.77458 7.90417 4.45375 7.90417H3.28708C2.96625 7.90417 2.70375 7.64167 2.70375 7.32083Z"
                  fill="#697077"
                />
              </svg>
              <div class="ml-2">
                {{ t('layout.systemManagement') }}
              </div>
            </div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 12 12"
              fill="none"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.7125 3.5575C8.6875 3.5 8.6525 3.445 8.605 3.3975C8.605 3.3975 8.605 3.3975 8.6025 3.395C8.5125 3.305 8.3875 3.25 8.25 3.25H3.75C3.475 3.25 3.25 3.475 3.25 3.75C3.25 4.025 3.475 4.25 3.75 4.25H7.0425L3.6475 7.6475C3.4525 7.8425 3.4525 8.16 3.6475 8.355C3.8425 8.55 4.16 8.55 4.355 8.355L7.7525 4.9575V8.25C7.7525 8.525 7.9775 8.75 8.2525 8.75C8.5275 8.75 8.7525 8.525 8.7525 8.25V3.75C8.7525 3.68 8.7375 3.6175 8.715 3.5575H8.7125Z"
                fill="#697077"
              />
            </svg>
          </div>
          <div class="p-2 flex items-center text-sm hover:bg-[#F4F9FD]" @click="handleHelpLink()">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2.4629 2.46265C3.62346 1.3021 5.22842 0.583252 7.00016 0.583252C8.7719 0.583252 10.3769 1.3021 11.5374 2.46265L11.1249 2.87513L11.5374 2.46265C12.698 3.62321 13.4168 5.22818 13.4168 6.99992C13.4168 8.77166 12.698 10.3766 11.5374 11.5372C10.3769 12.6977 8.77191 13.4166 7.00016 13.4166C5.22842 13.4166 3.62346 12.6977 2.4629 11.5372L2.87537 11.1247L2.46289 11.5372C1.30235 10.3766 0.583496 8.77166 0.583496 6.99992C0.583496 5.22818 1.30235 3.62321 2.46289 2.46265L2.4629 2.46265ZM7.00016 1.74992C5.55027 1.74992 4.23857 2.33691 3.28785 3.28761C2.33714 4.23833 1.75016 5.55003 1.75016 6.99992C1.75016 8.44981 2.33715 9.76152 3.28785 10.7122C4.23857 11.6629 5.55027 12.2499 7.00016 12.2499C8.45006 12.2499 9.76176 11.6629 10.7125 10.7122C11.6632 9.76151 12.2502 8.44981 12.2502 6.99992C12.2502 5.55003 11.6632 4.23833 10.7125 3.28761C9.76176 2.33691 8.45006 1.74992 7.00016 1.74992ZM4.66683 5.43221C4.66683 4.14355 5.7115 3.09888 7.00016 3.09888C8.28883 3.09888 9.3335 4.14355 9.3335 5.43221C9.3335 6.51945 8.58988 7.43301 7.5835 7.69203V8.34888C7.5835 8.67104 7.32233 8.93221 7.00016 8.93221C6.678 8.93221 6.41683 8.67104 6.41683 8.34888V7.18221C6.41683 6.86004 6.678 6.59888 7.00016 6.59888C7.64449 6.59888 8.16683 6.07654 8.16683 5.43221C8.16683 4.78788 7.64449 4.26554 7.00016 4.26554C6.35583 4.26554 5.8335 4.78788 5.8335 5.43221C5.8335 5.75438 5.57233 6.01554 5.25016 6.01554C4.928 6.01554 4.66683 5.75438 4.66683 5.43221ZM7.72933 10.2447C7.72933 10.6474 7.40287 10.9739 7.00016 10.9739C6.59746 10.9739 6.271 10.6474 6.271 10.2447C6.271 9.84201 6.59746 9.51554 7.00016 9.51554C7.40287 9.51554 7.72933 9.84201 7.72933 10.2447Z"
                fill="#697077"
              />
            </svg>
            <div class="ml-2">{{ t('layout.help') }}</div>
          </div>
          <div
            class="p-2 flex items-center text-sm cursor-pointer justify-between hover:bg-[#F4F9FD]"
            style="border-bottom: 1px solid #e5e5e5"
            @click="handleModalAbout()"
          >
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 14 14"
                fill="none"
              >
                <g clip-path="url(#clip0_2262_73890)">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M6.99992 0.583496C5.2295 0.583496 3.62242 1.301 2.46159 2.46183C1.30075 3.62266 0.583252 5.22975 0.583252 7.00016C0.583252 8.77058 1.30075 10.3777 2.46159 11.5385C3.62242 12.6993 5.22659 13.4168 6.99992 13.4168C8.77325 13.4168 10.3774 12.6993 11.5383 11.5385C12.6991 10.3777 13.4166 8.7735 13.4166 7.00016C13.4166 5.22683 12.6991 3.62266 11.5383 2.46183C10.3774 1.301 8.77325 0.583496 6.99992 0.583496ZM3.287 3.28725C4.23784 2.33641 5.55034 1.75016 6.99992 1.75016C8.4495 1.75016 9.762 2.33641 10.7128 3.28725C11.6637 4.23808 12.2499 5.55058 12.2499 7.00016C12.2499 8.44975 11.6637 9.76225 10.7128 10.7131C9.762 11.6639 8.4495 12.2502 6.99992 12.2502C5.55034 12.2502 4.23784 11.6639 3.287 10.7131C2.33617 9.76225 1.74992 8.44975 1.74992 7.00016C1.74992 5.55058 2.33617 4.23808 3.287 3.28725ZM7.72909 3.93766C7.72909 3.53516 7.40242 3.2085 6.99992 3.2085C6.59742 3.2085 6.27075 3.53516 6.27075 3.93766C6.27075 4.34016 6.59742 4.66683 6.99992 4.66683C7.40242 4.66683 7.72909 4.34016 7.72909 3.93766ZM5.97909 5.8335C5.97909 5.51266 6.24159 5.25016 6.56242 5.25016H7.14575C7.46659 5.25016 7.72909 5.51266 7.72909 5.8335V9.3335H8.16659C8.48742 9.3335 8.74992 9.596 8.74992 9.91683C8.74992 10.2377 8.48742 10.5002 8.16659 10.5002H6.12492C5.80409 10.5002 5.54159 10.2377 5.54159 9.91683C5.54159 9.596 5.80409 9.3335 6.12492 9.3335H6.56242V6.41683C6.24159 6.41683 5.97909 6.15433 5.97909 5.8335Z"
                    fill="#697077"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_2262_73890">
                    <rect width="14" height="14" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <div class="ml-2">{{ t('layout.about') }}</div>
            </div>
            <div v-if="current_version">v {{ current_version }}</div>
          </div>
          <div
            class="p-2 flex items-center text-sm cursor-pointer hover:bg-[#F4F9FD]"
            @click="handleLogout()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.74984 1.16675C1.42767 1.16675 1.1665 1.42792 1.1665 1.75008V12.2501C1.1665 12.5722 1.42767 12.8334 1.74984 12.8334H6.99984C7.322 12.8334 7.58317 12.5722 7.58317 12.2501C7.58317 11.9279 7.322 11.6667 6.99984 11.6667H2.33317V2.33341H6.99742C7.31958 2.33341 7.58075 2.07225 7.58075 1.75008C7.58075 1.42792 7.31958 1.16675 6.99742 1.16675H1.74984ZM9.21236 3.9626C9.44016 3.7348 9.80951 3.7348 10.0373 3.9626L12.6304 6.55573C12.7546 6.66271 12.8332 6.82108 12.8332 6.9978L12.8332 6.99891C12.8335 7.14859 12.7765 7.29836 12.6623 7.41256L10.0373 10.0376C9.80951 10.2654 9.44016 10.2654 9.21236 10.0376C8.98455 9.80976 8.98455 9.44041 9.21236 9.2126L10.8438 7.58114H4.6665C4.34434 7.58114 4.08317 7.31997 4.08317 6.9978C4.08317 6.67564 4.34434 6.41447 4.6665 6.41447H10.8393L9.21236 4.78756C8.98455 4.55975 8.98455 4.19041 9.21236 3.9626Z"
                fill="#697077"
              />
            </svg>
            <div class="ml-2">{{ t('layout.logout') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 语言设置弹窗 -->
    <ModalLanguage v-if="showModalLanguage" :onCancel="() => (showModalLanguage = false)" />

    <!-- 账户信息设置弹窗 -->
    <ModalUserinfo v-if="showModalUserinfo" :onCancel="() => (showModalUserinfo = false)" />

    <!-- 关于版本弹窗 -->
    <ModalAbout v-if="showModalAbout" :onCancel="() => (showModalAbout = false)" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref, onBeforeUnmount, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useMenuV3Store } from '@/store/modules/menuV3';
import { useUserStore } from '@/store/modules/userInfo';
import { getFirstLetter } from '@/utils/stringUtils';
import { useDevelopingTip } from '@/hooks/useDevelopingTip';
import ModalLanguage from '@/components/modalLanguage/index.vue';
import ModalUserinfo from '@/components/modalUserinfo/index.vue';
import LanguageSelector from '@/components/LanguageSelector.vue';
import ModalAbout from '@/components/modalAbout/index.vue';

// 初始化i18n
const { t } = useI18n();

// 定义组件属性
const props = defineProps({
  showWorkspaceButton: {
    type: Boolean,
    default: true,
  },
});

// 嵌入页面弹窗相关状态和方法
const showEmbedModalOverlay = ref(false);

// 处理蒙版点击事件
const handleOverlayClick = () => {
  // 可以选择是否允许点击蒙版关闭弹窗
  // 这里我们仅做演示，实际上可能需要将消息传回嵌入页面
  console.log('点击了蒙版');
  // showEmbedModalOverlay.value = false;
};

// 监听来自嵌入页面的消息
const setupEmbedMessageListener = () => {
  const messageHandler = (event: MessageEvent) => {
    // 安全检查 - 在生产环境中应该检查 event.origin
    // if (event.origin !== 'http://localhost:3000') return;
    // debugger;
    const message = event.data;

    // 处理弹窗消息
    if (message && message.from === 'niepan-embed') {
      if (message.type === 'modal-opened') {
        showEmbedModalOverlay.value = true;
      } else if (message.type === 'modal-closed') {
        showEmbedModalOverlay.value = false;
      } else if (message.type === 'reload-page') {
        window.location.reload();
      }
    }
  };

  window.addEventListener('message', messageHandler);

  // 返回清理函数
  return () => {
    window.removeEventListener('message', messageHandler);
  };
};

const route = useRoute();
const router = useRouter();
const menuStore = useMenuV3Store();
const userStore = useUserStore();
const { showTip } = useDevelopingTip();

// 工作空间下拉菜单相关
const showWorkspaceDropdown = ref(false);
const workspaceDropdownRef = ref<HTMLElement | null>(null);

const toggleWorkspaceDropdown = () => {
  showWorkspaceDropdown.value = !showWorkspaceDropdown.value;
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  // 处理工作空间下拉菜单
  if (workspaceDropdownRef.value && !workspaceDropdownRef.value.contains(event.target as Node)) {
    showWorkspaceDropdown.value = false;
  }

  // 处理用户信息下拉菜单
  if (userInfoDropdownRef.value && !userInfoDropdownRef.value.contains(event.target as Node)) {
    showUserInfoDropdown.value = false;
  }
};

onMounted(() => {
  // 添加点击外部关闭下拉菜单的事件监听器
  document.addEventListener('click', handleClickOutside);

  // 设置嵌入页面消息监听器
  const cleanupMessageListener = setupEmbedMessageListener();

  // 在组件卸载时清理消息监听器
  onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
    cleanupMessageListener();
  });
});

// 用户信息下拉菜单
const showUserInfoDropdown = ref(false);
const userInfoDropdownRef = ref<HTMLElement | null>(null);
const toggleUserInfoDropdown = () => {
  showUserInfoDropdown.value = !showUserInfoDropdown.value;
};

// 退出登录
const handleLogout = async () => {
  localStorage.removeItem('token');
  localStorage.removeItem('setup_status');
  localStorage.removeItem('console_token');
  localStorage.removeItem('refresh_token');

  router.push('/login');
};

// 跳转到系统管理-部门管理页面
const linkSettingDepartment = () => {
  showUserInfoDropdown.value = false;
  router.push('/setting/department');
};

// 语言和时区 弹窗
const showModalLanguage = ref(false);
const handleModalLanguage = () => {
  showUserInfoDropdown.value = false;
  showModalLanguage.value = !showModalLanguage.value;
};

// 账户信息 弹窗
const showModalUserinfo = ref(false);
const handleModalUserinfo = () => {
  showUserInfoDropdown.value = false;
  showModalUserinfo.value = !showModalUserinfo.value;
};

// 关于版本 弹窗
import { useVersionStore } from '@/store/version';
const versionStore = useVersionStore(); //全局状态管理

const showModalAbout = ref(false);
const current_version = ref('');
const handleModalAbout = () => {
  showUserInfoDropdown.value = false;
  showModalAbout.value = !showModalAbout.value;
};

// 帮助文档地址
import { useSysConfigsStore } from '@/store/sysConfigs';
const sysConfigsStore = useSysConfigsStore();
const sysConfigs = computed(() => sysConfigsStore.sysConfigsData);
const helpDocsUrl = ref('');

const handleHelpLink = () => {
  if (helpDocsUrl.value) {
    window.open(helpDocsUrl.value, '_blank');
  }
};

onMounted(async () => {
  await versionStore.fetchVersion();
  current_version.value = versionStore.versionsData.current_version;

  await sysConfigsStore.fetchSysConfigs();
  // 初始化帮助文档URL
  if (sysConfigs.value && sysConfigs.value.length > 0) {
    const helpDocs = sysConfigs.value.find(item => item.name === 'HELP_DOCS_URL');
    if (helpDocs) {
      helpDocsUrl.value = helpDocs.value;
    }
  }
});
</script>
