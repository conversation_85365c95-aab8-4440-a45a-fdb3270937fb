import{d as y,s as _,r as w,z as I,N as z,c as O,o as D,b,t as T,n as R,ad as Y}from"./pnpm-pnpm-B4aX-tnA.js";import{f as p}from"./ChatView-BF1kD0Xc.js";import{a as G}from"./index-C4ad4gme.js";const H={class:"text-[28px] font-zhongcu mb-[5px] text-[#00080E]"},j={class:"text-[16px] font-zhongcu text-[#00080E] mb-[20px]"},C=100,F=y({__name:"CenterBusinessAmountComponent",props:{data:{},colorMode:{}},setup(A){const h={red:["#f07c82","#EF483A","#c02c38"],blue:["#10aec2","#3B6DF3","#1661ab"],purple:["#8076a3","#7A40F2","#2e317c"],yellow:["#fed71a","#FFB200","#e8b004"],green:["#55bb8a","#41b349","#1a6840"],orange:["#f09c5a","#f97d1c","#de7622"]},m={SYSTEM_CYCLE:"SYSTEM_CYCLE",RANDOM:"RANDOM",SEQUENTIAL:"SEQUENTIAL",SYSTEM_SEQUENTIAL:"SYSTEM_SEQUENTIAL"},u=A,W=m.SYSTEM_CYCLE;let i=0,v=0,g=0,d=[];const M=()=>{switch(u.colorMode||W){case m.SYSTEM_CYCLE:{const e=Object.values(h),n=e[i],c=n[v];return i=(i+1)%e.length,i===0&&(v=(v+1)%n.length),c}case m.RANDOM:{const e=Object.values(h),n=e[Math.floor(Math.random()*e.length)];return n[Math.floor(Math.random()*n.length)]}case m.SEQUENTIAL:{const e=Object.values(h).flat(),n=e[g];return g=(g+1)%e.length,n}case m.SYSTEM_SEQUENTIAL:{if(!d.length){const n=Object.values(h);d=n[i],i=(i+1)%n.length}const e=d[0];return d=d.slice(1),e}default:return Object.values(h)[0][0]}},f=_(()=>{i=0,v=0,g=0,d=[];const a=o=>{const N="　";return o.length>12?o.slice(0,11)+"...":o.length<10?o+N.repeat(10-o.length):o},e=u.data.items.map(o=>({...o,value:parseFloat(o.value)||0,color:o.color||M(),name:a(o.name)}));if(e.length<=C)return e;const n=e.slice(0,C-1),t=e.slice(C-1).reduce((o,S)=>o+S.value,0),r={name:a("其他"),value:t,color:M()};return[...n,r]}),x=_(()=>{if(u.data.total_estimated_amount==0)return{value:0,unit:""};const{value:a,unit:e}=p(u.data.total_estimated_amount,!1);return{value:a,unit:e}}),l=_(()=>{const a=f.value.length,n=Math.max(...f.value.map(c=>c.name.length))*12+40;return a>8?{containerWidth:"w-[340px]",chartCenter:"35%",fontSize:14,legendWidth:0}:a>4?{containerWidth:`w-[${Math.max(630,n*2+100)}px]`,chartCenter:"30%",fontSize:16,legendWidth:n}:{containerWidth:`w-[${Math.max(430,n+50)}px]`,chartCenter:"30%",fontSize:16,legendWidth:n}}),E=w(null);let s=null;w(new Set);const L=()=>{if(!E.value)return;s||(s=Y(E.value));const a={...u.data,items:f.value},e=Object.fromEntries(a.items.map(t=>[t.name,!0])),n=()=>(console.log("selectedMap",e),console.log("currentData.items",a.items),a.items.filter(t=>e[t.name]).reduce((t,r)=>t+r.value,0)),c={legend:{orient:"vertical",show:!(f.value.length>8),right:"2%",top:"middle",itemWidth:8,itemHeight:8,icon:"circle",itemGap:20,height:"60%",width:l.value.legendWidth,textStyle:{fontSize:12,fontWeight:500,color:"#505D70",overflow:"break",width:l.value.legendWidth-20},formatter:t=>t,rich:{name:{fontSize:12,color:"#505D70",width:l.value.legendWidth*.6,overflow:"truncate"},value:{fontSize:12,color:"#505D70",align:"right",width:l.value.legendWidth*.4}},selected:e,columns:f.value.length>4?2:1},series:[{type:"pie",radius:["45%","65%"],center:[l.value.chartCenter,"50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:2,borderWidth:2,borderColor:"#ffffff"},label:{show:!1,position:"outside",formatter:function(t){const{value:r,unit:o}=p(t.value,!1);return r+o},fontSize:12,fontWeight:400,color:function(t){return"red"},distance:-5},labelLine:{show:!1},emphasis:{scale:!1,label:{show:!0}},data:a.items.map(t=>({name:t.name,value:t.value,label:{show:!0,formatter:function(r){if(f.value.length>8)return"";const{value:o,unit:S}=p(r.value,!1);return o+S},color:t.color,fontSize:13,padding:[0,-30]},itemStyle:{color:t.color}}))},{type:"pie",radius:["0%","58%"],center:[l.value.chartCenter,"50%"],label:{show:!0,position:"center",formatter:function(t){const{value:r,unit:o}=p(t.value,!1);return["{value|"+r+o+"}"].join(`
`)},rich:{value:{fontSize:l.value.fontSize,color:"#00080E",fontWeight:"bold",padding:[8,0,0,0],lineHeight:8}}},data:[{value:n(),name:"",itemStyle:{color:"rgba(255, 255, 255, 0.5)"}}]},{type:"pie",radius:["0%","20%"],center:[l.value.chartCenter,"50%"],label:{show:!0,position:"center",formatter:function(t){const{value:r,unit:o}=p(t.value,!1);return["{value|"+r+o+"}"].join(`
`)},rich:{value:{fontSize:l.value.fontSize,color:"#00080E",fontWeight:"normal",padding:[8,0,0,0],lineHeight:8}}},data:[{value:n(),name:"",itemStyle:{color:"transparent"}}]}]};s.setOption(c),s.on("legendselectchanged",function(t){Object.assign(e,t.selected),s==null||s.setOption({series:[{},{data:[{value:n(),name:"",itemStyle:{color:"rgba(255, 255, 255, 0.5)"}}]},{}]})})};return I(()=>{L(),window.addEventListener("resize",()=>{s==null||s.resize()})}),z(()=>u.data,()=>{L()},{deep:!0}),(a,e)=>(D(),O("div",{class:R(["rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px]",l.value.containerWidth])},[b("div",H,T(x.value.value||"0")+T(x.value.unit||"元"),1),b("div",j,T(a.data.title||"商机总金额"),1),b("div",{ref_key:"chartRef",ref:E,class:"w-full min-h-[200px]"},null,512)],2))}}),X=G(F,[["__scopeId","data-v-6b2fadff"]]);export{X as default};
