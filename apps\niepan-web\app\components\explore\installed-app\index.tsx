'use client';
import type { FC } from 'react';
import React, { useEffect } from 'react';
import { useContext } from 'use-context-selector';
import ExploreContext from '@/context/explore-context';
import TextGenerationApp from '@/app/components/share/text-generation';
import Loading from '@/app/components/base/loading';
import ChatWithHistory from '@/app/components/base/chat/chat-with-history';
import { fetchInstalledAppList } from '@/service/explore';

export type IInstalledAppProps = {
  id: string;
};

const InstalledAppComponent: FC<IInstalledAppProps> = ({ id }) => {
  const { installedApps, setInstalledApps } = useContext(ExploreContext);

  useEffect(() => {
    const fetchApps = async () => {
      try {
        const response = await fetchInstalledAppList();
        const installed_apps = response.installed_apps;
        setInstalledApps(installed_apps || []);
      } catch (error) {
        console.error('Failed to fetch installed apps:', error);
      }
    };
    if (installedApps.length === 0) fetchApps();
  }, [installedApps.length, setInstalledApps]);
  const installedApp = installedApps.find(item => item.id === id);
  if (!installedApp) {
    return (
      <div className="flex h-full items-center">
        <Loading type="area" />
      </div>
    );
  }

  return (
    <div className="h-full">
      {installedApp.app.mode !== 'completion' && installedApp.app.mode !== 'workflow' && (
        <ChatWithHistory installedAppInfo={installedApp} className="overflow-hidden" />
      )}
      {installedApp.app.mode === 'completion' && (
        <TextGenerationApp isInstalledApp installedAppInfo={installedApp} />
      )}
      {installedApp.app.mode === 'workflow' && (
        <TextGenerationApp isWorkflow isInstalledApp installedAppInfo={installedApp} />
      )}
    </div>
  );
};

export default React.memo(InstalledAppComponent);
