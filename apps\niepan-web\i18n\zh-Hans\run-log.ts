const translation = {
  input: '输入',
  result: '运行结果',
  detail: '结果详情',
  tracing: '追踪',
  resultPanel: {
    status: '状态',
    time: '运行时间',
    tokens: '总 token 数',
  },
  meta: {
    title: '元数据',
    status: '状态',
    version: '版本',
    executor: '执行人',
    startTime: '开始时间',
    time: '运行时间',
    tokens: '总 token 数',
    steps: '运行步数',
  },
  resultEmpty: {
    title: '本次运行仅输出 JSON 格式，',
    tipLeft: '请转到',
    link: '详细信息面板',
    tipRight: '查看它。',
  },
  actionLogs: 'Action 日志',
  circularInvocationTip: '当前工作流中存在工具/节点的循环调用。',
};

export default translation;
