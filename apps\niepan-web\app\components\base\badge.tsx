import type { ReactNode } from 'react';
import { memo } from 'react';
import cn from '@/utils/classnames';

type BadgeProps = {
  className?: string;
  text?: ReactNode;
  children?: ReactNode;
  uppercase?: boolean;
  hasRedCornerMark?: boolean;
};

const Badge = ({ className, text, children, uppercase = true, hasRedCornerMark }: BadgeProps) => {
  return (
    <div
      className={cn(
        'relative inline-flex h-4 min-w-4 items-center justify-center rounded-[2px] border-[2px] border-divider-deep text-[10px] font-medium leading-[9px] text-text-quaternary',
        uppercase ? '' : '',
        // uppercase ? 'system-2xs-medium-uppercase' : 'system-xs-medium',
        className
      )}
    >
      {hasRedCornerMark && (
        <div className="absolute right-[-2px] top-[-2px] h-1.5 w-1.5 rounded-[2px] border border-components-badge-status-light-error-border-inner bg-components-badge-status-light-error-bg shadow-sm"></div>
      )}
      {children || text}
    </div>
  );
};

export default memo(Badge);
