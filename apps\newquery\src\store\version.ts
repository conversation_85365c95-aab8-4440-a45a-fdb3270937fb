import { defineStore } from 'pinia';
import { fetchGetVersion } from '@/services/console-api';

export const useVersionStore = defineStore('version', {
  state: () => ({ versionsData: {} }),
  actions: {
    async fetchVersion() {
      try {
        const res = await fetchGetVersion();
        if (res.data) {
          this.versionsData = res.data;
        }
      } catch (error) {
        console.error('获取版本信息失败:', error);
      }
    },
  },
});
