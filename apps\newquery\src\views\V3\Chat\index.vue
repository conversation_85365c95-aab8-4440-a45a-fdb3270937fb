<template>
  <div class="w-[100vw] h-[100vh] chat-container overflow-hidden">
    <ChangeAppNav @goBackClick="handleGoBackClick" />
    <iframe
      :src="embedUrl + '/' + route.params.appId"
      class="w-[100vw] h-[calc(100vh-55px)] border-0"
      frameborder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen
    ></iframe>
    <!-- :id="route.params.appId" -->
  </div>
</template>
<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChangeAppNav from './components/ChangeAppNav.vue';
import { useMenuV3Store } from '@/store/modules/menuV3';
import { useConfig } from '@/hooks/useConfig';

const route = useRoute(); // 获取路由参数
console.log(route.params, 'route.params');
console.log(route.params.appId, 'route.params.appId');
// 返回广场
const router = useRouter();
const handleGoBackClick = () => {
  router.push(`/index`);
};

const menuStore = useMenuV3Store();
const { globalConfig } = useConfig();
const embedUrl = ref('');

onMounted(() => {
  // 从全局配置中获取嵌入URL
  if (globalConfig.workspaceSettings && globalConfig.workspaceSettings.installedApp) {
    embedUrl.value = globalConfig.workspaceSettings.installedApp;
  } else {
    console.warn('模型供应商嵌入URL未配置，请检查config.js文件');
    embedUrl.value = 'http://localhost:3000/embed/installed-app'; // 默认URL
  }
  console.log('embedUrl:', embedUrl.value);
  console.log('Final URL:', embedUrl.value + '/' + route.params.appId);
});
</script>
<style lang="css" scoped>
.chat-container {
  background: linear-gradient(129deg, #f5fbff 11.03%, #fcfcfc 88.98%);
}
</style>
