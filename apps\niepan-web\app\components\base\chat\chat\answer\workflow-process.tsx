import { useEffect, useState } from 'react';
import { RiArrowRightSLine, RiErrorWarningFill } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import type { ChatItem, WorkflowProcess } from '../../types';
import TracingPanel from '@/app/components/workflow/run/tracing-panel';
import cn from '@/utils/classnames';
import { CheckCircle } from '@/app/components/base/icons/src/vender/solid/general';
import { WorkflowRunningStatus } from '@/app/components/workflow/types';

type WorkflowProcessProps = {
  data: WorkflowProcess;
  item?: ChatItem;
  expand?: boolean;
  hideInfo?: boolean;
  hideProcessDetail?: boolean;
  readonly?: boolean;
};
const WorkflowProcessItem = ({
  data,
  expand = false,
  hideInfo = false,
  hideProcessDetail = false,
  readonly = false,
}: WorkflowProcessProps) => {
  const { t } = useTranslation();
  const [collapse, setCollapse] = useState(!expand);
  const running = data.status === WorkflowRunningStatus.Running;
  const succeeded = data.status === WorkflowRunningStatus.Succeeded;
  const failed =
    data.status === WorkflowRunningStatus.Failed || data.status === WorkflowRunningStatus.Stopped;

  useEffect(() => {
    setCollapse(!expand);
  }, [expand]);

  return (
    <div
      className={cn(
        'min-h-8 rounded-2xl px-3 py-2.5',
        running && !collapse && 'bg-background-section-burn',
        succeeded && !collapse && 'bg-[#EBFBE9]',
        failed && !collapse && 'bg-state-destructive-hover',
        collapse && 'bg-workflow-process-bg'
      )}
    >
      <div
        className={cn(
          'flex cursor-pointer items-center',
          !collapse && 'px-1.5',
          readonly && 'cursor-default'
        )}
        onClick={() => !readonly && setCollapse(!collapse)}
      >
        {running && (
          // <RiLoader2Line className="h-4.5 w-4.5 mr-1 shrink-0 animate-spin text-text-tertiary" />
          <img
            src="/apps/loading.gif"
            className="mr-1 h-[18px] w-[18px] shrink-0 animate-spin text-text-tertiary"
          />
        )}
        {succeeded && (
          <CheckCircle className="h-4.5 w-4.5 mr-1 shrink-0 text-util-colors-green-green-400" />
        )}
        {failed && (
          <RiErrorWarningFill className="h-4.5 w-4.5 mr-1 shrink-0 text-text-destructive" />
        )}
        <div className={cn('system-xs-medium text-sm text-text-secondary', !collapse && 'grow')}>
          {t('workflow.common.workflowProcess')}
        </div>
        {!readonly && (
          <RiArrowRightSLine
            className={cn('ml-1 h-4 w-4 text-text-tertiary', !collapse && 'rotate-90')}
          />
        )}
      </div>
      {!collapse && !readonly && (
        <div className="mt-1.5">
          {
            <TracingPanel
              list={data.tracing}
              hideNodeInfo={hideInfo}
              hideNodeProcessDetail={hideProcessDetail}
            />
          }
        </div>
      )}
    </div>
  );
};

export default WorkflowProcessItem;
