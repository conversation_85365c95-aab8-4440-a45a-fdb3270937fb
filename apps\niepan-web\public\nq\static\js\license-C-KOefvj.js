import{w as U,d as j,aN as z,aO as R,aP as O,a as Q}from"./index-C4ad4gme.js";import{v as q}from"./v3Confirms-DNgkozfF.js";import{d as G,r as u,s as H,z as J,e as B,c as y,o as f,b as t,a as w,f as I,p as v,t as h,m as C,A as S,E as c,Q as E,x as K,y as W}from"./pnpm-pnpm-B4aX-tnA.js";const X=async(_,r,l)=>{const e=_.replace(/([\n\r])+/g,`
`);try{return navigator.clipboard&&navigator.clipboard.writeText?(await navigator.clipboard.writeText(e),r&&typeof r=="function"&&r(),!0):T(e,r,l)}catch(d){return console.error("复制失败:",d),T(e,r,l)}};function T(_,r,l){try{const e=document.createElement("textarea");e.value=_,e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.width="1px",e.style.height="1px",e.style.padding="0",e.style.border="none",e.style.outline="none",e.style.boxShadow="none",e.style.background="transparent",document.body.appendChild(e),e.focus(),e.select();const d=document.execCommand("copy");return document.body.removeChild(e),d?(r&&typeof r=="function"&&r(),!0):(l&&typeof l=="function"&&l(new Error("document.execCommand失败")),!1)}catch(e){return console.error("回退复制方法失败:",e),l&&typeof l=="function"&&l(e),!1}}const p=_=>(K("data-v-f6237f29"),_=_(),W(),_),Y={class:"bg-white rounded-[20px] overflow-hidden h-[calc(100vh-120px)] relative z-50"},Z={class:"mx-auto max-w-[800px] px-3 py-3"},ee=p(()=>t("div",{class:"text-[20px] font-medium text-base px-3 py-4"},"管理您的企业授权",-1)),te={key:0,class:"bg-white rounded-[12px] p-3 mb-6 text-sm"},se={key:0,class:"bg-[#E6F7E6] text-[#343A3F] text-sm rounded-[8px] p-3 mb-4"},oe=p(()=>t("strong",null,"您的IntelliSight企业授权已激活！",-1)),ae={key:1,class:"bg-[#FEDDDE] text-[#343A3F] rounded-[8px] p-3 mb-4"},le={class:"text-[#FF4D4F]"},ne={key:2,class:"bg-[#FEDDDE] text-[#343A3F] rounded-[8px] p-3 mb-4"},ie=p(()=>t("strong",null,"您的IntelliSight 企业授权已过期！",-1)),ce={class:"bg-[#F9FAFB] rounded-[8px] p-5"},re={class:"grid grid-cols-2 gap-4"},de={class:"flex flex-row items-center"},ue=p(()=>t("div",{class:"text-[#6B7280] text-[14px] mr-2 w-[100px]"},"授权给：",-1)),ve={class:"text-[#111827] text-[14px]"},pe={class:"flex flex-row items-center"},xe=p(()=>t("div",{class:"text-[#6B7280] text-[14px] mr-2 w-[100px]"},"签发时间：",-1)),fe={class:"text-[#111827] text-[14px]"},_e={class:"flex flex-row items-center"},ge=p(()=>t("div",{class:"text-[#6B7280] text-[14px] mr-2 w-[100px]"},"有效期至：",-1)),me={class:"text-[#111827] text-[14px]"},ye={class:"flex flex-row items-center"},he=p(()=>t("div",{class:"text-[#6B7280] text-[14px] mr-2 w-[100px]"},"支持用户数：",-1)),be={class:"text-[#111827] text-[14px]"},we={class:"flex justify-between mt-8"},Ie={class:"flex gap-3"},Ce={class:"flex gap-3"},De={key:1,class:"bg-white rounded-[12px] p-6"},ke=p(()=>t("div",{class:"text-[14px] font-medium mb-4"},"请输入您的License授权码",-1)),Fe={class:"mb-4"},Ae=p(()=>t("div",{class:"text-[13px] text-[#697077] mb-6"}," 激活后企业用户便可在有效期内正常使用IntelliSight平台功能 ",-1)),Ee={class:"flex justify-between"},Le=p(()=>t("div",{class:"flex gap-3"},null,-1)),Be={class:"flex gap-3"},Se={key:2,class:"fixed top-0 left-0 w-full bg-[#FFF2F0] text-[#FF4D4F] p-3 text-center"},Te=G({__name:"license",setup(_){const r=U(),l=j(),e=u("loading"),d=u({company:"",issueDate:"",expiryDate:"",userCount:"",licenseCode:"",id:null,expiryDays:0}),g=u(""),m=u(!1),D=u(!1),k=u(!1),F=u(!1),M=u(""),b=u(!1),L=u(!1),n=H(()=>{var x,o;return(o=(x=l.userInfo)==null?void 0:x.tenant)==null?void 0:o.id}),A=async()=>{var o,i,a;if(!n.value){console.error("获取租户信息失败，tenantId为空"),c.error("获取租户信息失败"),e.value="none",m.value=!0;return}console.log("开始获取license信息，租户ID:",n.value),b.value=!0;const x=E.service({lock:!0,text:"加载授权信息中...",background:"rgba(255, 255, 255, 0.7)"});try{console.log("请求license API:",`/tenants/${n.value}/license`);const s=await z(n.value);console.log("License API 响应:",s),s&&s.data?(console.log("获取到license数据:",s.data),d.value={company:s.data.company||"",issueDate:s.data.issue_time||"",expiryDate:s.data.expired_date||"",userCount:((o=s.data.max_users)==null?void 0:o.toString())||"0",licenseCode:s.data.license_value,id:s.data.id,expiryDays:s.data.expiry_days||0},s.data.id?(s.data.expiry_days<0?e.value="expired":s.data.expiry_days<=30?e.value="expiring":e.value="active",console.log("设置license状态为:",e.value)):(e.value="none",m.value=!0,console.log("未获取到license ID，设置状态为none"))):(e.value="none",m.value=!0,console.log("API响应中没有data字段，设置状态为none"),c.warning("未获取到许可证信息"))}catch(s){c.error(((a=(i=s.response)==null?void 0:i.data)==null?void 0:a.message)||"获取许可证信息失败")}finally{x.close(),b.value=!1,console.log("license信息获取完成，当前状态:",e.value)}},V=()=>{g.value=""},N=()=>{X(d.value.licenseCode,()=>{c.success("授权码已复制到剪贴板")},()=>{c.error("复制失败，请手动复制")})},P=async()=>{var o,i;if(!n.value){console.error("激活license失败：租户ID为空"),c.error("获取租户信息失败");return}if(!g.value){c.error("请输入有效的授权码"),setTimeout(()=>{F.value=!1},3e3);return}console.log("开始激活license，租户ID:",n.value),console.log("授权码:",g.value),b.value=!0;const x=E.service({lock:!0,text:"激活许可证中...",background:"rgba(255, 255, 255, 0.7)"});try{console.log("请求激活license API:",`/tenants/${n.value}/license/activate`);const a=await R(n.value,g.value);console.log("激活许可证响应:",a),a&&a.data?(console.log("激活成功，响应数据:",a.data),await A(),m.value=!1,D.value=!1,L.value=!1,g.value="",c.success("许可证激活成功")):(console.error("激活失败：响应中没有data字段"),c.error("激活失败:"+a.data.message),setTimeout(()=>{F.value=!1},3e3))}catch(a){console.error("激活许可证失败:",a),console.error("错误详情:",a.response||a.message||a),c.error(((i=(o=a.response)==null?void 0:o.data)==null?void 0:i.message)||"激活失败")}finally{x.close(),b.value=!1,console.log("license激活操作完成")}},$=async()=>{var o,i;if(!n.value){console.error("移除license失败：租户ID为空"),c.error("获取租户信息失败");return}console.log("开始移除license，租户ID:",n.value),b.value=!0;const x=E.service({lock:!0,text:"移除许可证中...",background:"rgba(255, 255, 255, 0.7)"});try{console.log("请求移除license API:",`/tenants/${n.value}/license`),await O(n.value),e.value="none",k.value=!1,m.value=!0,c.success("许可证已移除"),console.log("license已成功移除"),await A()}catch(a){c.error(((i=(o=a.response)==null?void 0:o.data)==null?void 0:i.message)||"移除许可证失败")}finally{x.close(),b.value=!1,console.log("license移除操作完成")}};return J(async()=>{r.setActiveMenu("license"),l.userInfo||await l.fetchUserInfo(null),console.log("用户信息:",l.userInfo),console.log("租户ID:",n.value),n.value?await A():(console.error("无法获取租户ID，无法加载license信息"),e.value="none",m.value=!0)}),(x,o)=>{const i=B("el-button"),a=B("el-input");return f(),y("div",Y,[t("div",Z,[ee,e.value!=="none"&&!D.value&&!m.value?(f(),y("div",te,[e.value==="active"?(f(),y("div",se,[oe,v("企业内用户可正在有效期内正常使用IntelliSight 平台功能 ")])):e.value==="expiring"?(f(),y("div",ae,[t("strong",null,[v("您的IntelliSight 企业授权将在"),t("span",le,h(d.value.expiryDays),1),v("天后到期！")]),v("请联系AiAlign相关人申请新的许可证 ")])):e.value==="expired"?(f(),y("div",ne,[ie,v("请联系AiAlign相关人申请新的许可证 ")])):w("",!0),t("div",ce,[t("div",re,[t("div",de,[ue,t("div",ve,h(d.value.company),1)]),t("div",pe,[xe,t("div",fe,h(d.value.issueDate),1)]),t("div",_e,[ge,t("div",me,h(d.value.expiryDate),1)]),t("div",ye,[he,t("div",be,h(d.value.userCount),1)])])]),t("div",we,[t("div",Ie,[I(i,{onClick:o[0]||(o[0]=s=>{D.value=!0,L.value=!0}),class:"border border-[#129BFE] text-[#129BFE] rounded-[6px]",style:{border:"1px solid #129bfe",color:"#129bfe"}},{default:C(()=>[v(h(e.value==="active"||e.value==="expired"?"激活新许可证":"激活许可证"),1)]),_:1})]),t("div",Ce,[e.value==="active"||e.value==="expiring"||e.value=="expired"?(f(),S(i,{key:0,onClick:N,class:"border border-[#E5E7EB] text-[#374151] rounded-[6px]"},{default:C(()=>[v("复制授权码")]),_:1})):w("",!0),e.value==="active"||e.value==="expiring"||e.value=="expired"?(f(),S(i,{key:1,onClick:o[1]||(o[1]=s=>k.value=!0),class:"border border-[#E5E7EB] text-[#374151] rounded-[6px]"},{default:C(()=>[v("移除许可证")]),_:1})):w("",!0)])])])):w("",!0),e.value==="none"||m.value||D.value?(f(),y("div",De,[ke,t("div",Fe,[I(a,{modelValue:g.value,"onUpdate:modelValue":o[2]||(o[2]=s=>g.value=s),type:"textarea",rows:6,placeholder:"输入您的License授权码",class:"w-full"},null,8,["modelValue"])]),Ae,t("div",Ee,[Le,t("div",Be,[I(i,{onClick:V,class:"rounded-[6px]"},{default:C(()=>[v("清空")]),_:1}),I(i,{type:"primary",class:"bg-[#129BFE] rounded-[6px]",style:{"background-color":"#129bfe"},onClick:P,disabled:!g.value},{default:C(()=>[v("激活")]),_:1},8,["disabled"])])])])):w("",!0),F.value?(f(),y("div",Se,h(M.value||"激活失败"),1)):w("",!0),I(q,{show:k.value,title:"确定移除企业授权吗？",message:"移除后企业用户将失去使用IntelliSight平台的完整权限",confirmText:"移除",onClose:o[3]||(o[3]=s=>k.value=!1),onConfirm:$},null,8,["show"])])])}}}),Pe=Q(Te,[["__scopeId","data-v-f6237f29"]]);export{Pe as default};
