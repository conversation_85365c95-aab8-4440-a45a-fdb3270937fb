const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/CustomPagination-BZ_cbN8c.js","static/js/pnpm-pnpm-B4aX-tnA.js","static/css/pnpm-pnpm-BdGb95pV.css","static/js/index-C4ad4gme.js","static/css/index-CKcViCi5.css","static/css/CustomPagination-DcylDgxW.css","static/js/UserFormDialog-BKPSJ82w.js","static/css/UserFormDialog-DVuDpZdE.css"])))=>i.map(i=>d[i]);
import{w as ge,d as ye,aE as be,aI as ke,c as Q,aJ as Ie,aK as Ae,aL as Ce,aM as Se,a as Ve}from"./index-C4ad4gme.js";import{d as Ue,r as f,z as De,Q as N,E as u,s as W,e as g,c as w,o as _,b as a,f as o,m as i,n as X,a as T,t as v,aa as b,a_ as Te,ah as G,p as H,a1 as Fe,A as Y,J as k,K as R,aV as Pe,aW as ze,aX as Ee,aF as Z,x as Ke,y as Me}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as $e}from"./MoreIcon.vue_vue_type_script_setup_true_lang-DK105Kpz.js";import{_ as Le}from"./EditIcon.vue_vue_type_script_setup_true_lang-B09bwPmi.js";const I=F=>(Ke("data-v-c07cb6d9"),F=F(),Me(),F),Ne={class:"rounded-[20px] flex"},Re={class:"w-[184px] mr-4 flex flex-col bg-white"},Be={class:"bg-[#fff] rounded-[12px] p-4"},Oe={class:"relative"},Je=I(()=>a("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[a("circle",{cx:"11",cy:"11",r:"8"}),a("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})],-1)),je={class:"bg-white rounded-[12px] flex-1 p-4 pt-0 overflow-auto",style:{"max-height":"calc(100vh - 180px)","overflow-x":"auto","overflow-y":"auto"}},qe=I(()=>a("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[a("line",{x1:"8",y1:"6",x2:"21",y2:"6"}),a("line",{x1:"8",y1:"12",x2:"21",y2:"12"}),a("line",{x1:"8",y1:"18",x2:"21",y2:"18"}),a("line",{x1:"3",y1:"6",x2:"3.01",y2:"6"}),a("line",{x1:"3",y1:"12",x2:"3.01",y2:"12"}),a("line",{x1:"3",y1:"18",x2:"3.01",y2:"18"})],-1)),Qe=I(()=>a("span",null,"全部",-1)),We={class:"flex items-center w-full pr-[10px]"},Xe={class:"flex items-center whitespace-nowrap"},Ge={key:0,class:"text-gray-400 text-xs ml-1"},He={class:"flex-1 overflow-hidden"},Ye={class:"flex justify-between mb-4"},Ze={class:"search-box flex items-center"},et=I(()=>a("div",{class:"whitespace-nowrap text-[14px] leading-[18px] font-medium mr-[10px]"},"姓名",-1)),tt=I(()=>a("div",{class:"whitespace-nowrap text-[14px] leading-[18px] font-medium mr-[10px]"},"邮箱",-1)),at=I(()=>a("div",{class:"whitespace-nowrap text-[14px] leading-[18px] font-medium mr-[10px]"},"状态",-1)),nt={class:"rounded-xl h-[calc(100vh-210px)] bg-white overflow-hidden flex flex-col"},lt=["title"],st=["title"],ot={class:"flex items-center"},rt={class:"flex items-center overflow-hidden"},it=["title"],dt={key:0,class:"mr-1 text-[#343A3F]"},ct={class:"ml-1 cursor-pointer px-1 rounded text-xs font-medium",style:{background:"#f5f5f5",color:"#697077","line-height":"16px","border-radius":"4px","font-size":"12px","font-weight":"500"}},ut={class:"flex items-center"},mt={class:"flex items-center overflow-hidden"},pt=["title"],ft={key:0,class:"mr-1 text-[#343A3F]"},_t={class:"ml-1 cursor-pointer px-1 rounded text-xs font-medium",style:{background:"#f5f5f5",color:"#697077","line-height":"16px","border-radius":"4px","font-size":"12px","font-weight":"500"}},ht=["title"],vt={class:"flex items-center"},wt=["title"],xt={class:"flex items-center"},gt={class:"cursor-pointer p-2 hover:bg-[#f1f2f2] rounded-md"},yt={class:"flex items-center text-[#343A3F]"},bt=I(()=>a("span",null,"编辑",-1)),kt=Ue({__name:"user",setup(F){const ee=Z(()=>Q(()=>import("./CustomPagination-BZ_cbN8c.js"),__vite__mapDeps([0,1,2,3,4,5]))),te=Z(()=>Q(()=>import("./UserFormDialog-BKPSJ82w.js"),__vite__mapDeps([6,1,2,3,4,7]))),P=f(),ae=ge(),h=ye();De(async()=>{ae.setActiveMenu("user"),await ne(),await U(),le()});const A=f(),C=f(""),S=f("all"),ne=async()=>{var e,l;const t=N.service({lock:!0,text:"加载部门数据中...",background:"rgba(255, 255, 255, 0.7)"});try{h.userInfo||await h.fetchUserInfo(null);const s=(l=(e=h.userInfo)==null?void 0:e.tenant)==null?void 0:l.id;if(!s){u.error("获取租户信息失败");return}const d=await be(s);d&&d.data?z.value=O(d.data):u.warning("未获取到部门数据")}catch(s){console.error("获取部门数据失败:",s),u.error("获取部门数据失败")}finally{t.close()}},O=t=>!t||!Array.isArray(t)?[]:t.map(e=>({id:e.id,name:e.name,memberCount:e.member_count||0,manager:e.members&&e.members.length>0?e.members[0].name:"",createTime:e.create_time||new Date().toISOString().split("T")[0],uuid:e.uuid,sequence:e.sequence,update_time:e.update_time,children:e.children&&e.children.length>0?O(e.children):[]})),z=f([]),B=f([]),le=()=>{B.value=[]},se=W(()=>{if(!C.value)return z.value;const t=(e,l)=>e.filter(s=>{const d=s.name.toLowerCase().includes(l.toLowerCase());let p=[];return s.children&&s.children.length>0&&(p=t(s.children,l)),p.length>0?(s.children=p,!0):d});return t(JSON.parse(JSON.stringify(z.value)),C.value)}),oe=async t=>{console.log("点击部门:",t.name,"部门ID:",t.id),S.value=t.id,await D()},re=async t=>{S.value=t,A.value&&A.value.setCurrentKey(null),await D()},ie=()=>{var t,e;C.value?(t=A.value)==null||t.expandAll():((e=A.value)==null||e.collapseAll(),B.value.forEach(l=>{var s;(s=A.value)==null||s.expandByKeys([l])}))},E=f(""),K=f(""),M=f(""),V=f(1),$=f(15),de=f([15,25,50]),J=f(0),ce=f([{id:1,name:"知识管理员"},{id:2,name:"模型管理员"},{id:3,name:"超级管理员"},{id:4,name:"普通用户"},{id:5,name:"访客"}]),j=f([]),U=async()=>{var e,l;const t=N.service({lock:!0,text:"加载用户数据中...",background:"rgba(255, 255, 255, 0.7)"});try{h.userInfo||await h.fetchUserInfo(null);const s=(l=(e=h.userInfo)==null?void 0:e.tenant)==null?void 0:l.id;if(!s){u.error("获取租户信息失败");return}const d={page:V.value,limit:$.value};S.value!=="all"&&(d.department_id=S.value),E.value&&(d.name=E.value),K.value&&(d.email=K.value),M.value&&(d.enabled=M.value);const p=await ke(s,d);console.log("用户列表API响应:",p),p&&p.data?(j.value=ue(p.data.data||[]),J.value=p.data.total_num||0):u.warning("未获取到用户数据")}catch(s){console.error("获取用户数据失败:",s),u.error("获取用户数据失败")}finally{t.close()}},ue=t=>!t||!Array.isArray(t)?[]:t.map(e=>({id:e.id,userId:e.user_id,name:e.name||"",email:e.email||"",phone:e.cellphone||"",enabled:e.enabled||!1,createTime:e.create_time?e.create_time.split(" ")[0]:"-",departments:e.departments&&Array.isArray(e.departments)?e.departments.map(l=>({id:l.id,name:l.name,uuid:l.uuid})):[],roles:e.roles&&Array.isArray(e.roles)?e.roles.map(l=>({id:l.id,name:l.name,type:l.type})):[],departmentId:e.departments&&e.departments.length>0?e.departments[0].id:null,updateTime:e.update_time||"-",isPasswordSet:e.is_password_set})),me=W(()=>j.value),D=async()=>{V.value=1,await U()},pe=async({page:t,pageSize:e})=>{V.value=t,$.value=e,await U()},q=async()=>{var t,e;try{h.userInfo||await h.fetchUserInfo(null);const l=(e=(t=h.userInfo)==null?void 0:t.tenant)==null?void 0:e.id;if(!l)return u.error("获取租户信息失败"),[];const s=await Ae(l,{limit:100});return console.log("角色列表API响应:",s),s&&s.data&&s.data.data?s.data.data.map(d=>({id:d.id,name:d.name,type:d.type})):(u.warning("未获取到角色数据"),[])}catch(l){return console.error("获取角色数据失败:",l),u.error("获取角色数据失败"),[]}},fe=async()=>{var e;const t=await q();(e=P.value)==null||e.openAddDialog(t)},_e=async t=>{var l;const e=await q();(l=P.value)==null||l.openEditDialog(t,e)},he=async(t,e)=>{var d,p;const l=N.service({lock:!0,text:"更新用户状态中...",background:"rgba(255, 255, 255, 0.7)"}),s=(p=(d=h.userInfo)==null?void 0:d.tenant)==null?void 0:p.id;if(!s){u.error("获取租户信息失败");return}console.log(t,e,"----数据");try{const c=await Ie(s,t.id,e);if(console.log("更新用户状态API响应:",c),c&&c.data&&c.data.id&&c.data.enabled===e){const m=e?"启用":"禁用";u.success(`已${m}用户: ${t.name}`),await U()}else t.enabled=!e,u.error("更新用户状态失败")}catch(c){console.error("更新用户状态失败:",c),t.enabled=!e,u.error("更新用户状态失败")}finally{l.close()}},ve=async(t,e)=>{var s,d,p;const l=N.service({lock:!0,text:e?"更新用户中...":"添加用户中...",background:"rgba(255, 255, 255, 0.7)"});try{h.userInfo||await h.fetchUserInfo(null);const c=(d=(s=h.userInfo)==null?void 0:s.tenant)==null?void 0:d.id;if(!c){u.error("获取租户信息失败");return}const m={tenant_id:c,email:t.email,name:t.name,cellphone:t.phone,department_ids:t.departmentIds,role_ids:t.roleIds,status:"enabled"};t.password&&(m.password=t.password);let x;if(e)if(t.id)m.user_id=t.userId,x=await Ce(t.id,m);else{u.error("用户ID不存在，无法更新");return}else x=await Se(m);if(console.log(e?"更新用户API响应:":"添加用户API响应:",x),x&&x.data)if(x.data.error){const L=x.data.message||"操作失败";u.error(L)}else u.success(e?`已更新用户: ${t.name}`:`已添加用户: ${t.name}`),await U(),(p=P.value)==null||p.closeDialog();else u.error(e?"更新用户失败":"添加用户失败")}catch(c){console.error(e?"更新用户失败:":"添加用户失败:",c);let m=e?"更新用户失败":"添加用户失败";c.response&&c.response.data&&c.response.data.message&&(m=c.response.data.message),u.error(m)}finally{l.close()}};return(t,e)=>{const l=g("el-icon"),s=g("el-input"),d=g("el-option"),p=g("el-select"),c=g("el-button"),m=g("el-table-column"),x=g("el-tooltip"),L=g("el-popover"),we=g("el-switch"),xe=g("el-table");return _(),w(k,null,[a("div",Ne,[a("div",Re,[a("div",Be,[a("div",Oe,[o(s,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=n=>C.value=n),placeholder:"请输入部门名称",class:"role-input !text-14px",clearable:"",onInput:ie},{suffix:i(()=>[o(l,{class:"cursor-pointer"},{default:i(()=>[Je]),_:1})]),_:1},8,["modelValue"])])]),a("div",je,[a("div",{class:X(["flex items-center p-2 mb-2 rounded-md cursor-pointer mr-2 text-[12px]",S.value==="all"?"bg-[#e6f4ff] text-[#129bfe]":"hover:bg-[#e6e7e7]"]),onClick:e[1]||(e[1]=n=>re("all"))},[o(l,{class:"mr-2"},{default:i(()=>[qe]),_:1}),Qe],2),o(b(Te),{ref_key:"deptTreeRef",ref:A,data:se.value,props:{label:"name",children:"children"},"node-key":"id","default-expanded-keys":B.value,"highlight-current":"",indent:24,onNodeClick:oe},{default:i(({node:n,data:r})=>[a("div",We,[a("div",Xe,[a("span",null,v(n.label),1),r.memberCount?(_(),w("span",Ge," ("+v(r.memberCount)+") ",1)):T("",!0)])])]),_:1},8,["data","default-expanded-keys"])])]),a("div",He,[a("div",Ye,[a("div",Ze,[et,o(s,{modelValue:E.value,"onUpdate:modelValue":e[2]||(e[2]=n=>E.value=n),placeholder:"请输入姓名",class:"mr-[10px] w-[180px] role-input2",clearable:"",onKeyup:G(D,["enter"])},null,8,["modelValue"]),tt,o(s,{modelValue:K.value,"onUpdate:modelValue":e[3]||(e[3]=n=>K.value=n),placeholder:"请输入邮箱",class:"mr-[10px] w-[180px] role-input2",clearable:"",onKeyup:G(D,["enter"])},null,8,["modelValue"]),at,o(p,{modelValue:M.value,"onUpdate:modelValue":e[4]||(e[4]=n=>M.value=n),placeholder:"请选择",class:"mr-[10px] w-[120px] role-input2",clearable:""},{default:i(()=>[o(d,{label:"启用",value:"enabled"}),o(d,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"]),o(c,{type:"primary",class:"filter-btn rounded-md h-8 min-w-[60px] px-4 text-sm",onClick:D},{default:i(()=>[H("筛选")]),_:1})]),o(c,{type:"primary",class:"add-user-btn",onClick:fe},{default:i(()=>[o(l,{class:"mr-1"},{default:i(()=>[o(b(Fe))]),_:1}),H("添加用户 ")]),_:1})]),a("div",nt,[o(xe,{data:me.value,style:{width:"100%"},"row-key":"id","table-layout":"fixed"},{default:i(()=>[o(m,{prop:"name",label:"姓名",width:"*"},{default:i(n=>[a("span",{title:n.row.name,class:"truncate block"},v(n.row.name),9,lt)]),_:1}),o(m,{prop:"email",label:"邮箱",width:"180"},{default:i(n=>[a("span",{title:n.row.email,class:"truncate block"},v(n.row.email),9,st)]),_:1}),o(m,{label:"部门",width:"160"},{default:i(n=>[a("div",ot,[a("div",rt,[(_(!0),w(k,null,R(n.row.departments.slice(0,2),(r,y)=>(_(),w(k,{key:y},[o(x,{content:r.name,placement:"top",disabled:r.name.length<=8},{default:i(()=>[a("span",{class:"mr-1 max-w-[70px] truncate inline-block text-[#343A3F]",title:r.name},v(r.name),9,it)]),_:2},1032,["content","disabled"]),y<n.row.departments.slice(0,2).length-1?(_(),w("span",dt)):T("",!0)],64))),128))]),n.row.departments.length>2?(_(),Y(L,{key:0,placement:"bottom",width:200,trigger:"hover"},{reference:i(()=>[a("span",ct,"+"+v(n.row.departments.length-2),1)]),default:i(()=>[a("div",null,[(_(!0),w(k,null,R(n.row.departments.slice(2),(r,y)=>(_(),w("div",{key:y,class:"py-1"},v(r.name),1))),128))])]),_:2},1024)):T("",!0)])]),_:1}),o(m,{label:"角色",width:"160"},{default:i(n=>[a("div",ut,[a("div",mt,[(_(!0),w(k,null,R((n.row.roles||[]).slice(0,2),(r,y)=>(_(),w(k,{key:y},[o(x,{content:(r==null?void 0:r.name)||"",placement:"top",disabled:((r==null?void 0:r.name)||"").length<=8},{default:i(()=>[a("span",{class:"mr-1 max-w-[70px] truncate inline-block text-[#343A3F]",title:(r==null?void 0:r.name)||""},v((r==null?void 0:r.name)||""),9,pt)]),_:2},1032,["content","disabled"]),y<(n.row.roles||[]).slice(0,2).length-1?(_(),w("span",ft)):T("",!0)],64))),128))]),(n.row.roles||[]).length>2?(_(),Y(L,{key:0,placement:"bottom",width:200,trigger:"hover"},{reference:i(()=>[a("span",_t,"+"+v((n.row.roles||[]).length-2),1)]),default:i(()=>[a("div",null,[(_(!0),w(k,null,R((n.row.roles||[]).slice(2),(r,y)=>(_(),w("div",{key:y,class:"py-1"},v((r==null?void 0:r.name)||""),1))),128))])]),_:2},1024)):T("",!0)])]),_:1}),o(m,{prop:"phone",label:"手机号码",width:"120"},{default:i(n=>[a("span",{title:n.row.phone,class:"truncate block"},v(n.row.phone),9,ht)]),_:1}),o(m,{label:"启用状态",width:"100"},{default:i(n=>[a("div",vt,[a("div",{class:X(["w-2 h-2 rounded-full mr-1",n.row.enabled?"bg-green-500":"bg-gray-400"])},null,2),a("span",null,v(n.row.enabled?"已启用":"已禁用"),1)])]),_:1}),o(m,{prop:"createTime",label:"加入时间",width:"120"},{default:i(n=>[a("span",{title:n.row.createTime,class:"truncate block"},v(n.row.createTime),9,wt)]),_:1}),o(m,{label:"操作",width:"100",fixed:"right"},{default:i(n=>[a("div",xt,[o(we,{modelValue:n.row.enabled,"onUpdate:modelValue":r=>n.row.enabled=r,onClick:r=>he(n.row,n.row.enabled),class:"mr-2"},null,8,["modelValue","onUpdate:modelValue","onClick"]),o(b(Pe),{trigger:"click"},{dropdown:i(()=>[o(b(ze),null,{default:i(()=>[o(b(Ee),{onClick:r=>_e(n.row)},{default:i(()=>[a("div",yt,[o(Le,{size:"16",class:"mr-1"}),bt])]),_:2},1032,["onClick"])]),_:2},1024)]),default:i(()=>[a("div",gt,[o($e,{width:"16",height:"16"})])]),_:2},1024)])]),_:1})]),_:1},8,["data"])]),o(b(ee),{currentPage:V.value,"onUpdate:currentPage":e[5]||(e[5]=n=>V.value=n),pageSize:$.value,"onUpdate:pageSize":e[6]||(e[6]=n=>$.value=n),total:J.value,pageSizes:de.value,onChange:pe},null,8,["currentPage","pageSize","total","pageSizes"])])]),o(b(te),{ref_key:"userFormDialogRef",ref:P,"department-data":z.value,"role-data":ce.value,onSubmit:ve},null,8,["department-data","role-data"])],64)}}}),Vt=Ve(kt,[["__scopeId","data-v-c07cb6d9"]]);export{Vt as default};
