'use client';
import type { FC } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiErrorWarningFill } from '@remixicon/react';
// RiBookmark3Line
import { useBoolean } from 'ahooks';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import TabHeader from '../../base/tab-header';
import { checkOrSetAccessToken } from '../utils';
import MenuDropdown from './menu-dropdown';
import RunBatch from './run-batch';
import ResDownload from './run-batch/res-download';
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints';
import RunOnce from '@/app/components/share/text-generation/run-once';
import {
  fetchSavedMessage as doFetchSavedMessage,
  fetchAppInfo,
  fetchAppParams,
  removeMessage,
  saveMessage,
} from '@/service/share';
import type { SiteInfo } from '@/models/share';
import type {
  MoreLikeThisConfig,
  PromptConfig,
  SavedMessage,
  TextToSpeechConfig,
} from '@/models/debug';
import AppIcon from '@/app/components/base/app-icon';
import Badge from '@/app/components/base/badge';
import { changeLanguage } from '@/i18n/i18next-config';
import Loading from '@/app/components/base/loading';
import { userInputsFormToPromptVariables } from '@/utils/model-config';
import Res from '@/app/components/share/text-generation/result';
import SavedItems from '@/app/components/app/text-generate/saved-items';
import type { InstalledApp } from '@/models/explore';
import { DEFAULT_VALUE_MAX_LEN, appDefaultIconBackground } from '@/config';
import Toast from '@/app/components/base/toast';
import type { VisionFile, VisionSettings } from '@/types/app';
import { Resolution, TransferMethod } from '@/types/app';
import { useAppFavicon } from '@/hooks/use-app-favicon';
import cn from '@/utils/classnames';
import type { SVGProps } from 'react';
import { AppTypeIcon } from '@/app/components/app/type-selector';
// import { CheckCircle } from '@/app/components/base/icons/src/vender/solid/general';

const GROUP_SIZE = 5; // to avoid RPM(Request per minute) limit. The group task finished then the next group.
enum TaskStatus {
  pending = 'pending',
  running = 'running',
  completed = 'completed',
  failed = 'failed',
}

type TaskParam = {
  inputs: Record<string, any>;
};

type Task = {
  id: number;
  status: TaskStatus;
  params: TaskParam;
};

export type IMainProps = {
  isInstalledApp?: boolean;
  installedAppInfo?: InstalledApp;
  isWorkflow?: boolean;
};

const CollectIcon = ({ className }: SVGProps<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.99914 1C8.25298 0.99935 8.48517 1.14291 8.59802 1.37029L10.507 5.21658L14.7642 5.83734C15.0149 5.87391 15.2233 6.04949 15.3018 6.29044C15.3804 6.53138 15.3155 6.79602 15.1344 6.97333L12.0379 10.0059L12.7732 14.2187C12.817 14.4697 12.7139 14.7237 12.5076 14.8732C12.3012 15.0227 12.0277 15.0415 11.8029 14.9217L8.00089 12.8952L4.19974 14.9216C3.97476 15.0416 3.70107 15.0227 3.49469 14.873C3.2883 14.7233 3.18537 14.469 3.22953 14.2179L3.97013 10.0059L0.868609 6.97374C0.687257 6.79644 0.622213 6.5316 0.7008 6.29046C0.779386 6.04932 0.98799 5.87366 1.23898 5.83727L5.51931 5.21659L7.40217 1.37336C7.51385 1.14541 7.7453 1.00065 7.99914 1ZM8.00473 3.17407L6.56187 6.11917C6.465 6.31689 6.27676 6.45404 6.05886 6.48563L2.76517 6.96324L5.15367 9.29833C5.31102 9.45217 5.38233 9.67375 5.34422 9.89048L4.77961 13.1015L7.68723 11.5514C7.88324 11.447 8.11841 11.4469 8.31443 11.5514L11.2249 13.1027L10.6641 9.88966C10.6264 9.67337 10.6975 9.45236 10.8544 9.29874L13.2399 6.96251L9.9689 6.48556C9.75241 6.45399 9.56518 6.31821 9.46792 6.12224L8.00473 3.17407Z"
        fill="currentColor"
      />
    </svg>
  );
};

const TextGeneration: FC<IMainProps> = ({
  isInstalledApp = false,
  installedAppInfo,
  isWorkflow = false,
}) => {
  const { notify } = Toast;

  const { t } = useTranslation();
  const media = useBreakpoints();
  const isPC = media === MediaType.pc;

  const searchParams = useSearchParams();
  const mode = searchParams.get('mode') || 'create';
  const [currentTab, setCurrentTab] = useState<string>(
    ['create', 'batch'].includes(mode) ? mode : 'create'
  );

  const router = useRouter();
  const pathname = usePathname();
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    if (params.has('mode')) {
      params.delete('mode');
      router.replace(`${pathname}?${params.toString()}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Notice this situation isCallBatchAPI but not in batch tab
  const [isCallBatchAPI, setIsCallBatchAPI] = useState(false);
  const isInBatchTab = currentTab === 'batch';
  const [inputs, doSetInputs] = useState<Record<string, any>>({});
  const inputsRef = useRef(inputs);
  const setInputs = useCallback((newInputs: Record<string, any>) => {
    doSetInputs(newInputs);
    inputsRef.current = newInputs;
  }, []);
  const [appId, setAppId] = useState<string>('');
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [canReplaceLogo, setCanReplaceLogo] = useState<boolean>(false);
  const [customConfig, setCustomConfig] = useState<Record<string, any> | null>(null);
  const [promptConfig, setPromptConfig] = useState<PromptConfig | null>(null);
  const [moreLikeThisConfig, setMoreLikeThisConfig] = useState<MoreLikeThisConfig | null>(null);
  const [textToSpeechConfig, setTextToSpeechConfig] = useState<TextToSpeechConfig | null>(null);

  // save message
  const [savedMessages, setSavedMessages] = useState<SavedMessage[]>([]);
  const fetchSavedMessage = async () => {
    const res: any = await doFetchSavedMessage(isInstalledApp, installedAppInfo?.id);
    setSavedMessages(res.data);
  };
  const handleSaveMessage = async (messageId: string) => {
    await saveMessage(messageId, isInstalledApp, installedAppInfo?.id);
    notify({ type: 'success', message: t('common.api.saved') });
    fetchSavedMessage();
  };
  const handleRemoveSavedMessage = async (messageId: string) => {
    await removeMessage(messageId, isInstalledApp, installedAppInfo?.id);
    notify({ type: 'success', message: t('common.api.remove') });
    fetchSavedMessage();
  };

  // send message task
  const [controlSend, setControlSend] = useState(0);
  const [controlStopResponding, setControlStopResponding] = useState(0);
  const [visionConfig, setVisionConfig] = useState<VisionSettings>({
    enabled: false,
    number_limits: 2,
    detail: Resolution.low,
    transfer_methods: [TransferMethod.local_file],
  });
  const [completionFiles, setCompletionFiles] = useState<VisionFile[]>([]);

  const handleSend = () => {
    setIsCallBatchAPI(false);
    setControlSend(Date.now());

    // eslint-disable-next-line ts/no-use-before-define
    setAllTaskList([]); // clear batch task running status

    // eslint-disable-next-line ts/no-use-before-define
    showResultPanel();
  };

  const [controlRetry, setControlRetry] = useState(0);
  const handleRetryAllFailedTask = () => {
    setControlRetry(Date.now());
  };
  const [allTaskList, doSetAllTaskList] = useState<Task[]>([]);
  const allTaskListRef = useRef<Task[]>([]);
  const getLatestTaskList = () => allTaskListRef.current;
  const setAllTaskList = (taskList: Task[]) => {
    doSetAllTaskList(taskList);
    allTaskListRef.current = taskList;
  };
  const pendingTaskList = allTaskList.filter(task => task.status === TaskStatus.pending);
  const noPendingTask = pendingTaskList.length === 0;
  const showTaskList = allTaskList.filter(task => task.status !== TaskStatus.pending);
  const [currGroupNum, doSetCurrGroupNum] = useState(0);
  const currGroupNumRef = useRef(0);
  const setCurrGroupNum = (num: number) => {
    doSetCurrGroupNum(num);
    currGroupNumRef.current = num;
  };
  const getCurrGroupNum = () => {
    return currGroupNumRef.current;
  };
  const allSuccessTaskList = allTaskList.filter(task => task.status === TaskStatus.completed);
  const allFailedTaskList = allTaskList.filter(task => task.status === TaskStatus.failed);
  const allTasksFinished = allTaskList.every(task => task.status === TaskStatus.completed);
  const allTasksRun = allTaskList.every(task =>
    [TaskStatus.completed, TaskStatus.failed].includes(task.status)
  );
  const [batchCompletionRes, doSetBatchCompletionRes] = useState<Record<string, string>>({});
  const batchCompletionResRef = useRef<Record<string, string>>({});
  const setBatchCompletionRes = (res: Record<string, string>) => {
    doSetBatchCompletionRes(res);
    batchCompletionResRef.current = res;
  };
  const getBatchCompletionRes = () => batchCompletionResRef.current;
  const exportRes = allTaskList.map(task => {
    const batchCompletionResLatest = getBatchCompletionRes();
    const res: Record<string, string> = {};
    const { inputs } = task.params;
    promptConfig?.prompt_variables.forEach(v => {
      res[v.name] = inputs[v.key];
    });
    let result = batchCompletionResLatest[task.id];
    // task might return multiple fields, should marshal object to string
    if (typeof batchCompletionResLatest[task.id] === 'object') result = JSON.stringify(result);

    res[t('share.generation.completionResult')] = result;
    return res;
  });
  const checkBatchInputs = (data: string[][]) => {
    if (!data || data.length === 0) {
      notify({ type: 'error', message: t('share.generation.errorMsg.empty') });
      return false;
    }
    const headerData = data[0];
    let isMapVarName = true;
    promptConfig?.prompt_variables.forEach((item, index) => {
      if (!isMapVarName) return;

      if (item.name !== headerData[index]) isMapVarName = false;
    });

    if (!isMapVarName) {
      notify({
        type: 'error',
        message: t('share.generation.errorMsg.fileStructNotMatch'),
      });
      return false;
    }

    let payloadData = data.slice(1);
    if (payloadData.length === 0) {
      notify({
        type: 'error',
        message: t('share.generation.errorMsg.atLeastOne'),
      });
      return false;
    }

    // check middle empty line
    const allEmptyLineIndexes = payloadData
      .filter(item => item.every(i => i === ''))
      .map(item => payloadData.indexOf(item));
    if (allEmptyLineIndexes.length > 0) {
      let hasMiddleEmptyLine = false;
      let startIndex = allEmptyLineIndexes[0] - 1;
      allEmptyLineIndexes.forEach(index => {
        if (hasMiddleEmptyLine) return;

        if (startIndex + 1 !== index) {
          hasMiddleEmptyLine = true;
          return;
        }
        startIndex++;
      });

      if (hasMiddleEmptyLine) {
        notify({
          type: 'error',
          message: t('share.generation.errorMsg.emptyLine', {
            rowIndex: startIndex + 2,
          }),
        });
        return false;
      }
    }

    // check row format
    payloadData = payloadData.filter(item => !item.every(i => i === ''));
    // after remove empty rows in the end, checked again
    if (payloadData.length === 0) {
      notify({
        type: 'error',
        message: t('share.generation.errorMsg.atLeastOne'),
      });
      return false;
    }
    let errorRowIndex = 0;
    let requiredVarName = '';
    let moreThanMaxLengthVarName = '';
    let maxLength = 0;
    payloadData.forEach((item, index) => {
      if (errorRowIndex !== 0) return;

      promptConfig?.prompt_variables.forEach((varItem, varIndex) => {
        if (errorRowIndex !== 0) return;
        if (varItem.type === 'string') {
          const maxLen = varItem.max_length || DEFAULT_VALUE_MAX_LEN;
          if (item[varIndex].length > maxLen) {
            moreThanMaxLengthVarName = varItem.name;
            maxLength = maxLen;
            errorRowIndex = index + 1;
            return;
          }
        }
        if (!varItem.required) return;

        if (item[varIndex].trim() === '') {
          requiredVarName = varItem.name;
          errorRowIndex = index + 1;
        }
      });
    });

    if (errorRowIndex !== 0) {
      if (requiredVarName) {
        notify({
          type: 'error',
          message: t('share.generation.errorMsg.invalidLine', {
            rowIndex: errorRowIndex + 1,
            varName: requiredVarName,
          }),
        });
      }

      if (moreThanMaxLengthVarName) {
        notify({
          type: 'error',
          message: t('share.generation.errorMsg.moreThanMaxLengthLine', {
            rowIndex: errorRowIndex + 1,
            varName: moreThanMaxLengthVarName,
            maxLength,
          }),
        });
      }

      return false;
    }
    return true;
  };
  const handleRunBatch = (data: string[][]) => {
    if (!checkBatchInputs(data)) return;
    if (!allTasksFinished) {
      notify({
        type: 'info',
        message: t('appDebug.errorMessage.waitForBatchResponse'),
      });
      return;
    }

    const payloadData = data.filter(item => !item.every(i => i === '')).slice(1);
    const varLen = promptConfig?.prompt_variables.length || 0;
    setIsCallBatchAPI(true);
    const allTaskList: Task[] = payloadData.map((item, i) => {
      const inputs: Record<string, string> = {};
      if (varLen > 0) {
        item.slice(0, varLen).forEach((input, index) => {
          inputs[promptConfig?.prompt_variables[index].key as string] = input;
        });
      }
      return {
        id: i + 1,
        status: i < GROUP_SIZE ? TaskStatus.running : TaskStatus.pending,
        params: {
          inputs,
        },
      };
    });
    setAllTaskList(allTaskList);
    setCurrGroupNum(0);
    setControlSend(Date.now());
    // clear run once task status
    setControlStopResponding(Date.now());

    // eslint-disable-next-line ts/no-use-before-define
    showResultPanel();
  };
  const handleCompleted = (completionRes: string, taskId?: number, isSuccess?: boolean) => {
    const allTaskListLatest = getLatestTaskList();
    const batchCompletionResLatest = getBatchCompletionRes();
    const pendingTaskList = allTaskListLatest.filter(task => task.status === TaskStatus.pending);
    const runTasksCount =
      1 +
      allTaskListLatest.filter(task =>
        [TaskStatus.completed, TaskStatus.failed].includes(task.status)
      ).length;
    const needToAddNextGroupTask =
      getCurrGroupNum() !== runTasksCount &&
      pendingTaskList.length > 0 &&
      (runTasksCount % GROUP_SIZE === 0 || allTaskListLatest.length - runTasksCount < GROUP_SIZE);
    // avoid add many task at the same time
    if (needToAddNextGroupTask) setCurrGroupNum(runTasksCount);

    const nextPendingTaskIds = needToAddNextGroupTask
      ? pendingTaskList.slice(0, GROUP_SIZE).map(item => item.id)
      : [];
    const newAllTaskList = allTaskListLatest.map(item => {
      if (item.id === taskId) {
        return {
          ...item,
          status: isSuccess ? TaskStatus.completed : TaskStatus.failed,
        };
      }
      if (needToAddNextGroupTask && nextPendingTaskIds.includes(item.id)) {
        return {
          ...item,
          status: TaskStatus.running,
        };
      }
      return item;
    });
    setAllTaskList(newAllTaskList);
    if (taskId) {
      setBatchCompletionRes({
        ...batchCompletionResLatest,
        [`${taskId}`]: completionRes,
      });
    }
  };

  const fetchInitData = async () => {
    if (!isInstalledApp) await checkOrSetAccessToken();
    // debugger;
    return Promise.all([
      isInstalledApp
        ? {
            app_id: installedAppInfo?.id,
            site: {
              title: installedAppInfo?.app.name,
              prompt_public: false,
              copyright: '',
              icon_url: installedAppInfo?.app.icon_url,
              icon_type: installedAppInfo?.app.icon_type,
              icon: installedAppInfo?.app.icon,
              icon_background: installedAppInfo?.app.icon_background,
              mode: installedAppInfo?.app.mode,
            },
            plan: 'basic',
          }
        : fetchAppInfo(),
      fetchAppParams(isInstalledApp, installedAppInfo?.id),
      !isWorkflow ? fetchSavedMessage() : {},
    ]);
  };

  useEffect(() => {
    (async () => {
      const [appData, appParams]: any = await fetchInitData();
      // debugger;
      const { app_id: appId, site: siteInfo, can_replace_logo, custom_config } = appData;
      setAppId(appId);
      setSiteInfo(siteInfo as SiteInfo);
      setCanReplaceLogo(can_replace_logo);
      setCustomConfig(custom_config);
      changeLanguage(siteInfo.default_language);

      const { user_input_form, more_like_this, file_upload, text_to_speech }: any = appParams;
      setVisionConfig({
        // legacy of image upload compatible
        ...file_upload,
        transfer_methods:
          file_upload.allowed_file_upload_methods || file_upload.allowed_upload_methods,
        // legacy of image upload compatible
        image_file_size_limit: appParams?.system_parameters?.image_file_size_limit,
        fileUploadConfig: appParams?.system_parameters,
      });
      const prompt_variables = userInputsFormToPromptVariables(user_input_form);
      setPromptConfig({
        prompt_template: '', // placeholder for future
        prompt_variables,
      } as PromptConfig);
      setMoreLikeThisConfig(more_like_this);
      setTextToSpeechConfig(text_to_speech);
    })();
  }, []);

  // Can Use metadata(https://beta.nextjs.org/docs/api-reference/metadata) to set title. But it only works in server side client.
  useEffect(() => {
    if (siteInfo?.title) {
      if (canReplaceLogo) document.title = `${siteInfo.title}`;
      else document.title = `${siteInfo.title} - Powered by INTELLIDO`;
    }
  }, [siteInfo?.title, canReplaceLogo]);

  useAppFavicon({
    enable: !isInstalledApp,
    icon_type: siteInfo?.icon_type,
    icon: siteInfo?.icon,
    icon_background: siteInfo?.icon_background,
    icon_url: siteInfo?.icon_url,
  });

  const [isShowResultPanel, { setTrue: doShowResultPanel, setFalse: hideResultPanel }] =
    useBoolean(false);
  const showResultPanel = () => {
    // fix: useClickAway hideResSidebar will close sidebar
    setTimeout(() => {
      doShowResultPanel();
    }, 0);
  };
  const [resultExisted, setResultExisted] = useState(false);

  const renderRes = (task?: Task) => (
    <Res
      key={task?.id}
      isWorkflow={isWorkflow}
      isCallBatchAPI={isCallBatchAPI}
      isPC={isPC}
      isMobile={!isPC}
      isInstalledApp={isInstalledApp}
      installedAppInfo={installedAppInfo}
      isError={task?.status === TaskStatus.failed}
      promptConfig={promptConfig}
      moreLikeThisEnabled={!!moreLikeThisConfig?.enabled}
      inputs={isCallBatchAPI ? (task as Task).params.inputs : inputs}
      controlSend={controlSend}
      controlRetry={task?.status === TaskStatus.failed ? controlRetry : 0}
      controlStopResponding={controlStopResponding}
      onShowRes={showResultPanel}
      handleSaveMessage={handleSaveMessage}
      taskId={task?.id}
      onCompleted={handleCompleted}
      visionConfig={visionConfig}
      completionFiles={completionFiles}
      isShowTextToSpeech={!!textToSpeechConfig?.enabled}
      siteInfo={siteInfo}
      onRunStart={() => setResultExisted(true)}
    />
  );

  const renderBatchRes = () => {
    return showTaskList.map(task => renderRes(task));
  };

  const renderResWrap = (
    <div
      className={cn(
        'relative flex h-full flex-col px-6',
        !isPC && 'h-[calc(100vh_-_36px)] rounded-t-2xl shadow-lg backdrop-blur-sm'
        // !isPC
        //   ? isShowResultPanel
        //     ? 'bg-background-default-burn'
        //     : 'border-t-[0.5px] border-divider-regular bg-components-panel-bg'
        //   : 'bg-chatbot-bg'
      )}
    >
      {isCallBatchAPI && (
        <div
          className={cn(
            'flex shrink-0 items-center justify-between px-0 pb-1 pt-6'
            // !isPC && 'px-0 pb-1 pt-6'
          )}
        >
          <div className="system-md-semibold-uppercase text-text-primary">
            {t('share.generation.executions', { num: allTaskList.length })}
          </div>
          {/* {allSuccessTaskList.length > 0 && && (
            <CheckCircle className="h-4.5 w-4.5 mr-1 shrink-0 text-util-colors-green-green-400" />
          )} */}
          {allSuccessTaskList.length > 0 && <ResDownload isMobile={!isPC} values={exportRes} />}
        </div>
      )}
      <div
        className={cn(
          'flex h-0 grow flex-col overflow-y-auto',
          isPC && 'px-0 py-5',
          isPC && isCallBatchAPI && 'pt-0',
          !isPC && 'p-0 pb-2'
        )}
      >
        {!isCallBatchAPI ? renderRes() : renderBatchRes()}
        {!noPendingTask && (
          <div className="mt-4">
            <Loading type="area" />
          </div>
        )}
      </div>
      {isCallBatchAPI && allFailedTaskList.length > 0 && (
        <div className="absolute bottom-6 left-1/2 z-10 flex -translate-x-1/2 items-center gap-2 rounded-xl border border-components-panel-border bg-components-panel-bg-blur p-3 shadow-lg backdrop-blur-sm">
          <RiErrorWarningFill className="h-4 w-4 text-text-destructive" />
          <div className="system-sm-medium text-text-secondary">
            {t('share.generation.batchFailed.info', {
              num: allFailedTaskList.length,
            })}
          </div>
          <div className="h-3.5 w-px bg-divider-regular"></div>
          <div
            onClick={handleRetryAllFailedTask}
            className="system-sm-semibold-uppercase cursor-pointer text-text-accent"
          >
            {t('share.generation.batchFailed.retry')}
          </div>
        </div>
      )}
    </div>
  );

  if (!appId || !siteInfo || !promptConfig) {
    return (
      <div className="flex h-screen items-center">
        <Loading type="app" />
      </div>
    );
  }

  return (
    <div
      className={cn(
        // 'bg-background-default-burn',
        isPC && 'flex',
        !isPC && 'flex-col',
        isInstalledApp ? 'h-full' : 'h-screen'
      )}
    >
      {/* Left */}
      <div
        className={cn(
          'relative flex h-full shrink-0 flex-col bg-components-panel-bg px-4 py-5',
          isPC ? 'w-[600px] max-w-[50%]' : resultExisted ? 'h-[calc(100%_-_64px)]' : ''
          // isInstalledApp && 'rounded-l-2xl'
        )}
      >
        {/* header */}
        <div
          className={cn(
            'shrink-0 space-y-4 border-b border-divider-subtle',
            isPC ? 'bg-components-panel-bg' : ''
          )}
        >
          <div className="flex items-center gap-3">
            <div className="relative shrink-0">
              <AppIcon
                size={isPC ? 'large' : 'small'}
                iconType={siteInfo.icon_type}
                icon={siteInfo.icon}
                background={siteInfo.icon_background || appDefaultIconBackground}
                imageUrl={siteInfo.icon_url}
                className="rounded-full !text-xl"
              />
              <AppTypeIcon
                type={siteInfo.mode}
                wrapperClassName="absolute -bottom-0.5 -right-0.5 w-4 h-4 shadow-sm"
                className="h-3 w-3"
              />
            </div>
            <div className="system-md-semibold grow truncate text-text-secondary">
              {siteInfo.title}
            </div>
            <MenuDropdown data={siteInfo} />
          </div>
          <div className="flex items-center text-xs font-medium text-text-tertiary">
            {siteInfo.mode === 'advanced-chat' && (
              <div className="truncate">{t('app.types.advanced').toUpperCase()}</div>
            )}
            {siteInfo.mode === 'chat' && (
              <div className="truncate">{t('app.types.chatbot').toUpperCase()}</div>
            )}
            {siteInfo.mode === 'agent-chat' && (
              <div className="truncate">{t('app.types.agent').toUpperCase()}</div>
            )}
            {siteInfo.mode === 'workflow' && (
              <div className="truncate">{t('app.types.workflow').toUpperCase()}</div>
            )}
            {siteInfo.mode === 'completion' && (
              <div className="truncate">{t('app.types.completion').toUpperCase()}</div>
            )}
          </div>
          {siteInfo.description && (
            <div className="system-xs-regular text-text-tertiary">{siteInfo.description}</div>
          )}
          <TabHeader
            items={[
              { id: 'create', name: t('share.generation.tabs.create') },
              { id: 'batch', name: t('share.generation.tabs.batch') },
              ...(!isWorkflow
                ? [
                    {
                      id: 'saved',
                      name: t('share.generation.tabs.saved'),
                      isRight: true,
                      icon: <CollectIcon className="h-4 w-4" />,
                      extra:
                        savedMessages.length > 0 ? (
                          <Badge className="ml-1">{savedMessages.length}</Badge>
                        ) : null,
                    },
                  ]
                : []),
            ]}
            value={currentTab}
            onChange={setCurrentTab}
          />
        </div>
        {/* form */}
        <div
          className={cn(
            'h-0 grow overflow-y-auto bg-components-panel-bg px-3',
            !isPC &&
              resultExisted &&
              customConfig?.remove_webapp_brand &&
              'rounded-b-2xl border-b-[0.5px] border-divider-regular'
          )}
        >
          <div className={cn(currentTab === 'create' ? 'block' : 'hidden')}>
            <RunOnce
              siteInfo={siteInfo}
              inputs={inputs}
              inputsRef={inputsRef}
              onInputsChange={setInputs}
              promptConfig={promptConfig}
              onSend={handleSend}
              visionConfig={visionConfig}
              onVisionFilesChange={setCompletionFiles}
            />
          </div>
          <div className={cn(isInBatchTab ? 'block' : 'hidden')}>
            <RunBatch
              vars={promptConfig.prompt_variables}
              onSend={handleRunBatch}
              isAllFinished={allTasksRun}
            />
          </div>
          {currentTab === 'saved' && (
            <SavedItems
              className={cn(isPC ? 'mt-6' : 'mt-4')}
              isShowTextToSpeech={textToSpeechConfig?.enabled}
              list={savedMessages}
              onRemove={handleRemoveSavedMessage}
              onStartCreateContent={() => setCurrentTab('create')}
            />
          )}
        </div>
        {/* powered by */}
        {/* {!customConfig?.remove_webapp_brand && (
          <div
            className={cn(
              'flex shrink-0 items-center gap-1.5 bg-components-panel-bg py-3',
              isPC ? 'px-8' : 'px-4',
              !isPC && resultExisted && 'rounded-b-2xl border-b-[0.5px] border-divider-regular'
            )}
          >
            <div className="system-2xs-medium-uppercase text-text-tertiary">
              {t('share.chat.poweredBy')} INTELLIDO
            </div>
          </div>
        )} */}
      </div>
      {/* Result */}
      <div
        className={cn(
          isPC
            ? 'h-full grow'
            : isShowResultPanel
            ? 'fixed inset-0 z-50 backdrop-blur-sm'
            : resultExisted
            ? 'relative h-16 shrink-0 overflow-hidden pt-2.5'
            : ''
        )}
      >
        {!isPC && (
          <div
            className={cn(
              isShowResultPanel
                ? 'flex items-center justify-center p-2 pt-6'
                : 'absolute left-0 top-0 z-10 flex w-full items-center justify-center px-2 pb-[57px] pt-[3px]'
            )}
            onClick={() => {
              if (isShowResultPanel) hideResultPanel();
              else showResultPanel();
            }}
          >
            <div className="h-1 w-8 cursor-grab rounded bg-divider-solid" />
          </div>
        )}
        {renderResWrap}
      </div>
    </div>
  );
};

export default TextGeneration;
