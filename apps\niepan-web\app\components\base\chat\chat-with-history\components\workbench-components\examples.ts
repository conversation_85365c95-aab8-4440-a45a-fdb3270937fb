// 工作台组件使用示例

import type { WorkbenchComponentData } from './componentRegistry';

// 示例1：PPT预览组件
export const pptxPreviewExample: WorkbenchComponentData = {
  uid: "pptx-preview-component",
  area: "main",
  data: {
    title: "项目演示文档",
    fileUrl: "http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf",
    initialPage: 1,
    hideFullscreenButton: false,
  },
};

// 示例2：文本详情组件 - 成功信息
export const textDetailSuccessExample: WorkbenchComponentData = {
  uid: "text-detail-component",
  area: "main",
  data: {
    title: "分析完成",
    content: "文档分析已成功完成！\n\n主要发现：\n• 文档结构清晰\n• 内容完整性良好\n• 建议进入下一阶段",
    type: "success",
    collapsible: false,
    defaultExpanded: true,
  },
};

// 示例3：文本详情组件 - 警告信息
export const textDetailWarningExample: WorkbenchComponentData = {
  uid: "text-detail-component",
  area: "main",
  data: {
    title: "注意事项",
    content: "在继续之前，请注意以下几点：\n\n1. 确保所有依赖项已安装\n2. 检查网络连接状态\n3. 备份重要数据",
    type: "warning",
    collapsible: true,
    defaultExpanded: true,
  },
};

// 示例4：列表组件 - 项目列表
export const projectListExample: WorkbenchComponentData = {
  uid: "list-component",
  area: "main",
  data: {
    title: "项目列表",
    showSearch: true,
    showStatus: true,
    maxHeight: "500px",
    items: [
      {
        id: "proj-001",
        title: "智能客服系统",
        description: "基于AI的智能客服解决方案，支持多轮对话和情感分析",
        status: "active",
        metadata: {
          progress: "85%",
          team: "AI团队",
          deadline: "2024-03-15"
        }
      },
      {
        id: "proj-002",
        title: "数据分析平台",
        description: "企业级数据分析和可视化平台，支持实时数据处理",
        status: "pending",
        metadata: {
          progress: "60%",
          team: "数据团队",
          deadline: "2024-04-20"
        }
      },
      {
        id: "proj-003",
        title: "移动应用开发",
        description: "跨平台移动应用，包含iOS和Android版本",
        status: "inactive",
        metadata: {
          progress: "30%",
          team: "移动团队",
          deadline: "2024-05-10"
        }
      }
    ]
  },
};

// 示例5：文档列表
export const documentListExample: WorkbenchComponentData = {
  uid: "list-component",
  area: "main",
  data: {
    title: "相关文档",
    showSearch: true,
    showStatus: false,
    items: [
      {
        id: "doc-001",
        title: "需求规格说明书",
        description: "详细的功能需求和非功能需求描述",
        metadata: {
          type: "Word",
          size: "2.5MB",
          modified: "2024-01-15"
        }
      },
      {
        id: "doc-002",
        title: "系统设计文档",
        description: "系统架构设计和模块划分说明",
        metadata: {
          type: "PDF",
          size: "4.2MB",
          modified: "2024-01-20"
        }
      },
      {
        id: "doc-003",
        title: "API接口文档",
        description: "RESTful API接口定义和使用说明",
        metadata: {
          type: "Markdown",
          size: "1.8MB",
          modified: "2024-01-25"
        }
      }
    ]
  },
};

// 组合示例：多个组件的完整工作台配置
export const fullWorkbenchExample: WorkbenchComponentData[] = [
  {
    uid: "text-detail-component",
    area: "main",
    data: {
      title: "任务概览",
      content: "当前正在处理文档分析任务，预计完成时间：5分钟",
      type: "info",
      collapsible: false,
    },
  },
  pptxPreviewExample,
  projectListExample,
  {
    uid: "text-detail-component",
    area: "main",
    data: {
      title: "操作建议",
      content: "基于当前分析结果，建议您：\n\n1. 查看PPT预览中的关键页面\n2. 关注项目列表中的高优先级项目\n3. 准备下一步的详细分析",
      type: "info",
      collapsible: true,
      defaultExpanded: false,
    },
  },
];

// 聊天消息触发的动态更新示例
export const chatTriggeredUpdates = {
  // 当用户上传文档时
  onDocumentUpload: (fileUrl: string, fileName: string): WorkbenchComponentData => ({
    uid: "pptx-preview-component",
    area: "main",
    data: {
      title: `文档预览: ${fileName}`,
      fileUrl,
      initialPage: 1,
    },
  }),

  // 当分析完成时
  onAnalysisComplete: (results: string): WorkbenchComponentData => ({
    uid: "text-detail-component",
    area: "main",
    data: {
      title: "分析结果",
      content: results,
      type: "success",
      collapsible: true,
      defaultExpanded: true,
    },
  }),

  // 当需要显示相关项目时
  onShowRelatedProjects: (projects: any[]): WorkbenchComponentData => ({
    uid: "list-component",
    area: "main",
    data: {
      title: "相关项目",
      showSearch: true,
      showStatus: true,
      items: projects.map(project => ({
        id: project.id,
        title: project.name,
        description: project.description,
        status: project.status,
        metadata: project.metadata || {},
      })),
    },
  }),
};
