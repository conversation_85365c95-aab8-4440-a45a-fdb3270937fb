# 工作台动态组件系统

这是一个基于React的动态组件注册和渲染系统，参考Vue版本的设计模式，用于在聊天工作台中动态显示各种类型的组件。

## 核心概念

### 组件数据结构

每个工作台组件都遵循统一的数据结构：

```typescript
interface WorkbenchComponentData {
  uid: string; // 组件唯一标识符
  area: string; // 显示区域（目前主要使用 "main"）
  data: Record<string, any>; // 组件具体数据
}
```

### 组件注册机制

- **同步注册**：`registerComponent(uid, Component)`
- **异步注册**：`registerAsyncComponent(uid, loader)`

## 组件

### 1. PPT预览组件 (`pptx-preview-component`)

用于预览PPT、PDF等文档文件。

```typescript
{
  uid: "pptx-preview-component",
  area: "main",
  data: {
    title: "文档标题",
    fileUrl: "文档URL",
    initialPage: 1,
    hideFullscreenButton: false
  }
}
```

### 2. 文本详情组件 (`text-detail-component`)

用于显示格式化的文本信息，支持不同类型的样式。

```typescript
{
  uid: "text-detail-component",
  area: "main",
  data: {
    title: "标题",
    content: "文本内容",
    type: "info" | "warning" | "error" | "success",
    collapsible: true,
    defaultExpanded: true
  }
}
```

### 3. 列表组件 (`list-component`)

用于显示可搜索、可选择的列表数据。

```typescript
{
  uid: "list-component",
  area: "main",
  data: {
    title: "列表标题",
    showSearch: true,
    showStatus: true,
    maxHeight: "400px",
    items: [
      {
        id: "item-1",
        title: "项目标题",
        description: "项目描述",
        status: "active" | "inactive" | "pending",
        metadata: { key: "value" }
      }
    ]
  }
}
```

## 使用方法

### 1. 基本使用

```tsx
import { useWorkbenchComponents } from './hooks/useWorkbenchComponents';

function WorkbenchExample() {
  const { renderComponents, addComponent } = useWorkbenchComponents();

  return <div className="workbench">{renderComponents('main')}</div>;
}
```

### 2. 动态添加组件

```tsx
const { addComponent } = useWorkbenchComponents();

// 添加PPT预览组件
addComponent({
  uid: 'pptx-preview-component',
  area: 'main',
  data: {
    title: '新文档',
    fileUrl: 'http://example.com/doc.pdf',
    initialPage: 1,
  },
});
```

### 3. 更新组件数据

```tsx
const { updateComponentData } = useWorkbenchComponents();

// 更新PPT当前页码
updateComponentData('pptx-preview-component', {
  initialPage: 5,
});
```

### 4. 聊天消息触发的动态更新

```tsx
// 在聊天消息处理中
function handleChatMessage(message) {
  if (message.type === 'document_analysis_complete') {
    addComponent({
      uid: 'text-detail-component',
      area: 'main',
      data: {
        title: '分析结果',
        content: message.analysis_result,
        type: 'success',
      },
    });
  }
}
```

## 扩展新组件

### 1. 创建组件

```tsx
// MyCustomComponent.tsx
import type { FC } from 'react';

interface MyCustomProps {
  data: {
    customField: string;
    // 其他字段...
  };
}

const MyCustomComponent: FC<MyCustomProps> = ({ data }) => {
  return (
    <div className="my-custom-component">
      <h3>{data.customField}</h3>
      {/* 组件内容 */}
    </div>
  );
};

export default MyCustomComponent;
```

### 2. 注册组件

```tsx
// 在 index.ts 中注册
import { registerAsyncComponent } from './componentRegistry';

registerAsyncComponent('my-custom-component', () => import('./MyCustomComponent'));
```

### 3. 使用新组件

```tsx
addComponent({
  uid: 'my-custom-component',
  area: 'main',
  data: {
    customField: 'Hello World',
  },
});
```

## 最佳实践

1. **组件命名**：使用描述性的名称，以 `-component` 结尾
2. **数据验证**：在组件内部进行数据验证和默认值处理
3. **错误处理**：利用内置的错误边界处理组件渲染错误
4. **性能优化**：大型组件使用异步注册（懒加载）
5. **类型安全**：为组件数据定义明确的TypeScript接口

## 预留的扩展点

- **多区域支持**：可以扩展支持多个显示区域（如侧边栏、底部等）
- **组件通信**：可以添加组件间的事件通信机制
- **持久化**：可以添加组件状态的本地存储
- **主题支持**：可以添加主题切换功能
- **动画效果**：可以添加组件切换的动画效果

## 示例数据

查看 `examples.ts` 文件获取完整的使用示例和模拟数据。
