import{d as K,al as q,q as N,u as V,r as p,z as U,e as $,c as s,o as t,b as o,p as J,a as F,A as L,f as r,M as x,aa as v,t as h,m as C,I as P,H as Q,J as T,K as Z,w as S,Q as O,x as W,y as X}from"./pnpm-pnpm-B4aX-tnA.js";import{b as Y,a as z,w as ee,x as te}from"./index-C4ad4gme.js";import{E as B,_ as E,g as ae}from"./apiUrl-B-GJIBbf.js";import{_ as se}from"./SearchIcon.vue_vue_type_script_setup_true_lang-DDwCl3ZY.js";const oe=m=>(W("data-v-66c498c0"),m=m(),X(),m),le={class:"w-full h-[52px] px-4 py-3 relative flex justify-center items-center",style:{"border-bottom":"1px solid #e5e5e5"}},ne=oe(()=>o("button",{class:"p-[6px] border-none bg-transparent h-[30px]"},[o("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[o("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.53033 5.03033C9.82322 4.73744 9.82322 4.26256 9.53033 3.96967C9.23744 3.67678 8.76256 3.67678 8.46967 3.96967L4.01065 8.42869C3.85104 8.56623 3.75 8.76985 3.75 8.99707C3.75 8.99757 3.75 8.99807 3.75 8.99857C3.74964 9.19099 3.82286 9.38352 3.96967 9.53033L8.46967 14.0303C8.76256 14.3232 9.23744 14.3232 9.53033 14.0303C9.82322 13.7374 9.82322 13.2626 9.53033 12.9697L6.30773 9.74707H13.5C13.9142 9.74707 14.25 9.41128 14.25 8.99707C14.25 8.58286 13.9142 8.24707 13.5 8.24707H6.31359L9.53033 5.03033Z",fill:"#697077"})])],-1)),ce={class:"relative"},pe={key:0,class:"flex flex-col items-center mr-2"},re={key:1,class:"w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium"},ie=["src","alt"],de={key:1},ue={key:0,class:"truncate max-w-[182px] mr-[6px]"},fe={key:3,class:"absolute top-[52px] left-0 bg-[#fff] rounded-lg w-full shadow-sm p-2 text-[#343A3F] text-xs"},me={class:"relative"},ve={class:"max-h-[40vh] overflow-y-auto custom-scrollbar"},_e=["onClick"],xe={class:"relative"},he={class:"flex flex-col items-center mr-2"},we={key:1,class:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium"},ge=["src","alt"],ye={class:"truncate max-w-[182px] mr-[6px]"},ke={name:"ChangeAppNav"},be=K({...ke,setup(m){const{t:i}=q(),w=N(),g=V(),n=p(w.params.appId),e=p({}),y=c=>ae(c),d=p([]),D=p(1),M=p(1e6),_=p(""),u=p(!1),k=p(null),R=()=>{u.value=!u.value},G=c=>{n.value=c.id,e.value=c,u.value=!u.value,g.push(`/app-chat/${c.id}`)},H=async()=>{const c={page:D.value,limit:M.value};_.value&&(c.name=_.value);try{k.value=O.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const l=await Y(c);if(d.value=l.data.installed_apps,k.value.close(),n.value){const f=d.value.findIndex(b=>b.id==n.value);f!==-1?(n.value=d.value[f].id,e.value=d.value[f]):l.data.length>0&&(n.value=l.data[0].id,e.value=l.data[0])}else l.data.length>0&&(n.value=l.data[0].id,e.value=l.data[0])}finally{k.value.close()}};return U(()=>{H()}),(c,l)=>{var A,I;const f=$("el-icon"),b=$("el-input");return t(),s("div",le,[o("div",{class:"flex items-center text-sm font-medium absolute top-3 left-4 cursor-pointer",onClick:l[0]||(l[0]=a=>c.$emit("goBackClick"))},[ne,J(" 返回 ")]),o("div",{class:"text-sm flex items-center cursor-pointer relative min-w-[250px]",onClick:S(R,["stop"])},[o("div",ce,[e.value.app?(t(),s("div",pe,[e.value.app.icon_type==="emoji"?(t(),s("div",{key:0,class:"w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${e.value.app.icon_background||"#FFEAD5"}`)},[r(B,{emojiId:e.value.app.icon,size:14},null,8,["emojiId"])],4)):e.value.app.icon_url?(t(),s("div",re,[o("img",{src:y(e.value.app.icon_url),alt:v(i)("apps.appIcon"),class:"w-[40px] h-[40px] object-contain rounded-full"},null,8,ie)])):(t(),s("div",{key:2,class:"w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${e.value.app.icon_background||"#FFEAD5"}`)},h((A=e.value.app.name)==null?void 0:A.charAt(0)),5)),r(E,{type:e.value.app.mode,wrapperClassName:"!rounded-full absolute -bottom-[2px] right-[4px] w-4 h-4 shadow-sm",class:"h-4 w-4 rounded-full"},null,8,["type"])])):(t(),s("div",de))]),e.value.app?(t(),s("div",ue,h((I=e.value)==null?void 0:I.app.name),1)):F("",!0),u.value?(t(),L(f,{key:2,class:"flex-shrink-0",color:"#A2A9B0"},{default:C(()=>[r(v(Q))]),_:1})):(t(),L(f,{key:1,class:"flex-shrink-0",color:"#A2A9B0"},{default:C(()=>[r(v(P))]),_:1})),u.value?(t(),s("div",fe,[o("div",me,[r(b,{modelValue:_.value,"onUpdate:modelValue":l[1]||(l[1]=a=>_.value=a),placeholder:"搜索",size:"default",class:"w-full search-input"},{prefix:C(()=>[r(se,{color:"#343A3F"})]),_:1},8,["modelValue"])]),o("div",ve,[(t(!0),s(T,null,Z(d.value,a=>{var j;return t(),s("div",{class:"flex items-center cursor-pointer py-[5px] px-3",key:a.app.id,onClick:S($e=>G(a),["stop"])},[o("div",xe,[o("div",he,[a.app.icon_type==="emoji"?(t(),s("div",{key:0,class:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${a.app.icon_background||"#FFEAD5"}`)},[r(B,{emojiId:a.app.icon,size:13},null,8,["emojiId"])],4)):a.app.icon_url?(t(),s("div",we,[o("img",{src:y(a.app.icon_url),alt:v(i)("apps.appIcon"),class:"w-full h-full object-contain rounded-full"},null,8,ge)])):(t(),s("div",{key:2,class:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${a.app.icon_background||"#FFEAD5"}`)},h((j=a.app.name)==null?void 0:j.charAt(0)),5)),r(E,{type:a.app.mode,wrapperClassName:"!rounded-full absolute -bottom-[2px] right-[4px] w-4 h-4 shadow-sm",class:"h-[14px] w-[14px] rounded-full"},null,8,["type"])])]),o("div",ye,h(a.app.name),1)],8,_e)}),128))])])):F("",!0)])])}}}),Ce=z(be,[["__scopeId","data-v-66c498c0"]]),Ae={class:"w-[100vw] h-[100vh] chat-container overflow-hidden"},Ie=["src"],je={__name:"index",setup(m){const i=N();console.log(i.params,"route.params"),console.log(i.params.appId,"route.params.appId");const w=V(),g=()=>{w.push("/index")};ee();const{globalConfig:n}=te(),e=p("");return U(()=>{n.workspaceSettings&&n.workspaceSettings.installedApp?e.value=n.workspaceSettings.installedApp:(console.warn("模型供应商嵌入URL未配置，请检查config.js文件"),e.value="http://localhost:3000/embed/installed-app"),console.log("embedUrl:",e.value),console.log("Final URL:",e.value+"/"+i.params.appId)}),(y,d)=>(t(),s("div",Ae,[r(Ce,{onGoBackClick:g}),o("iframe",{src:e.value+"/"+v(i).params.appId,class:"w-[100vw] h-[calc(100vh-55px)] border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""},null,8,Ie)]))}},Ee=z(je,[["__scopeId","data-v-a6b78348"]]);export{Ee as default};
