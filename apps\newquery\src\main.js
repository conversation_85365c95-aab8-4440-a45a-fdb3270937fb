import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import enUs from 'element-plus/dist/locale/en.mjs';
import zhTw from 'element-plus/dist/locale/zh-tw.mjs';
import './styles/tailwind.css';
import '@/styles/base_admin.css';
import '@/styles/v3/admin.css';
import 'element-plus/dist/index.css';
import 'ant-design-vue/dist/reset.css';
// import 'emoji-mart/css/emoji-mart.css';
// import Vue3Spline from 'vue3-spline'
import modalPlugin from './modalPlugin';
// import comfirmsModal from '@/components/confirms/confirms.vue'; // 删除的警告弹窗，全局变量
import newComfirmsModal from '@/components/confirms/newConfirms.vue'; // 删除的警告弹窗，全局变量
import v2ConfirmsModal from '@/components/confirms/v2Confirms.vue'; // 删除的警告弹窗，全局变量
import store from '@/store';
import { useUserStore } from '@/store/modules/userInfo';
import i18n, { getLocaleOnClient } from '@/i18n';

const app = createApp(App);
app.use(router);

// 根据当前语言设置Element Plus的语言
const locale = getLocaleOnClient();
let elementLocale = zhCn; // 默认使用简体中文
if (locale === 'en-US') {
    elementLocale = enUs;
} else if (locale === 'zh-Hant') {
    elementLocale = zhTw;
}

app.use(ElementPlus, {
    locale: elementLocale,
});
app.use(modalPlugin);
app.use(i18n);

console.log('this is new query');

// app.use(Vue3Spline)

// app.component('comfirmsModal', comfirmsModal);
app.component('newComfirmsModal', newComfirmsModal);
app.component('v2ConfirmsModal', v2ConfirmsModal);
// 加载Pinia插件
app.use(store);
if (localStorage.getItem('console_token')) {
    // 创建用户状态管理实例
    const userStore = useUserStore();
    // 在应用启动时获取用户信息
    userStore.fetchUserInfo(router.currentRoute.value);
}

app.mount('#app');
