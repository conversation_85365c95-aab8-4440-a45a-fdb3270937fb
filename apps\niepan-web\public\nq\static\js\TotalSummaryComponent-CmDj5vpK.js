import{_ as i}from"./<EMAIL>";import{d as p,c as e,o as s,b as o,t as a,J as _,K as x}from"./pnpm-pnpm-B4aX-tnA.js";const m={class:"w-[360px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs"},b={class:"flex justify-between items-center"},h={class:"flex items-start"},f=["src"],g={key:1,src:i,alt:"",class:"w-[20px] h-[20px] object-contain object-center mt-[2px]"},u={class:"font-zhongcu ml-1.5 text-15 leading-[24px] break-words"},w={class:"mt-[12px]"},j={class:"flex items-center"},y=["src"],k={key:1,src:i,alt:"",class:"w-[16px] h-[16px] object-contain object-center"},v={class:"overflow-one w-[10em] ml-[4px]"},F={class:"overflow-one text-xl font-semibold"},S=p({__name:"TotalSummaryComponent",props:{data:{}},setup(U){return(n,B)=>{var c,r,l;return s(),e("div",m,[o("div",b,[o("div",h,[(c=n.data)!=null&&c.titleImgUrl?(s(),e("img",{key:0,src:(r=n.data)==null?void 0:r.titleImgUrl,alt:"",class:"w-[20px] h-[20px] object-contain object-center mt-[2px]"},null,8,f)):(s(),e("img",g)),o("span",u,a(n.data.title),1)])]),o("div",w,[(s(!0),e(_,null,x((l=n.data)==null?void 0:l.items,(t,d)=>(s(),e("div",{key:d,class:"flex justify-between item-center py-[6px] px-[10px] rounded-[10px] bg-gradient-to-r from-[#ebfbff] to-white mt-[10px]"},[o("div",j,[t!=null&&t.imgUrl?(s(),e("img",{key:0,src:t==null?void 0:t.imgUrl,alt:"",class:"w-[16px] h-[16px] object-contain object-center"},null,8,y)):(s(),e("img",k)),o("span",v,a(t.title),1)]),o("span",F,a(t.total),1)]))),128))])])}}});export{S as default};
