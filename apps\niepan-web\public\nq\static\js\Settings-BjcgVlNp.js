import{d as V,q as D,r as n,ae as I,E as p,z as K,e as M,c as b,o as v,b as e,A as F,a as N,f as d,m as z,w as P,aa as r,k as g,v as x,J as q,K as L,p as U,t as B,X as A,Y as R,x as T,y as j}from"./pnpm-pnpm-B4aX-tnA.js";import{R as G,P as J,ao as O,B as X,N as Y,a as $}from"./index-C4ad4gme.js";import{_ as H}from"./BackArrowIcon.vue_vue_type_script_setup_true_lang-D6zln9HB.js";const l=c=>(T("data-v-4226ea18"),c=c(),j(),c),Q={class:"h-full overflow-hidden"},W={class:"overflow-hidden h-[calc(100vh-234px)] overflow-y-auto"},Z={class:"flex items-center mb-6 pb-[12px] border-0 border-solid border-b border-[#E5E5E5]"},ee=l(()=>e("span",{class:"ml-2 text-[18px] font-semibold text-[#121619] leading-[26px] font-[PingFang SC]"},"设置",-1)),se={class:"h-[calc(100vh-300px)] flex flex-col items-center pr-40 form-div overflow-y"},te={class:"w-[30vw] max-w-[588px]"},oe={class:"form-item mb-8"},ae=l(()=>e("label",{class:"font14 font-zhongcu block"},"名称",-1)),ne={class:"form-item mb-8"},le=l(()=>e("label",{class:"font14 font-zhongcu block"},"知识库描述",-1)),ie={class:"form-item mb-8"},de=l(()=>e("label",{class:"font14 font-zhongcu block"},"数据模型",-1)),re={class:"form-item mb-8"},ce=l(()=>e("label",{class:"font14 font-zhongcu block"},"使用权限",-1)),ue={class:"field-ipt field-siteuser"},me={class:"siteuser-div"},fe={class:"siteuser-headimg"},pe=l(()=>e("div",{class:"form-div-bottom"},[e("button",{type:"submit",class:"form-div-bottom-confirm font14 font-zhongcu common-confirm-btn"}," 保存 ")],-1)),ve={class:"mt-6"},_e=V({__name:"Settings",setup(c){const w=D(),u=n([]),i=n([]),_=n(!1),C=n([{name:"知识库",path:"/admin/knowledge"},{name:"设置"}]),m=n(""),f=n(""),h=n(""),k=()=>{_.value=!_.value},E=o=>{const s=o;u.value=[],i.value=[],s.forEach(a=>{u.value.push(a),i.value.push(a.id)})},y=I.debounce(async()=>{try{const o=await G(m.value,f.value,w.params.id,i.value.join("|"));o.data.error=="0"?p.success("设置成功"):o.data.error=="403"?p.error("暂无权限"):o.data.message&&p.error(o.data.message)}catch{p.error("设置失败")}});K(()=>{S(w.params.id)});const S=async o=>{try{const s=await J(o);s.data.error=="0"&&(m.value=s.data.title,f.value=s.data.description,h.value=s.data.embedding_type,s.data.siteusers.forEach(a=>{u.value.push({id:a.id,name:a.name,nameOne:a.name.split("")[0]}),i.value.push(a.id)}))}catch{}};return(o,s)=>{const a=M("router-link");return v(),b("div",Q,[e("div",W,[e("div",Z,[d(a,{to:{name:"Knowledge"},class:"flex items-center cursor-pointer"},{default:z(()=>[d(H,{size:24,color:"#121619"})]),_:1}),ee]),e("div",se,[e("form",{onSubmit:s[3]||(s[3]=P((...t)=>r(y)&&r(y)(...t),["prevent"]))},[e("div",te,[e("div",oe,[ae,g(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":s[0]||(s[0]=t=>m.value=t),placeholder:"点击输入名字",required:"",maxlength:"50"},null,512),[[x,m.value]])]),e("div",ne,[le,g(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":s[1]||(s[1]=t=>f.value=t),placeholder:"给知识库介绍一下",required:"",maxlength:"500"},null,512),[[x,f.value]])]),e("div",ie,[de,g(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":s[2]||(s[2]=t=>h.value=t),readonly:"",style:{background:"#f7f9fc"}},null,512),[[x,h.value]])]),e("div",re,[ce,e("div",ue,[e("div",me,[(v(!0),b(q,null,L(u.value.slice(0,4),t=>(v(),b("div",{class:"siteuser-div-item font12 font-zhongcu",key:t.id},[e("div",fe,B(r(O)(t.name)),1),U(" "+B(t.name),1)]))),128))]),d(r(R),{size:"20",class:"siteuser-add",onClick:k},{default:z(()=>[d(r(A))]),_:1})])])]),pe],32)])]),e("div",ve,[d(X,{breadcrumbs:C.value},null,8,["breadcrumbs"])]),_.value?(v(),F(Y,{key:0,type:"kbase",sids:i.value,onValueFromChild:E,closeModal:k},null,8,["sids"])):N("",!0)])}}}),xe=$(_e,[["__scopeId","data-v-4226ea18"]]);export{xe as default};
