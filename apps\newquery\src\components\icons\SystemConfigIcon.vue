<!-- 系统设置图标 -->
<template>
  <BaseSvgIcon :color="color" :size="size" :class="className">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
      <path
        d="M4.08277 1.45898C3.87568 1.45898 3.6861 1.56982 3.5811 1.74773L0.664434 6.70607C0.556517 6.88982 0.556517 7.1144 0.664434 7.29815L3.5811 12.2565C3.6861 12.4344 3.8786 12.5452 4.08277 12.5452H9.9161C10.1232 12.5452 10.3128 12.4344 10.4178 12.2565L13.3344 7.29815C13.4424 7.1144 13.4424 6.88982 13.3344 6.70607L10.4178 1.74773C10.3128 1.56982 10.1203 1.45898 9.9161 1.45898H4.08277ZM1.84277 7.00065L4.41527 2.62565H9.58068L12.1532 7.00065L9.58068 11.3757H4.41527L1.84277 7.00065ZM6.99943 4.95898C6.45693 4.95898 5.93777 5.17482 5.55568 5.5569C5.1736 5.93898 4.95777 6.45815 4.95777 7.00065C4.95777 7.54315 5.1736 8.06232 5.55568 8.4444C5.93777 8.82649 6.45693 9.04232 6.99943 9.04232C7.54193 9.04232 8.0611 8.82649 8.44318 8.4444C8.82527 8.06232 9.0411 7.54315 9.0411 7.00065C9.0411 6.45815 8.82527 5.93898 8.44318 5.5569C8.0611 5.17482 7.54193 4.95898 6.99943 4.95898ZM6.3811 6.38232C6.54443 6.21898 6.76902 6.12565 6.99943 6.12565C7.22985 6.12565 7.45443 6.21898 7.61777 6.38232C7.7811 6.54565 7.87443 6.77023 7.87443 7.00065C7.87443 7.23107 7.7811 7.45565 7.61777 7.61899C7.45443 7.78232 7.22985 7.87565 6.99943 7.87565C6.76902 7.87565 6.54443 7.78232 6.3811 7.61899C6.21777 7.45565 6.12443 7.23107 6.12443 7.00065C6.12443 6.77023 6.21777 6.54565 6.3811 6.38232Z"
        :fill="color"
      />
    </svg>
  </BaseSvgIcon>
</template>

<script setup lang="ts">
import BaseSvgIcon from '../base/BaseSvgIcon.vue';

interface Props {
  color?: string;
  size?: string | number;
  className?: string;
}

withDefaults(defineProps<Props>(), {
  color: '#343A3F',
  size: 14,
  className: '',
});
</script>

<script lang="ts">
export default {
  name: 'SystemConfigIcon',
};
</script>
