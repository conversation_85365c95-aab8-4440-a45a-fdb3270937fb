const { codeInspectorPlugin } = require('code-inspector-plugin');
const withMDX = require('@next/mdx')({
  extension: /\.mdx?$/,
  options: {
    // If you use remark-gfm, you'll need to use next.config.mjs
    // as the package is ESM only
    // https://github.com/remarkjs/remark-gfm#install
    remarkPlugins: [],
    rehypePlugins: [],
    // If you use `MDXProvider`, uncomment the following line.
    // providerImportSource: "@mdx-js/react",
  },
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { dev, isServer }) => {
    config.plugins.push(codeInspectorPlugin({ bundler: 'webpack' }));
    return config;

    // 如果是开发模式 (dev)，处理 config.entry
    // if (dev) {
    //   // 确保 config.entry 是一个数组
    //   if (Array.isArray(config.entry)) {
    //     config.entry = config.entry.filter(
    //       (entry) => !entry.includes("webpack-dev-server")
    //     );
    //   } else if (typeof config.entry === "object") {
    //     // 如果 config.entry 是对象形式，逐个遍历它的条目
    //     Object.keys(config.entry).forEach((key) => {
    //       config.entry[key] = config.entry[key].filter(
    //         (entry) => !entry.includes("webpack-dev-server")
    //       );
    //     });
    //   }
    //   // config.plugins.push(new WindiCSSWebpackPlugin());
    // }
    // return config;
  },
  sassOptions: {
    includePaths: ['./app/styles'],
  },
  productionBrowserSourceMaps: false, // enable browser source map generation during the production build
  // Configure pageExtensions to include md and mdx
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],
  experimental: {},
  // fix all before production. Now it slow the develop speed.
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
    dirs: [
      'app',
      'bin',
      'config',
      'context',
      'hooks',
      'i18n',
      'models',
      'service',
      'test',
      'types',
      'utils',
    ],
  },
  typescript: {
    // https://nextjs.org/docs/api-reference/next.config.js/ignoring-typescript-errors
    ignoreBuildErrors: true,
  },
  reactStrictMode: true,
  async redirects() {
    return [
      {
        source: '/',
        destination: '/apps',
        permanent: false,
      },
    ];
  },
  async rewrites() {
    return [
      // 静态资源路径优先匹配
      {
        source: '/nq/static/:path*',
        destination: '/nq/static/:path*',
      },
      {
        source: '/nq/static_aios/:path*',
        destination: '/nq/static_aios/:path*',
      },
      {
        source: '/nq/assets/:path*',
        destination: '/nq/assets/:path*',
      },
      {
        source: '/nq/favicon.ico',
        destination: '/nq/favicon.ico',
      },
      // 页面路径
      {
        source: '/nq',
        destination: '/nq/index.html',
      },
      {
        source: '/nq/:path*',
        destination: '/nq/index.html',
      },
      // {
      //   source: "/api/v1/:path*",
      //   destination: "http://************:9102/api/v1/:path*",
      // },
    ];
  },
  fastRefresh: true,
  concurrentFeatures: true,
  swcMinify: true,
  output: 'standalone',
};

module.exports = withMDX(nextConfig);
