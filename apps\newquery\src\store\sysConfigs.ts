import { defineStore } from 'pinia';
import { fetchGetSysConfigs } from '@/services/console-api';

export const useSysConfigsStore = defineStore('sysConfigs', {
  state: () => ({ sysConfigsData: {} }),
  actions: {
    async fetchSysConfigs() {
      try {
        const res = await fetchGetSysConfigs();
        if (res.data) {
          this.sysConfigsData = res.data;
        }
      } catch (error) {
        console.error('获取版本信息失败:', error);
      }
    },
  },
});
