import type { FC } from 'react';
import { useState, useEffect } from 'react';

interface PPTXPreviewProps {
  data: {
    title?: string;
    fileUrl: string;
    initialPage?: number;
    hideFullscreenButton?: boolean;
  };
}

const PPTXPreviewComponent: FC<PPTXPreviewProps> = ({ data }) => {
  const [currentPage, setCurrentPage] = useState(data.initialPage || 1);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    // 模拟加载过程
    const timer = setTimeout(() => {
      setIsLoading(false);
      setTotalPages(10); // 模拟总页数
    }, 1000);

    return () => clearTimeout(timer);
  }, [data.fileUrl]);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleFullscreen = () => {
    setIsFullscreen(true);
  };

  if (isLoading) {
    return (
      <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white rounded-lg shadow-sm border">
      {/* 标题 */}
      {data.title && (
        <div className="px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-900">{data.title}</h3>
        </div>
      )}

      {/* 控制栏 */}
      <div className="flex justify-between items-center px-4 py-3 bg-gray-50 border-b">
        <div className="flex items-center gap-2">
          <button
            onClick={handlePrevPage}
            disabled={currentPage <= 1}
            className="px-3 py-1 text-sm bg-white border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ❮
          </button>
          <span className="text-sm text-gray-600">
            {currentPage} / {totalPages}
          </span>
          <button
            onClick={handleNextPage}
            disabled={currentPage >= totalPages}
            className="px-3 py-1 text-sm bg-white border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ❯
          </button>
        </div>

        {!data.hideFullscreenButton && (
          <button
            onClick={handleFullscreen}
            className="px-3 py-1 text-sm bg-white border rounded hover:bg-gray-50"
          >
            全屏
          </button>
        )}
      </div>

      {/* 预览区域 */}
      <div className="p-4">
        <div className="w-full h-96 bg-gray-100 rounded border-2 border-dashed border-gray-300 flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-2">📄</div>
            <div className="text-gray-600">PPT 预览区域</div>
            <div className="text-sm text-gray-500 mt-1">
              第 {currentPage} 页
            </div>
            <div className="text-xs text-gray-400 mt-2">
              文件: {data.fileUrl.split('/').pop()}
            </div>
          </div>
        </div>
      </div>

      {/* 全屏模态框 */}
      {isFullscreen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg w-11/12 h-5/6 flex flex-col">
            {/* 全屏控制栏 */}
            <div className="flex justify-between items-center px-4 py-3 border-b">
              <div className="flex items-center gap-2">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage <= 1}
                  className="px-3 py-1 text-sm bg-gray-100 border rounded hover:bg-gray-200 disabled:opacity-50"
                >
                  ❮
                </button>
                <span className="text-sm text-gray-600">
                  {currentPage} / {totalPages}
                </span>
                <button
                  onClick={handleNextPage}
                  disabled={currentPage >= totalPages}
                  className="px-3 py-1 text-sm bg-gray-100 border rounded hover:bg-gray-200 disabled:opacity-50"
                >
                  ❯
                </button>
              </div>

              <button
                onClick={() => setIsFullscreen(false)}
                className="px-3 py-1 text-sm bg-gray-100 border rounded hover:bg-gray-200"
              >
                关闭
              </button>
            </div>

            {/* 全屏预览区域 */}
            <div className="flex-1 p-4">
              <div className="w-full h-full bg-gray-100 rounded border-2 border-dashed border-gray-300 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">📄</div>
                  <div className="text-xl text-gray-600">PPT 全屏预览</div>
                  <div className="text-lg text-gray-500 mt-2">
                    第 {currentPage} 页
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PPTXPreviewComponent;
